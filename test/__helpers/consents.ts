import { CreateConsentDto } from '@app/consents/dto/create-consent.dto';
import { ConsentDefinitionEntity } from '@app/consents/entities/consent-definition.entity';
import { ConsentType } from '@app/constants/models/consent-config';
import { INestApplication } from '@nestjs/common';
import request from 'supertest';

export const generateGDPRConsentPayload = (): CreateConsentDto => {
  return {
    code: ConsentType.GDPR,
    version: '1.0.0',
    content: 'This is a test consent',
    shortDescription: 'This is a test consent',
    isRequired: true,
  };
};

export const createAndAssertConsent = async (
  app: INestApplication,
  payload: CreateConsentDto,
): Promise<ConsentDefinitionEntity> => {
  const response = await request(app.getHttpServer()).post('/v1/consent/create').send(payload);

  expect(response.status).toBe(201);
  expect(response.body.id).toBeDefined();
  expect(response.body.code).toBe(payload.code);
  expect(response.body.version).toBe('v1'); //TODO: why when creating consent with version 1.0.0 it returns v1?
  expect(response.body.content).toBe(payload.content);
  expect(response.body.isRequired).toBe(payload.isRequired);

  return response.body as ConsentDefinitionEntity;
};
