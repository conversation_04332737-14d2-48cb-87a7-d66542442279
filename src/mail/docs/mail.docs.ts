import {
  ApiTags,
  ApiOperation,
  ApiBody,
  ApiBadRequestResponse,
  ApiInternalServerErrorResponse,
  ApiCreatedResponse,
} from '@nestjs/swagger';
import { applyDecorators } from '@nestjs/common';
import { SendTestEmailDto } from '../dto/send-test-email.dto';
import { SendTestEmailResponseDto } from '../dto/send-test-email-response.dto';

export class MailSwaggerDocs {
  static apiTags() {
    return applyDecorators(ApiTags('Mail'));
  }

  static sendTestEmailDocs() {
    return applyDecorators(
      ApiOperation({
        summary: 'Send test welcome email',
        description:
          'Sends a test welcome email to the specified email address with personalized content. ' +
          'This endpoint is primarily used for testing email functionality and template rendering. ' +
          'The email will be sent using the configured SendGrid service with the welcome template.',
      }),
      ApiBody({
        type: SendTestEmailDto,
        description: 'Email recipient details',
        required: true,
        examples: {
          example1: {
            summary: 'Basic test email',
            description: 'Send a test email to a user',
            value: {
              email: '<EMAIL>',
              name: '<PERSON>',
            },
          },
          example2: {
            summary: 'Test email with special characters',
            description: 'Send a test email with name containing special characters',
            value: {
              email: '<EMAIL>',
              name: 'María José García-López',
            },
          },
        },
      }),
      ApiCreatedResponse({
        description: 'Test email sent successfully',
        type: SendTestEmailResponseDto,
        example: {
          message: 'Test email sent successfully',
        },
      }),
      ApiBadRequestResponse({
        description: 'Invalid input data - email format is incorrect or required fields are missing',
        example: {
          statusCode: 400,
          message: ['email must be an email', 'name should not be empty'],
          error: 'Bad Request',
        },
      }),
      ApiInternalServerErrorResponse({
        description: 'Email service error - failed to send email due to service configuration or network issues',
        example: {
          statusCode: 500,
          message: 'Failed to send email',
          error: 'Internal Server Error',
        },
      }),
    );
  }
}
