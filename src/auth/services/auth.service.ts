import { HttpException, HttpStatus, Injectable, UnauthorizedException } from '@nestjs/common';
import { UsersService } from '@app/users/services/users.service';
import { compare, hash } from 'bcryptjs';
import { authMessages } from '@app/constants/messages/auth-messages';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { UserEntity } from '@app/users/entities/user.entity';
import { ITokenPayload } from '@app/constants/interfaces/token-payload.interface';
import { InjectRepository } from '@nestjs/typeorm';
import { RefreshTokenEntity } from '@app/auth/entities/refresh-token.entity';
import { Repository } from 'typeorm';
import { Request } from 'express';
import { CustomRequest } from '@app/constants/interfaces/custom-request.interface';
import { LoginResponseDto } from '../dto/login-response.dto';

@Injectable()
export class AuthService {
  constructor(
    @InjectRepository(RefreshTokenEntity)
    private readonly refreshTokenRepository: Repository<RefreshTokenEntity>,
    private readonly usersService: UsersService,
    private readonly configService: ConfigService,
    private readonly jwtService: JwtService,
  ) {}

  async login(user: UserEntity, req?: Request): Promise<LoginResponseDto> {
    if (!user.isActive) {
      throw new HttpException(authMessages.user.notActiveAccount, HttpStatus.FORBIDDEN);
    }
    const expiresAccessToken = new Date();
    expiresAccessToken.setSeconds(
      expiresAccessToken.getSeconds() + Number(this.configService.get('JWT_ACCESS_TOKEN_EXPIRATION_S')),
    );
    const expiresRefreshToken = new Date();
    expiresRefreshToken.setSeconds(
      expiresRefreshToken.getSeconds() + Number(this.configService.get('JWT_REFRESH_TOKEN_EXPIRATION_S')),
    );
    const tokenPayload: ITokenPayload = {
      id: user.id,
      email: user.email,
      roles: user.roles,
    };
    const accessToken = this.jwtService.sign(tokenPayload, {
      secret: this.configService.getOrThrow('JWT_ACCESS_TOKEN_SECRET'),
      expiresIn: `${this.configService.getOrThrow('JWT_ACCESS_TOKEN_EXPIRATION_S')}s`,
    });

    const refreshToken = this.jwtService.sign(tokenPayload, {
      secret: this.configService.getOrThrow('JWT_REFRESH_TOKEN_SECRET'),
      expiresIn: `${this.configService.getOrThrow('JWT_REFRESH_TOKEN_EXPIRATION_S')}s`,
    });

    const ip = req?.ip ?? 'unknown';
    const device = req?.headers['user-agent'] ?? 'unknown';

    await this.refreshTokenRepository.save({
      token: await hash(refreshToken, 10),
      user,
      ip,
      device,
      expiresAt: expiresRefreshToken,
    });

    //TODO: I think we should return only accessToken and refreshToken
    //TODO: why we need to return expiresIn and user?
    return {
      accessToken,
      refreshToken,
      expiresIn: {
        accessToken: expiresAccessToken,
        refreshToken: expiresRefreshToken,
      },
      user: {
        id: user.id,
        email: user.email,
        roles: user.roles,
        name: user.name,
      },
    };
  }

  async refreshToken(user: UserEntity, oldRefreshToken: string, req?: Request | CustomRequest) {
    const tokens = await this.refreshTokenRepository.find({
      where: { user: { id: user.id } },
    });

    for (const dbToken of tokens) {
      const isMatch = await compare(oldRefreshToken, dbToken.token);
      if (isMatch) {
        await this.refreshTokenRepository.delete(dbToken.id);
        return this.login(user, req as Request);
      }
    }

    throw new UnauthorizedException('Invalid refresh token');
  }

  async verifyUser(email: string, password: string) {
    try {
      const user = await this.usersService.getUserByEmail(email);
      if (!user) {
        return false;
      }
      const authenticated = await compare(password, user.password);
      if (!authenticated) {
        throw new UnauthorizedException();
      }
      return user;
    } catch {
      throw new UnauthorizedException(authMessages.auth.invalidCredentials);
    }
  }

  async verifyUserRefreshToken(refreshToken: string, userId: string): Promise<UserEntity> {
    const user = await this.usersService.getUserById(userId);
    if (!user) throw new UnauthorizedException('User not found');

    const tokens = await this.refreshTokenRepository.find({
      where: { user: { id: userId } },
      relations: ['user'],
    });

    for (const dbToken of tokens) {
      const isValid = await compare(refreshToken, dbToken.token);
      if (isValid) {
        return user;
      }
    }

    throw new UnauthorizedException('Invalid refresh token');
  }

  async logout(user: UserEntity, refreshToken: string): Promise<{ message: string }> {
    const tokens = await this.refreshTokenRepository.find({
      where: { user: { id: user.id } },
    });
    let matchedToken: RefreshTokenEntity | null = null;

    for (const dbToken of tokens) {
      const isMatch = await compare(refreshToken, dbToken.token);
      if (isMatch) {
        matchedToken = dbToken;
        break;
      }
    }
    if (!matchedToken) {
      throw new UnauthorizedException('Invalid or already used refresh token');
    }
    await this.refreshTokenRepository.delete(matchedToken.id);
    return { message: 'Successfully logged out.' };
  }

  // TODO: What is the purpose of this method?
  async logoutAll(user: UserEntity): Promise<{ message: string }> {
    const result = await this.refreshTokenRepository.delete({
      user: { id: user.id },
    });

    if (result.affected && result.affected > 0) {
      return { message: `Logged out from ${result.affected} session(s).` };
    }

    return { message: 'No active sessions found to logout.' };
  }

  async getActiveSessions(user: UserEntity): Promise<Partial<RefreshTokenEntity>[]> {
    const tokens = await this.refreshTokenRepository.find({
      where: { user: { id: user.id } },
      order: { createdAt: 'DESC' },
    });

    return tokens.map(({ id, ip, device, createdAt, expiresAt }) => ({
      id,
      ip,
      device,
      createdAt,
      expiresAt,
    }));
  }
}
