import React from "react";
import { View, Image, StyleSheet } from "react-native";
import {
  Typography,
  useTheme,
  ProgressBar,
  BorderBoxWithGradient,
} from "@vs/kit-ui-expo";
import { useTranslation } from "react-i18next";
import Badges from "@/components/Badges";

export const WorkoutCard = () => {
  const { theme } = useTheme();
  const { t } = useTranslation();

  const renderHeader = () => (
    <Typography
      variant="Heading5"
      color={theme.colors.grey[50]}
      style={{ marginBottom: theme.spacing[2] }}
    >
      {t("user_dashboard_exercise")}
    </Typography>
  );

  const renderBadges = () => (
    <Badges
      labels={["15min", "Łatwy"]}
      align="right"
      badgeColor={theme.colors.accent[400]}
      textColor={theme.colors.grey[50]}
    />
  );

  const renderImage = () => (
    <Image
      source={require("@/assets/images/exercise-photo.png")}
      style={styles.image}
      resizeMode="cover"
    />
  );

  const renderProgress = () => (
    <ProgressBar
      progress={0.6}
      label="60%"
      gradientColors={["#f69109", "#beab26", "#5e8633"]}
      containerStyle={{ marginVertical: 8 }}
    />
  );

  const renderContent = () => (
    <View style={styles.textContent}>
      <Typography
        variant="Heading5"
        color={theme.colors.grey[50]}
        style={{ marginBottom: 8 }}
      >
        {t("user_dashboard_exercise_title")}
      </Typography>
      {renderProgress()}
    </View>
  );

  const renderCardBody = () => (
    <View style={styles.rowLayout}>
      {renderImage()}
      {renderContent()}
    </View>
  );

  const renderCard = () => (
    <BorderBoxWithGradient
      style={{
        borderColor: theme.colors.accent[400],
      }}
      gradientColors={["#354720", "#28351a", "#1b2214"]}
    >
      {renderBadges()}
      {renderCardBody()}
    </BorderBoxWithGradient>
  );

  return (
    <>
      {renderHeader()}
      {renderCard()}
    </>
  );
};

const styles = StyleSheet.create({
  rowLayout: {
    flexDirection: "row",
    gap: 16,
    marginBottom: 16,
  },
  textContent: {
    flex: 1,
    justifyContent: "center",
  },
  image: {
    width: 110,
    height: 110,
    borderRadius: 8,
  },
});
