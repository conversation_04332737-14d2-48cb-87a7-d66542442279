import React from "react";
import { View, StyleSheet } from "react-native";
import { Theme, Typography, useTheme } from "@vs/kit-ui-expo";

export default function Diet() {
  const { theme } = useTheme();
  const fnStyles = styles(theme);

  return (
    <View style={fnStyles.container}>
      <Typography variant="Heading3" color={theme.colors.grey[50]}>
        USER Diet
      </Typography>
    </View>
  );
}

const styles = (theme: Theme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      padding: 24,
      backgroundColor: theme.colors.grey[900],
    },
  });
