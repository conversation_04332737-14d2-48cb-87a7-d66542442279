import 'reflect-metadata';
import { INestApplication } from '@nestjs/common';
import request from 'supertest';

import { loginAndAssertUser, registerAndAssertUser } from '../__helpers/auth';
import { buildTestApp } from '../__setup/app.factory';
import { generateTestUser } from '../__helpers/user';
import { CreateTrainingPlanRequestDto } from '@app/training-plan/dto/create-training-plan.dto';
import { CreateTrainingRequestDto } from '@app/training/dto/create-training.dto';
import { createAndAssertTraining } from '../__helpers/training';
import { randomUUID } from 'crypto';
import { createAndAssertConsent, generateGDPRConsentPayload } from '../__helpers/consents';
import { ConsentDefinitionEntity } from '@app/consents/entities/consent-definition.entity';

describe('/POST /v1/training-plan', () => {
  let app: INestApplication;
  let accessToken: string;
  let consent: ConsentDefinitionEntity;

  beforeAll(async () => {
    app = await buildTestApp();

    const consentPayload = generateGDPRConsentPayload();
    consent = await createAndAssertConsent(app, consentPayload);

    const testUser = generateTestUser([consent.id]);
    await registerAndAssertUser(app, testUser);
    accessToken = (await loginAndAssertUser(app, testUser)).accessToken;
  });

  afterAll(async () => {
    await app.close();
  });

  it('should create a training plan without trainings', async () => {
    const payload: CreateTrainingPlanRequestDto = {
      name: 'Test Exercise E2E',
      description: 'This is a test exercise',
      trainings: [],
    };

    const response = await request(app.getHttpServer())
      .post('/v1/training-plan')
      .set('Authorization', `Bearer ${accessToken}`)
      .send(payload);

    expect(response.status).toBe(201);
    expect(response.body.name).toBe(payload.name);
    expect(response.body.description).toBe(payload.description);
    expect(response.body.trainings).toEqual(payload.trainings);
    expect(response.body.status).toBe('active');
    expect(response.body.createdAt).toBeDefined();
    expect(response.body.updatedAt).toBeDefined();
    expect(response.body.creatorId).toBeDefined();
  });

  it('should create a training plan with trainings', async () => {
    const trainingPayload: CreateTrainingRequestDto = {
      name: 'Test training name',
      description: 'Test training description',
      exercises: [],
      tags: '',
    };
    const training = await createAndAssertTraining(app, trainingPayload, accessToken);

    const trainingPlanPayload: CreateTrainingPlanRequestDto = {
      name: 'Test Exercise E2E',
      description: 'This is a test exercise',
      trainings: [training.id],
    };

    const response = await request(app.getHttpServer())
      .post('/v1/training-plan')
      .set('Authorization', `Bearer ${accessToken}`)
      .send(trainingPlanPayload);

    expect(response.status).toBe(201);
    expect(response.body.name).toBe(trainingPlanPayload.name);
    expect(response.body.description).toBe(trainingPlanPayload.description);
    expect(response.body.trainings.length).toBe(trainingPlanPayload.trainings.length);
    expect(response.body.status).toBe('active');
    expect(response.body.createdAt).toBeDefined();
    expect(response.body.updatedAt).toBeDefined();
    expect(response.body.creatorId).toBeDefined();
  });

  it('should return 400 if creating training plan with non-existing trainings', async () => {
    const randomID = randomUUID();
    const trainingPlanPayload: CreateTrainingPlanRequestDto = {
      name: 'Test Exercise E2E',
      description: 'This is a test exercise',
      trainings: [randomID],
    };

    const response = await request(app.getHttpServer())
      .post('/v1/training-plan')
      .set('Authorization', `Bearer ${accessToken}`)
      .send(trainingPlanPayload);

    expect(response.status).toBe(400);
    expect(response.body.message).toBe('One or more trainings do not exist or do not belong to you');
  });

  it('should return 400 if training does not belong to user creating training plan', async () => {
    const anotherUser = generateTestUser([consent.id]);
    await registerAndAssertUser(app, anotherUser);
    const anotherUserAccessToken = (await loginAndAssertUser(app, anotherUser)).accessToken;

    const trainingPayload: CreateTrainingRequestDto = {
      name: 'Test training name',
      description: 'Test training description',
      exercises: [],
      tags: '',
    };
    const training = await createAndAssertTraining(app, trainingPayload, anotherUserAccessToken);

    const trainingPlanPayload: CreateTrainingPlanRequestDto = {
      name: 'Test Exercise E2E',
      description: 'This is a test exercise',
      trainings: [training.id],
    };

    const response = await request(app.getHttpServer())
      .post('/v1/training-plan')
      .set('Authorization', `Bearer ${accessToken}`)
      .send(trainingPlanPayload);

    expect(response.status).toBe(400);
    expect(response.body.message).toBe('One or more trainings do not exist or do not belong to you');
  });

  it('should return 401 if user is not authenticated', async () => {
    const trainingPlanPayload: CreateTrainingPlanRequestDto = {
      name: 'Test Exercise E2E',
      description: 'This is a test exercise',
      trainings: [],
    };

    const response = await request(app.getHttpServer()).post('/v1/training-plan').send(trainingPlanPayload);

    expect(response.status).toBe(401);
    expect(response.body.message).toBe('Unauthorized');
  });

  it('should return 400 if training plan name is empty', async () => {
    const trainingPlanPayload: CreateTrainingPlanRequestDto = {
      name: '',
      description: 'This is a test exercise',
      trainings: [],
    };

    const response = await request(app.getHttpServer())
      .post('/v1/training-plan')
      .set('Authorization', `Bearer ${accessToken}`)
      .send(trainingPlanPayload);

    expect(response.status).toBe(400);
    expect(response.body.message).toEqual([
      'name should not be empty',
      'name must be longer than or equal to 3 characters',
    ]);
  });

  it('should return 400 if training plan description is empty', async () => {
    const trainingPlanPayload: CreateTrainingPlanRequestDto = {
      name: 'Test Exercise E2E',
      description: '',
      trainings: [],
    };

    const response = await request(app.getHttpServer())
      .post('/v1/training-plan')
      .set('Authorization', `Bearer ${accessToken}`)
      .send(trainingPlanPayload);

    expect(response.status).toBe(400);
    expect(response.body.message).toEqual([
      'description must be longer than or equal to 3 characters',
      'description should not be empty',
    ]);
  });
});
