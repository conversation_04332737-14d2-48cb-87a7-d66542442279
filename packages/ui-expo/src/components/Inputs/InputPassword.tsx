import React, { useState } from "react";
import { View, TextInput, Text, StyleSheet, StyleProp, ViewStyle, TextStyle, TouchableOpacity } from "react-native";
import { Sizes, buttonSizeStyles } from "../../utils/sizes";
import { useTheme } from "../../theme";
import { SecurityEyeIcon, SecurityEyeClosedIcon } from "@vs/kit-ui-assets";

interface Props {
  value: string;
  onChangeText: (text: string) => void;
  placeholder?: string;
  label?: string;
  size?: Sizes;
  disabled?: boolean;
  inputStyle?: StyleProp<TextStyle>;
  labelStyle?: StyleProp<TextStyle>;
  onBlur?: () => void;
  error?: string;
}

export const InputPassword = ({
  value,
  onChangeText,
  placeholder = "Password...",
  label,
  size = Sizes.Medium,
  disabled,
  inputStyle,
  labelStyle,
  onBlur,
  error,
}: Props) => {
  const { theme } = useTheme();
  const sizeStyle = buttonSizeStyles[size];
  const [isFocused, setIsFocused] = useState(false);
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);

  const togglePasswordVisibility = () => {
    setIsPasswordVisible(!isPasswordVisible);
  };

  return (
    <View style={styles.container}>
      {label && (
        <Text
          style={[
            styles.label,
            {
              color: error ? theme.colors.alert[500] : theme.colors.grey[50],
              fontWeight: theme.typography.BodyS.fontWeight as TextStyle["fontWeight"],
              fontSize: theme.typography.BodyS.fontSize,
              lineHeight: theme.typography.BodyS.lineHeight,
            },
            labelStyle,
          ]}
        >
          {label}
        </Text>
      )}
      <View style={styles.inputContainer}>
        <TextInput
          style={[
            styles.input,
            sizeStyle,
            {
              borderColor: error
                ? theme.colors.alert[500]
                : isFocused
                  ? theme.colors.accent[400]
                  : theme.colors.grey[500],
              color: theme.colors.grey[50],
              borderRadius: theme.borderRadius.lg,
              fontWeight: theme.typography.BodyS.fontWeight as TextStyle["fontWeight"],
              fontSize: theme.typography.BodyS.fontSize,
              lineHeight: theme.typography.BodyS.lineHeight,
            },
            disabled && { opacity: 0.7 },
            inputStyle,
          ]}
          value={value}
          onChangeText={onChangeText}
          placeholder={placeholder}
          placeholderTextColor={theme.colors.grey[50]}
          onFocus={() => setIsFocused(true)}
          onBlur={() => {
            setIsFocused(false);
            onBlur?.();
          }}
          editable={!disabled}
          secureTextEntry={!isPasswordVisible}
        />
        <TouchableOpacity style={styles.iconContainer} onPress={togglePasswordVisibility} disabled={disabled}>
          {isPasswordVisible ? (
            <SecurityEyeIcon width={24} height={24} fill={disabled ? theme.colors.grey[500] : theme.colors.grey[50]} />
          ) : (
            <SecurityEyeClosedIcon
              width={24}
              height={24}
              fill={disabled ? theme.colors.grey[500] : theme.colors.grey[50]}
            />
          )}
        </TouchableOpacity>
      </View>
      {error && <Text style={[styles.errorLabel, { color: theme.colors.alert[500] }]}>{error}</Text>}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: "100%",
  },
  label: {
    marginBottom: 8,
  },
  inputContainer: {
    position: "relative",
    width: "100%",
    flexDirection: "row",
    alignItems: "center",
  },
  input: {
    flex: 1,
    paddingHorizontal: 24,
    fontSize: 16,
    borderWidth: 1,
    paddingRight: 56, // Make room for the icon
  },
  iconContainer: {
    position: "absolute",
    right: 16,
    height: "100%",
    justifyContent: "center",
    alignItems: "center",
  },
  errorLabel: {
    marginTop: 4,
    fontSize: 12,
    lineHeight: 16,
  },
});
