import { View, Image, TouchableOpacity } from "react-native";
import {
  NavigationArrowBackBrokeIcon,
  NavigationHamburgerIcon,
} from "@vs/kit-ui-assets";
import { Theme, useTheme } from "@vs/kit-ui-expo";
import { StyleSheet } from "react-native";
import { useRouter } from "expo-router";

export const NavigationTopBarDashboardWithBack = () => {
  const { theme } = useTheme();
  const styles = stylesConfig(theme);
  const router = useRouter();

  const handleBack = () => {
    router.back();
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity onPress={handleBack} hitSlop={32}>
        <NavigationArrowBackBrokeIcon
          fill={"transparent"}
          stroke={theme.colors.grey[50]}
          width={24}
          height={24}
        />
      </TouchableOpacity>
      <Image
        source={require("../../assets/images/logo.png")}
        style={styles.logo}
        resizeMode="contain"
      />
      <NavigationHamburgerIcon fill={theme.colors.accent[300]} />
    </View>
  );
};

const stylesConfig = (theme: Theme) =>
  StyleSheet.create({
    container: {
      height: 64,
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      paddingHorizontal: theme.spacing[6],
      backgroundColor: theme.colors.grey[900],
    },
    logo: {
      width: 64,
      height: 64,
    },
  });
