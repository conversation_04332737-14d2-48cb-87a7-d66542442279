import React from "react";
import { View, StyleSheet } from "react-native";
import {
  ButtonBasicIcon,
  LoaderSpinnerPage,
  Sizes,
  SwitchWithLabel,
  Theme,
  Typography,
  useTheme,
} from "@vs/kit-ui-expo";
import { ActionsAddCircleIcon } from "@vs/kit-ui-assets";
import { useRouter } from "expo-router";
import { PathEnum } from "@/enums/path";
import { useTranslation } from "react-i18next";
import { useExerciseListQuery } from "@/hooks/useExerciseList";
import { SingleActivityListCard } from "@/components/Cards";
import FlatListWithSpacing from "@/components/ScrollableWithSpacing/FlatListWithSpacing";

export default function ExerciseList() {
  const { theme } = useTheme();
  const fnStyles = styles(theme);
  const { t } = useTranslation();
  const router = useRouter();
  const { exerciseList, loading } = useExerciseListQuery();

  const renderHeader = () => {
    return (
      <View style={fnStyles.header}>
        <ButtonBasicIcon
          size={Sizes.Small}
          rightIcon={
            <ActionsAddCircleIcon
              stroke={theme.colors.grey[900]}
              fill="transparent"
            />
          }
          style={{
            width: 200,
          }}
          onPress={() => {
            router.push(PathEnum.EXERCISE_NEW);
          }}
        >
          {t("activity_exercise_list_add_new")}
        </ButtonBasicIcon>
        <View style={{ height: 40 }}>
          <SwitchWithLabel
            style={{
              flex: 1,
            }}
            labelLeft={t("activity_exercise_list_switch_trainer")}
            labelRight={t("activity_exercise_list_switch_vitaminstation")}
            onValueChange={() => {}}
            value={false}
          />
        </View>
      </View>
    );
  };

  const renderExcerciseList = () => {
    return (
      <FlatListWithSpacing
        data={exerciseList}
        numColumns={2}
        contentContainerStyle={{
          gap: theme.spacing[4],
        }}
        columnWrapperStyle={{
          justifyContent: "space-between",
          columnGap: theme.spacing[4],
        }}
        style={{
          flex: 1,
        }}
        keyExtractor={(_, index) => `excercise-item-${index}`}
        ListEmptyComponent={() => (
          <Typography
            variant="BodyM"
            color={theme.colors.grey[50]}
            style={{ textAlign: "center" }}
          >
            {t("activity_exercise_list_empty")}
          </Typography>
        )}
        renderItem={({ item }) => (
          <SingleActivityListCard
            title={item.name}
            description={item.description}
            status={item.status}
            tags={item.tags.split(",")}
            onPress={() =>
              router.push(
                PathEnum.EXERCISE_NEW.replace("[id]", `${item.id}`) as any
              )
            }
          />
        )}
      />
    );
  };

  if (loading) return <LoaderSpinnerPage />;
  return (
    <View style={fnStyles.container}>
      {renderHeader()}
      {renderExcerciseList()}
    </View>
  );
}

const styles = (theme: Theme) =>
  StyleSheet.create({
    container: {
      padding: theme.spacing[4],
      flex: 1,
      gap: theme.spacing[4],
      backgroundColor: theme.colors.grey[900],
    },
    header: {
      gap: theme.spacing[4],
      borderColor: "white",
      flexDirection: "column",
      alignItems: "center",
      justifyContent: "space-between",
    },
  });
