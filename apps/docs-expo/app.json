{"expo": {"name": "native", "slug": "native", "version": "1.0.0", "scheme": "com.turbo.example", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "updates": {"fallbackToCacheTimeout": 0}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#FFFFFF"}, "package": "com.turbo.example"}, "web": {"favicon": "./assets/favicon.png", "bundler": "metro"}, "plugins": ["expo-router"]}}