import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TrainingPlanController } from './controllers/training-plan.controller';
import { TrainingPlanService } from './services/training-plan.service';
import { TrainingPlanEntity } from './entities/training-plan.entity';
import { TrainingPlanGroupEntity } from './entities/training-plan-group.entity';
import { TrainingPlanTrainingEntity } from './entities/training-plan-training.entity';
import { AuthModule } from '@app/auth/auth.module';
import { TrainingModule } from '@app/training/training.module';
import { GroupsModule } from '@app/groups/groups.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([TrainingPlanEntity, TrainingPlanGroupEntity, TrainingPlanTrainingEntity]),
    AuthModule,
    TrainingModule,
    GroupsModule,
  ],
  controllers: [TrainingPlanController],
  providers: [TrainingPlanService],
  exports: [TrainingPlanService],
})
export class TrainingPlanModule {}
