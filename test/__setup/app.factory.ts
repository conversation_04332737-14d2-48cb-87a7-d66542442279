import { INestApplication, ValidationPipe, VersioningType, ClassSerializerInterceptor } from '@nestjs/common';
import { Test } from '@nestjs/testing';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Reflector } from '@nestjs/core';

import { ExercisesModule } from '@app/exercises/exercises.module';
import { UsersModule } from '@app/users/users.module';
import { AuthModule } from '@app/auth/auth.module';
import { ConsentsModule } from '@app/consents/consents.module';

import { ExerciseEntity } from '@app/exercises/entities/exercise.entity';

import { UserEntity } from '@app/users/entities/user.entity';
import { RefreshTokenEntity } from '@app/auth/entities/refresh-token.entity';
import { ConsentDefinitionEntity } from '@app/consents/entities/consent-definition.entity';
import { UserConsentEntity } from '@app/consents/entities/user-consent.entity';
import { dbConfig } from './db.container';
import { TrainingModule } from '@app/training/training.module';
import { TrainingEntity } from '@app/training/entities/training.entity';
import { TrainingPlanModule } from '@app/training-plan/training-plan.module';
import { TrainingPlanEntity } from '@app/training-plan/entities/training-plan.entity';
import { TrainingPlanGroupEntity } from '@app/training-plan/entities/training-plan-group.entity';
import { GroupsModule } from '@app/groups/groups.module';
import { GroupEntity } from '@app/groups/entities/group.entity';
import { GroupUserEntity } from '@app/groups/entities/group-user.entity';
import { UserOtcEntity } from '@app/users/entities/user-otc.entity';
import { MailService } from '@app/mail/services/mail.service';
import { TrainingExerciseEntity } from '@app/training/entities/training-exercise.entity';
import { TrainingPlanTrainingEntity } from '@app/training-plan/entities/training-plan-training.entity';

export const buildTestApp = async (): Promise<INestApplication> => {
  const moduleRef = await Test.createTestingModule({
    imports: [
      ConfigModule.forRoot({ isGlobal: true }),
      JwtModule.register({ secret: 'test-secret', signOptions: { expiresIn: '60s' } }),
      TypeOrmModule.forRoot({
        type: dbConfig().type,
        host: dbConfig().host,
        port: dbConfig().port,
        username: dbConfig().username,
        password: dbConfig().password,
        database: dbConfig().database,
        dropSchema: true,
        synchronize: true,
        entities: [
          ExerciseEntity,
          UserEntity,
          RefreshTokenEntity,
          ConsentDefinitionEntity,
          UserConsentEntity,
          TrainingExerciseEntity,
          TrainingEntity,
          TrainingPlanEntity,
          TrainingPlanTrainingEntity,
          TrainingPlanGroupEntity,
          GroupEntity,
          GroupUserEntity,
          UserOtcEntity,
        ],
      }),
      TypeOrmModule.forFeature([
        ExerciseEntity,
        UserEntity,
        RefreshTokenEntity,
        ConsentDefinitionEntity,
        UserConsentEntity,
        TrainingExerciseEntity,
        TrainingEntity,
        TrainingPlanEntity,
        TrainingPlanTrainingEntity,
        TrainingPlanGroupEntity,
        GroupEntity,
        GroupUserEntity,
        UserOtcEntity,
      ]),
      ExercisesModule,
      UsersModule,
      AuthModule,
      ConsentsModule,
      TrainingModule,
      TrainingPlanModule,
      GroupsModule,
    ],
  })
    .overrideProvider(MailService)
    .useValue({
      sendOtc: jest.fn().mockResolvedValue(undefined),
    })
    .compile();

  const app = moduleRef.createNestApplication();
  app.useGlobalPipes(new ValidationPipe({ whitelist: true }));
  app.useGlobalInterceptors(new ClassSerializerInterceptor(app.get(Reflector)));
  app.enableVersioning({ type: VersioningType.URI, prefix: 'v' });
  await app.init();

  return app;
};
