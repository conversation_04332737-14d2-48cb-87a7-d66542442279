import {
  ApiTags,
  ApiOperation,
  ApiBearerAuth,
  ApiOkResponse,
  ApiUnauthorizedResponse,
  ApiCreatedResponse,
  ApiBadRequestResponse,
  ApiUnprocessableEntityResponse,
} from '@nestjs/swagger';
import { applyDecorators } from '@nestjs/common';
import { ConsentDefinitionEntity } from '@app/consents/entities/consent-definition.entity';
import { UserConsentEntity } from '@app/consents/entities/user-consent.entity';

export class ConsentsSwaggerDocs {
  static apiTags() {
    return applyDecorators(ApiTags('consents'));
  }

  static createConsentDocs() {
    return applyDecorators(
      ApiOperation({
        summary: 'Create a new consent definition',
        description:
          'Creates a new consent definition with a specific code, content, and optional short description for button display.',
      }),
      ApiCreatedResponse({
        description: 'Consent definition successfully created',
        type: ConsentDefinitionEntity,
      }),
      ApiBadRequestResponse({
        description: 'Invalid input data',
      }),
      ApiUnprocessableEntityResponse({
        description: 'Consent definition already exists',
      }),
    );
  }

  static getAllLatestConsentsDocs() {
    return applyDecorators(
      ApiOperation({
        summary: 'Get all latest consents',
        description: 'Retrieves the latest versions of all consents.',
      }),
      ApiOkResponse({
        description: 'List of latest consents retrieved successfully',
        type: [ConsentDefinitionEntity],
      }),
    );
  }

  static acceptConsentDocs() {
    return applyDecorators(
      ApiOperation({
        summary: 'Accept user consents',
        description: 'Accepts multiple consents for a specific user based on provided consent IDs.',
      }),
      ApiCreatedResponse({
        description: 'User consents successfully accepted',
        type: [UserConsentEntity],
      }),
      ApiBadRequestResponse({
        description: 'Invalid input data',
      }),
    );
  }

  static getUserConsentsDocs() {
    return applyDecorators(
      ApiBearerAuth(),
      ApiOperation({
        summary: 'Get user consents',
        description: 'Retrieves the list of consents accepted by the authenticated user.',
      }),
      ApiOkResponse({
        description: 'List of user consents retrieved successfully',
        type: [UserConsentEntity],
      }),
      ApiUnauthorizedResponse({
        description: 'Unauthorized access - valid JWT token required',
      }),
    );
  }

  static hasAcceptedAllDocs() {
    return applyDecorators(
      ApiOperation({
        summary: 'Check if user has accepted all required consents',
        description: 'Checks whether a user has accepted all consents that are marked as required.',
      }),
      ApiOkResponse({
        description: 'Boolean value indicating acceptance status',
        type: Boolean,
      }),
      ApiBadRequestResponse({
        description: 'Invalid user ID format',
      }),
    );
  }

  static getRequiredConsentsDocs() {
    return applyDecorators(
      ApiOperation({
        summary: 'Get required consents',
        description: 'Retrieves all consents that are marked as required.',
      }),
      ApiOkResponse({
        description: 'List of required consents retrieved successfully',
        type: [ConsentDefinitionEntity],
      }),
    );
  }
}
