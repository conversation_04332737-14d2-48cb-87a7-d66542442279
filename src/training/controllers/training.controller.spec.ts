import { Test, TestingModule } from '@nestjs/testing';
import { Training<PERSON>ontroller } from './training.controller';
import { TrainingService } from '../services/training.service';
import { CreateTrainingRequestDto } from '../dto/create-training.dto';
import { UpdateTrainingRequestDto } from '../dto/update-training.dto';
import { TrainingStatus } from '../enums/training-status.enum';
import { UserEntity } from '../../users/entities/user.entity';
import { TrainingEntity } from '../entities/training.entity';

describe('TrainingController', () => {
  let controller: TrainingController;
  let service: TrainingService;

  const mockUser = {
    id: 'user-id',
    email: '<EMAIL>',
    name: 'Test User',
    password: 'hashedPassword',
    consents: ['RODO'],
    roles: ['trainer'],
    createdAt: new Date(),
  } as unknown as UserEntity;

  const mockExercises = [
    { id: 'exercise_id_1', name: 'Exercise 1', creatorId: 'user-id' },
    { id: 'exercise_id_2', name: 'Exercise 2', creatorId: 'user-id' },
  ] as any[];

  const mockTrainingResponse: TrainingEntity = {
    id: 'training_id',
    name: 'Trening na biceps',
    description: 'Trening opis super ciało w 2godziny',
    exercises: mockExercises,
    status: TrainingStatus.ACTIVE,
    creator: mockUser,
    creatorId: 'user-id',
    tags: 'na_cale_cialo|i_na_nogi',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockTrainingService = {
    createTraining: jest.fn(),
    findAll: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [TrainingController],
      providers: [
        {
          provide: TrainingService,
          useValue: mockTrainingService,
        },
      ],
    }).compile();

    controller = module.get<TrainingController>(TrainingController);
    service = module.get<TrainingService>(TrainingService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create', () => {
    it('should create a training', async () => {
      const createTrainingDto: CreateTrainingRequestDto = {
        name: 'Trening na biceps',
        description: 'Trening opis super ciało w 2godziny',
        exercises: ['exercise_id_1', 'exercise_id_2'],
      };

      mockTrainingService.createTraining.mockResolvedValue(mockTrainingResponse);

      const result = await controller.create(createTrainingDto, mockUser);

      expect(service.createTraining).toHaveBeenCalledWith(createTrainingDto, mockUser);
      expect(result).toEqual(mockTrainingResponse);
    });
  });

  describe('findAll', () => {
    it('should return all trainings', async () => {
      mockTrainingService.findAll.mockResolvedValue([mockTrainingResponse]);

      const result = await controller.findAll(TrainingStatus.ACTIVE, mockUser);

      expect(service.findAll).toHaveBeenCalledWith(mockUser, TrainingStatus.ACTIVE);
      expect(result).toEqual([mockTrainingResponse]);
    });
  });

  describe('findOne', () => {
    it('should return a training by id', async () => {
      mockTrainingService.findOne.mockResolvedValue(mockTrainingResponse);

      const result = await controller.findOne('training_id', mockUser);

      expect(service.findOne).toHaveBeenCalledWith('training_id', mockUser);
      expect(result).toEqual(mockTrainingResponse);
    });
  });

  describe('update', () => {
    it('should update a training', async () => {
      const updateTrainingDto: UpdateTrainingRequestDto = {
        name: 'Updated Training Name',
      };

      mockTrainingService.update.mockResolvedValue({
        ...mockTrainingResponse,
        name: 'Updated Training Name',
      });

      const result = await controller.update('training_id', updateTrainingDto, mockUser);

      expect(service.update).toHaveBeenCalledWith('training_id', updateTrainingDto, mockUser);
      expect(result.name).toBe('Updated Training Name');
    });
  });

  describe('remove', () => {
    it('should remove a training', async () => {
      mockTrainingService.remove.mockResolvedValue({ status: TrainingStatus.DELETED });

      const result = await controller.remove('training_id', mockUser);

      expect(service.remove).toHaveBeenCalledWith('training_id', mockUser);
      expect(result).toEqual({ status: TrainingStatus.DELETED });
    });
  });
});
