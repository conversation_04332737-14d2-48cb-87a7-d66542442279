import { View, Text, Image, ImageStyle, StyleSheet } from "react-native";

interface Props {
  title: string;
  description: string;
  image: any;
  imageSize?: { width: number; height: number };
  screenNumber?: number;
}

export const ContentSection = ({
  title,
  description,
  image,
  imageSize,
  screenNumber,
}: Props) => {
  const getImageStyle = (): ImageStyle => ({
    width: imageSize?.width ?? 150,
    height: imageSize?.height ?? 180,
    resizeMode: "contain",
    maxWidth: "100%",
    marginBottom: screenNumber === 2 ? 20 : 40,
  });

  return (
    <View style={styles.contentContainer}>
      <Image source={image} style={getImageStyle()} />
      <Text style={styles.title}>{title}</Text>
      <Text style={styles.description}>{description}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  contentContainer: {
    flex: 1,
    width: "100%",
    maxWidth: 400,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 24,
    marginLeft: "auto",
    marginRight: "auto",
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#fff",
    textAlign: "center",
    marginBottom: 24,
    width: "100%",
  },
  description: {
    fontSize: 18,
    color: "#ccc",
    textAlign: "center",
    marginBottom: 64,
    width: "100%",
  },
});
