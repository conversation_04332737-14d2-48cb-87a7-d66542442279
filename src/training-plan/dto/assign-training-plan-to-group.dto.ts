import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsNotEmpty } from 'class-validator';

export class AssignTrainingPlanToGroupDto {
  @ApiProperty({
    description: 'Start date of the training plan for the group (format YYYY-MM-DD)',
    example: '2025-05-06',
  })
  @IsNotEmpty({ message: 'Start date is required' })
  @IsDateString({}, { message: 'Start date must be a valid date in format YYYY-MM-DD' })
  startDate: string;
}
