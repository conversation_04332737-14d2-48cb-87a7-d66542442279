{"name": "@vs/kit-ui-next", "version": "0.2.0", "license": "MIT", "sideEffects": ["**/*.css"], "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./*": {"types": "./src/*.ts", "import": "./dist/*.mjs", "require": "./dist/*.js"}, "./*.css": {"import": "./dist/*.css", "require": "./dist/*.css"}}, "scripts": {"build": "tsup", "dev": "tsup --watch", "lint": "eslint . --max-warnings 0", "clean": "rm -rf .turbo node_modules dist"}, "devDependencies": {"@types/react": "^18.2.61", "@types/react-dom": "^18.2.19", "@vs/kit-eslint-config": "*", "@vs/kit-tailwind-config": "*", "@vs/kit-typescript-config": "*", "eslint": "^8.57.0", "tsup": "^8.0.2", "typescript": "5.5.4"}, "dependencies": {"@svgr/webpack": "^8.1.0", "clsx": "^2.1.1", "next": "15.0.3", "react": "^18.2.0", "react-dom": "^18.2.0"}, "publishConfig": {"access": "public"}}