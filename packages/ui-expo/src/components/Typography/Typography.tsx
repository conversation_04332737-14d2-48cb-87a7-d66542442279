import React from "react";
import { Text, StyleSheet, TextStyle } from "react-native";
import { Theme } from "../../theme/theme";
import { useTheme } from "../../theme/ThemeProvider";

export type TypographyVariant = keyof Theme["typography"];

type TypographyStyle = {
  fontSize: number;
  lineHeight: number;
  fontWeight: TextStyle["fontWeight"];
};

export interface Props {
  variant: TypographyVariant;
  children: React.ReactNode;
  style?: TextStyle;
  color?: string;
  accessibilityLabel?: string;
}

export const Typography = React.memo(({ variant, children, style, color, accessibilityLabel }: Props) => {
  const { theme } = useTheme();
  const variantStyle = theme.typography[variant] as TypographyStyle;

  return (
    <Text
      style={[styles.base, variantStyle, color ? { color } : undefined, style]}
      accessibilityLabel={accessibilityLabel}
    >
      {children}
    </Text>
  );
});

Typography.displayName = "Typography";

const styles = StyleSheet.create({
  base: {
    fontFamily: "Outfit",
    color: "#000000",
  },
});
