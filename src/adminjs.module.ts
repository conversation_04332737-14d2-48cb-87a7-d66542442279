import { RefreshTokenEntity } from './auth/entities/refresh-token.entity';
import { INestApplication } from '@nestjs/common';
import { UserEntity } from './users/entities/user.entity';
import { ConsentDefinitionEntity } from './consents/entities/consent-definition.entity';
import { TrainingPlanGroupEntity } from './training-plan/entities/training-plan-group.entity';
import { TrainingPlanEntity } from './training-plan/entities/training-plan.entity';
import { GroupEntity } from './groups/entities/group.entity';
import { UserConsentEntity } from './consents/entities/user-consent.entity';
import { ExerciseEntity } from './exercises/entities/exercise.entity';
import { TrainingEntity } from './training/entities/training.entity';

const DEFAULT_ADMIN = {
  email: '<EMAIL>',
  password: 'admin',
};

const authenticate = async (email: string, password: string) => {
  if (email === DEFAULT_ADMIN.email && password === password) {
    return DEFAULT_ADMIN;
  }
  return null;
};

export async function createAdminJs(app: INestApplication) {
  const { default: AdminJS } = await import('adminjs');
  const AdminJSTypeorm = await import('@adminjs/typeorm');
  const AdminJSExpress = await import('@adminjs/express');

  AdminJS.registerAdapter({
    Resource: AdminJSTypeorm.Resource,
    Database: AdminJSTypeorm.Database,
  });

  const adminJs = new AdminJS({
    rootPath: '/admin',
    resources: [
      RefreshTokenEntity,
      UserEntity,
      TrainingPlanEntity,
      TrainingPlanGroupEntity,
      ConsentDefinitionEntity,
      GroupEntity,
      UserConsentEntity,
      ExerciseEntity,
      TrainingEntity,
    ],
  });

  const adminRouter = AdminJSExpress.buildAuthenticatedRouter(adminJs, {
    authenticate,
    cookieName: 'adminjs',
    cookiePassword: 'secret',
  });

  app.use(adminJs.options.rootPath, adminRouter);
}
