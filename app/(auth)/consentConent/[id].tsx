import React, { useEffect, useState } from "react";
import { ScrollView, View } from "react-native";
import { useLocalSearchParams } from "expo-router";
import { StyleSheet } from "react-native";
import { LoaderSpinner, Theme, useTheme } from "@vs/kit-ui-expo";
import { useConsentStore } from "@/stores/useConsentStore";
import { Consent } from "@/types/consents";
import Markdown from "react-native-markdown-display";

export default function ConsentContentScreen() {
  const { theme } = useTheme();
  const styles = stylesConfig(theme);
  const { id } = useLocalSearchParams();
  const { getConsentsById } = useConsentStore();
  const [consent, setConsent] = useState<Consent | undefined>();

  useEffect(() => {
    const handleGetById = async () => {
      const consent = await getConsentsById(id as string);
      setConsent(consent);
    };
    handleGetById();
  }, [id]);

  return (
    <View
      style={[styles.container, { backgroundColor: theme.colors.grey[900] }]}
    >
      {!consent ? (
        <LoaderSpinner />
      ) : (
        <ScrollView style={stylesConfig(theme).scroll}>
          <Markdown style={markdownStyles(theme)}>{consent.content}</Markdown>
        </ScrollView>
      )}
    </View>
  );
}

const stylesConfig = (theme: Theme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      justifyContent: "space-between",
      paddingHorizontal: theme.spacing[6],
    },
    infoText: {
      textAlign: "center",
      paddingHorizontal: theme.spacing[16],
    },
    scroll: {
      flex: 1,
    },
  });

const markdownStyles = (theme: Theme) =>
  StyleSheet.create({
    body: {
      color: theme.colors.grey[50],
      fontFamily: theme.fontFamily.sans,
      fontSize: 16,
      lineHeight: 24,
    },
    heading1: {
      fontSize: 24,
      color: theme.colors.accent[300],
      marginBottom: 10,
    },
    heading2: {
      fontSize: 20,
      color: theme.colors.accent[200],
      marginBottom: 8,
    },
    strong: {
      fontWeight: "bold",
      color: theme.colors.accent[100],
    },
    em: {
      fontStyle: "italic",
      color: theme.colors.accent[100],
    },
    bullet_list: {
      marginVertical: 8,
    },
    bullet_list_icon: {
      color: theme.colors.accent[300],
    },
    link: {
      color: theme.colors.accent[300],
      textDecorationLine: "underline",
    },
    fence: {
      backgroundColor: theme.colors.grey[50],
      padding: 8,
      borderRadius: 4,
    },
  });
