import { Test, TestingModule } from '@nestjs/testing';
import { UsersController } from './users.controller';
import { UsersService } from '@app/users/services/users.service';
import { CreateUserDto } from '@app/users/dto/create-user.dto';
import { UserEntity } from '@app/users/entities/user.entity';
import { EUserRole } from '@app/constants/models/user-role.enum';

describe('UserController', () => {
  let controller: UsersController;
  let usersService: UsersService;

  const mockUsersService = {
    create: jest.fn(),
    findAll: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UsersController],
      providers: [
        {
          provide: UsersService,
          useValue: mockUsersService,
        },
      ],
    }).compile();

    controller = module.get<UsersController>(UsersController);
    usersService = module.get<UsersService>(UsersService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('createUser', () => {
    it('should create a new user', async () => {
      const createUserDto: CreateUserDto = {
        email: '<EMAIL>',
        name: 'Test User',
        password: 'password123',
        consentIds: ['consent1', 'consent2'],
      };

      const expectedResult: UserEntity = {
        id: 'user-id',
        email: '<EMAIL>',
        name: 'Test User',
        password: 'hashed-password',
        refreshTokens: [],
        consents: [],
        roles: [EUserRole.USER],
        createdAt: new Date(),
        hashPassword: jest.fn(),
      };

      mockUsersService.create.mockResolvedValue(expectedResult);

      const result = await controller.createUser(createUserDto);

      expect(mockUsersService.create).toHaveBeenCalledWith(createUserDto);
      expect(result).toEqual(expectedResult);
    });
  });

  describe('getUsers', () => {
    it('should return an array of users', async () => {
      const expectedResult = [
        {
          id: 'user-id-1',
          email: '<EMAIL>',
          name: 'User 1',
          password: 'hashed-password-1',
          refreshTokens: [],
          consents: [],
          roles: ['user'],
          createdAt: new Date(),
          hashPassword: jest.fn(),
        },
        {
          id: 'user-id-2',
          email: '<EMAIL>',
          name: 'User 2',
          password: 'hashed-password-2',
          refreshTokens: [],
          consents: [],
          roles: ['user'],
          createdAt: new Date(),
          hashPassword: jest.fn(),
        },
      ] as unknown as UserEntity[];

      mockUsersService.findAll.mockResolvedValue(expectedResult);

      const result = await controller.getUsers();

      expect(mockUsersService.findAll).toHaveBeenCalled();
      expect(result).toEqual(expectedResult);
    });
  });
});
