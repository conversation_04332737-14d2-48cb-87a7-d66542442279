import { MigrationInterface, QueryRunner } from "typeorm";

export class AddInstructionFieldInExerciseTable1751231285760 implements MigrationInterface {
    name = 'AddInstructionFieldInExerciseTable1751231285760'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "exercises" ADD "instructions" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "exercises" DROP COLUMN "instructions"`);
    }

}
