import { useEffect } from "react";
import { useErrorHandling } from "./useErrorHandling";
import { getAllConsents } from "@/api/consent";
import { useConsentStore } from "@/stores/useConsentStore";
import { useLoading } from "./useLoading";

export const useConsents = () => {
  const { withErrorHandling } = useErrorHandling();
  const { setConsents, consents } = useConsentStore();
  const { withLoading, loading } = useLoading();

  const handleGetConsents = () =>
    withLoading(() =>
      withErrorHandling(async () => {
        const res = await getAllConsents();
        setConsents(res);
      })
    );

  useEffect(() => {
    handleGetConsents();
  }, []);

  return {
    consents,
    loading,
  };
};
