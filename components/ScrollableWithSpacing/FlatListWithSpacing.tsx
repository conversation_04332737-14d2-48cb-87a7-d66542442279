import { Theme, useTheme } from "@vs/kit-ui-expo";
import React from "react";
import { FlatList, FlatListProps, StyleSheet } from "react-native";

interface FlatListWithSpacingProps<T> extends FlatListProps<T> {}

const FlatListWithSpacing = <T,>({
  style,
  contentContainerStyle,
  ...props
}: FlatListWithSpacingProps<T>) => {
  const { theme } = useTheme();
  const styles = fnStyles(theme);

  return (
    <FlatList
      {...props}
      style={[styles.container, style]}
      contentContainerStyle={[styles.contentContainer, contentContainerStyle]}
    />
  );
};

const fnStyles = (theme: Theme) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    contentContainer: {
      paddingBottom: theme.spacing[20],
    },
  });

export default FlatListWithSpacing;
