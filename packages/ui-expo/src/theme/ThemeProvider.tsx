import React, { createContext, useContext, useRef, useEffect } from "react";
import { theme as defaultTheme, Theme } from "./theme";

// Przechowujemy aktualny motyw w zmiennej modułu
let currentTheme: Theme = defaultTheme;

// Funkcja do ustawiania motywu, którą aplikacja docelowa może wywołać
export const setAppTheme = (theme: Theme) => {
  currentTheme = theme;
};

// Hook useTheme, który zawsze zwraca aktualny motyw
export const useTheme = () => {
  return { theme: currentTheme };
};

interface ThemeProviderProps {
  children: React.ReactNode;
  theme?: Theme;
}

// ThemeProvider jest teraz tylko wrapperem, który ustawia motyw
export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children, theme = defaultTheme }) => {
  // Używamy useRef, aby śledzić poprzedni motyw i unikać niepotrzebnych aktualizacji
  const prevThemeRef = useRef(theme);
  
  // Aktualizuj currentTheme tylko gdy motyw się zmienił
  useEffect(() => {
    // Porównujemy referencje, nie wartości, aby uniknąć głębokich porównań
    if (prevThemeRef.current !== theme) {
      setAppTheme(theme);
      prevThemeRef.current = theme;
    }
  }, [theme]);
  
  return <>{children}</>;
};
