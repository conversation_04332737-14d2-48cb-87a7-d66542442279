module.exports = {
  multipass: true,
  js2svg: {
    indent: 2,
    pretty: true,
  },
  plugins: [
    { name: "preset-default" },
    "sortAttrs",
    "removeScriptElement",
    "removeDimensions",
    // Don't remove fill and stroke attributes completely
    // Instead, we'll handle them in the build script
    // {
    //   name: 'removeAttrs',
    //   params: {
    //     attrs: ['stroke', 'fill'], // Usunięcie istniejących wartości stroke i fill
    //   },
    // },
    // {
    //   name: 'addAttributesToSVGElement',
    //   params: {
    //     attributes: [{ stroke: 'currentColor' }, { fill: 'currentColor' }],
    //   },
    // },
    {
      name: "removeAttrs",
      params: {
        attrs: ["stroke", "fill"], // Usunięcie istniejących wartości stroke i fill
      },
    },
  ],
};
