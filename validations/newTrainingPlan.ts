import { z } from "zod";

export const newTrainingPlanSchema = z.object({
  name: z
    .string({ message: "validation_messages_required" })
    .min(3, "validation_messages_min_length_string"),
  description: z.string({ message: "validation_messages_required" }),
  trainings: z
    .array(z.string(), { message: "validation_messages_required" })
    .min(3, "validation_messages_min_exercises_count"),
});

export type NewTrainingPlanType = z.infer<typeof newTrainingPlanSchema>;
