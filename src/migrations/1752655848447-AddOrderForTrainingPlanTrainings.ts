import { MigrationInterface, QueryRunner } from "typeorm";

export class AddOrderForTrainingPlanTrainings1752655848447 implements MigrationInterface {
    name = 'AddOrderForTrainingPlanTrainings1752655848447'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "training_plan_trainings" DROP CONSTRAINT "FK_2c485373a1f199fdb151421f8fd"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_45ca5231e88b422575cd81b474"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_2c485373a1f199fdb151421f8f"`);
        await queryRunner.query(`ALTER TABLE "training_plan_trainings" ADD "id" uuid NOT NULL DEFAULT uuid_generate_v4()`);
        await queryRunner.query(`ALTER TABLE "training_plan_trainings" DROP CONSTRAINT "PK_23a5cad7150ad24e9fc65c8eac9"`);
        await queryRunner.query(`ALTER TABLE "training_plan_trainings" ADD CONSTRAINT "PK_1bf6c4bbe5f58d808f27a816d4d" PRIMARY KEY ("training_id", "training_plan_id", "id")`);
        await queryRunner.query(`ALTER TABLE "training_plan_trainings" ADD "order_index" integer NOT NULL`);
        await queryRunner.query(`ALTER TABLE "training_plan_trainings" ADD "createdAt" TIMESTAMP NOT NULL DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "training_plan_trainings" DROP CONSTRAINT "FK_45ca5231e88b422575cd81b474c"`);
        await queryRunner.query(`ALTER TABLE "training_plan_trainings" DROP CONSTRAINT "PK_1bf6c4bbe5f58d808f27a816d4d"`);
        await queryRunner.query(`ALTER TABLE "training_plan_trainings" ADD CONSTRAINT "PK_bc669cff0aa73600f2e7e163a91" PRIMARY KEY ("training_id", "id")`);
        await queryRunner.query(`ALTER TABLE "training_plan_trainings" DROP CONSTRAINT "PK_bc669cff0aa73600f2e7e163a91"`);
        await queryRunner.query(`ALTER TABLE "training_plan_trainings" ADD CONSTRAINT "PK_7fcdd5bf5c3e05b83b3da2a5b2b" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "training_plan_trainings" ADD CONSTRAINT "FK_45ca5231e88b422575cd81b474c" FOREIGN KEY ("training_plan_id") REFERENCES "training_plans"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "training_plan_trainings" ADD CONSTRAINT "FK_2c485373a1f199fdb151421f8fd" FOREIGN KEY ("training_id") REFERENCES "trainings"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "training_plan_trainings" DROP CONSTRAINT "FK_2c485373a1f199fdb151421f8fd"`);
        await queryRunner.query(`ALTER TABLE "training_plan_trainings" DROP CONSTRAINT "FK_45ca5231e88b422575cd81b474c"`);
        await queryRunner.query(`ALTER TABLE "training_plan_trainings" DROP CONSTRAINT "PK_7fcdd5bf5c3e05b83b3da2a5b2b"`);
        await queryRunner.query(`ALTER TABLE "training_plan_trainings" ADD CONSTRAINT "PK_bc669cff0aa73600f2e7e163a91" PRIMARY KEY ("training_id", "id")`);
        await queryRunner.query(`ALTER TABLE "training_plan_trainings" DROP CONSTRAINT "PK_bc669cff0aa73600f2e7e163a91"`);
        await queryRunner.query(`ALTER TABLE "training_plan_trainings" ADD CONSTRAINT "PK_1bf6c4bbe5f58d808f27a816d4d" PRIMARY KEY ("training_id", "training_plan_id", "id")`);
        await queryRunner.query(`ALTER TABLE "training_plan_trainings" ADD CONSTRAINT "FK_45ca5231e88b422575cd81b474c" FOREIGN KEY ("training_plan_id") REFERENCES "training_plans"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "training_plan_trainings" DROP COLUMN "createdAt"`);
        await queryRunner.query(`ALTER TABLE "training_plan_trainings" DROP COLUMN "order_index"`);
        await queryRunner.query(`ALTER TABLE "training_plan_trainings" DROP CONSTRAINT "PK_1bf6c4bbe5f58d808f27a816d4d"`);
        await queryRunner.query(`ALTER TABLE "training_plan_trainings" ADD CONSTRAINT "PK_23a5cad7150ad24e9fc65c8eac9" PRIMARY KEY ("training_id", "training_plan_id")`);
        await queryRunner.query(`ALTER TABLE "training_plan_trainings" DROP COLUMN "id"`);
        await queryRunner.query(`CREATE INDEX "IDX_2c485373a1f199fdb151421f8f" ON "training_plan_trainings" ("training_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_45ca5231e88b422575cd81b474" ON "training_plan_trainings" ("training_plan_id") `);
        await queryRunner.query(`ALTER TABLE "training_plan_trainings" ADD CONSTRAINT "FK_2c485373a1f199fdb151421f8fd" FOREIGN KEY ("training_id") REFERENCES "trainings"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
    }

}
