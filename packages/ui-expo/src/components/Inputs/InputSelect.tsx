import React, { useState, useRef } from "react";
import {
  View,
  Text,
  StyleSheet,
  StyleProp,
  TextStyle,
  TouchableOpacity,
  ScrollView,
  Modal,
  Pressable,
} from "react-native";
import { Sizes, buttonSizeStyles } from "../../utils/sizes";
import { useTheme } from "../../theme";

export interface SelectOption {
  label: string;
  value: string;
}

interface Props {
  value: string;
  onChangeText: (value: string) => void;
  options: SelectOption[];
  placeholder?: string;
  label?: string;
  size?: Sizes;
  disabled?: boolean;
  inputStyle?: StyleProp<TextStyle>;
  labelStyle?: StyleProp<TextStyle>;
  onBlur?: () => void;
  error?: string;
  maxHeight?: number;
}

export const InputSelect = ({
  value,
  onChangeText,
  options,
  placeholder = "Wybierz opcję...",
  label,
  size = Sizes.Medium,
  disabled,
  inputStyle,
  labelStyle,
  onBlur,
  error,
  maxHeight = 200,
}: Props) => {
  const { theme } = useTheme();
  const sizeStyle = buttonSizeStyles[size];
  const [isOpen, setIsOpen] = useState(false);
  const [isFocused, setIsFocused] = useState(false);

  const selectedOption = options.find(option => option.value === value);
  const displayValue = selectedOption ? selectedOption.label : "";

  const handleSelect = (selectedValue: string) => {
    onChangeText(selectedValue);
    setIsOpen(false);
    setIsFocused(false);
    onBlur?.();
  };

  const handlePress = () => {
    if (!disabled) {
      setIsOpen(true);
      setIsFocused(true);
    }
  };

  return (
    <View style={styles.container}>
      {label && (
        <Text
          style={[
            styles.label,
            {
              color: error ? theme.colors.alert[500] : theme.colors.grey[50],
              fontWeight: theme.typography.BodyS.fontWeight as TextStyle["fontWeight"],
              fontSize: theme.typography.BodyS.fontSize,
              lineHeight: theme.typography.BodyS.lineHeight,
            },
            labelStyle,
          ]}
        >
          {label}
        </Text>
      )}

      <TouchableOpacity
        style={[
          styles.input,
          styles.selectButton,
          sizeStyle,
          {
            borderColor: error
              ? theme.colors.alert[500]
              : isFocused
                ? theme.colors.accent[400]
                : theme.colors.grey[500],
            backgroundColor: theme.colors.grey[900],
            borderRadius: theme.borderRadius.lg,
          },
          disabled && { opacity: 0.7 },
          inputStyle,
        ]}
        onPress={handlePress}
        disabled={disabled}
        activeOpacity={0.8}
      >
        <Text
          style={[
            styles.selectText,
            {
              color: displayValue ? theme.colors.grey[50] : theme.colors.grey[400],
              fontWeight: theme.typography.BodyS.fontWeight as TextStyle["fontWeight"],
              fontSize: theme.typography.BodyS.fontSize,
              lineHeight: theme.typography.BodyS.lineHeight,
            },
          ]}
        >
          {displayValue || placeholder}
        </Text>
        <Text
          style={[
            styles.arrow,
            {
              color: theme.colors.grey[400],
              transform: [{ rotate: isOpen ? "180deg" : "0deg" }],
            },
          ]}
        >
          ▼
        </Text>
      </TouchableOpacity>

      <Modal
        visible={isOpen}
        transparent
        animationType="fade"
        onRequestClose={() => {
          setIsOpen(false);
          setIsFocused(false);
          onBlur?.();
        }}
      >
        <Pressable
          style={styles.modalOverlay}
          onPress={() => {
            setIsOpen(false);
            setIsFocused(false);
            onBlur?.();
          }}
        >
          <View style={styles.modalContent}>
            <ScrollView
              style={[
                styles.optionsList,
                {
                  maxHeight,
                  backgroundColor: theme.colors.grey[800],
                  borderRadius: theme.borderRadius.lg,
                  borderColor: theme.colors.grey[500],
                },
              ]}
              showsVerticalScrollIndicator={false}
            >
              {options.map((option, index) => (
                <TouchableOpacity
                  key={option.value}
                  style={[
                    styles.option,
                    {
                      backgroundColor: option.value === value ? theme.colors.accent[400] + "20" : "transparent",
                      borderBottomColor: theme.colors.grey[600],
                      borderBottomWidth: index < options.length - 1 ? 1 : 0,
                    },
                  ]}
                  onPress={() => handleSelect(option.value)}
                  activeOpacity={0.7}
                >
                  <Text
                    style={[
                      styles.optionText,
                      {
                        color: option.value === value ? theme.colors.accent[400] : theme.colors.grey[50],
                        fontWeight: theme.typography.BodyS.fontWeight as TextStyle["fontWeight"],
                        fontSize: theme.typography.BodyS.fontSize,
                        lineHeight: theme.typography.BodyS.lineHeight,
                      },
                    ]}
                  >
                    {option.label}
                  </Text>
                  {option.value === value && (
                    <Text style={[styles.checkmark, { color: theme.colors.accent[400] }]}>✓</Text>
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </Pressable>
      </Modal>

      {error && <Text style={[styles.errorLabel, { color: theme.colors.alert[500] }]}>{error}</Text>}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: "100%",
  },
  input: {
    paddingHorizontal: 24,
    fontSize: 16,
    borderWidth: 1,
  },
  selectButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    minHeight: 50,
  },
  selectText: {
    flex: 1,
  },
  arrow: {
    fontSize: 12,
    marginLeft: 8,
  },
  label: {
    marginBottom: 8,
  },
  errorLabel: {
    marginTop: 4,
    fontSize: 12,
    lineHeight: 16,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 20,
  },
  modalContent: {
    width: "100%",
    maxWidth: 400,
  },
  optionsList: {
    borderWidth: 1,
    maxHeight: 200,
  },
  option: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: 16,
    paddingHorizontal: 24,
  },
  optionText: {
    flex: 1,
  },
  checkmark: {
    fontSize: 16,
    fontWeight: "bold",
  },
});
