import { z } from "zod";
import { DifficultyLevel } from "../enums/difficultyLevel";

export const newExerciseSchema = z.object({
  name: z
    .string({ message: "validation_messages_required" })
    .min(3, "validation_messages_min_length_string"),
  description: z.string({ message: "validation_messages_required" }),
  tags: z.string({ message: "validation_messages_required" }),
  difficultyLevel: z.nativeEnum(DifficultyLevel, {
    message: "validation_messages_required",
  }),
  instructions: z
    .string({ message: "validation_messages_required" })
    .min(3, "validation_messages_min_length_string"),
});

export type NewExerciseType = z.infer<typeof newExerciseSchema>;
