import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { CreateTrainingRequestDto } from '@app/training/dto/create-training.dto';
import { TrainingEntity } from '@app/training/entities/training.entity';

export const createAndAssertTraining = async (
  app: INestApplication,
  payload: CreateTrainingRequestDto,
  accessToken: string,
): Promise<TrainingEntity> => {
  const response = await request(app.getHttpServer())
    .post('/v1/training')
    .set('Authorization', `Bearer ${accessToken}`)
    .send(payload);

  expect(response.status).toBe(201);
  expect(response.body).toMatchObject({
    name: payload.name,
    description: payload.description,
    tags: payload.tags,
    status: 'active',
    exercises: expect.any(Array),
  });
  expect(response.body.id).toMatch(/^[\w-]{36}$/);
  expect(response.body.createdAt).toBeDefined();
  expect(response.body.updatedAt).toBeDefined();
  expect(response.body.creatorId).toBeDefined();

  return response.body as TrainingEntity;
};

export const deleteAndAssertTraining = async (app: INestApplication, id: string, accessToken: string) => {
  const response = await request(app.getHttpServer())
    .delete(`/v1/training/${id}`)
    .set('Authorization', `Bearer ${accessToken}`);

  expect(response.status).toBe(200);
  expect(response.body.id).toBe(id);
  expect(response.body.status).toBe('deleted');
};
