import { StyleSheet, View, Image, KeyboardAvoidingView } from "react-native";
import {
  ButtonBasic,
  CheckboxBasic,
  InputBasic,
  InputPassword,
  LoaderSpinnerPage,
  Theme,
  Typography,
  useTheme,
} from "@vs/kit-ui-expo";
import { Controller } from "react-hook-form";
import { useRegister } from "@/hooks/useRegister";
import { ForgotPasswordLink } from "@/components/ForgotPasswordLink";
import { useTranslation } from "react-i18next";
import { BottleAssets } from "@/enums/bottleAssets";
import { useConsents } from "@/hooks/useConsents";
import { useRouter } from "expo-router";
import { ContainerBottomModalWithAuthLayout } from "@/components/ContainerBottomModal";

export default function RegisterScreen() {
  const { theme } = useTheme();
  const { t } = useTranslation();
  const { consents, loading } = useConsents();
  const {
    control,
    handleSubmit,
    errors,
    onSubmit,
    handleConsentChange,
    checkedConsents,
  } = useRegister({ consents });

  const router = useRouter();

  const renderHeader = () => {
    return (
      <View style={styles.logoContainer}>
        <Image
          source={BottleAssets.bottlePhone}
          style={styles.logo}
          resizeMode="contain"
        />
      </View>
    );
  };

  const renderForm = () => {
    return (
      <>
        <Controller
          control={control}
          name="name"
          render={({ field: { onChange, onBlur, value } }) => (
            <InputBasic
              placeholder={t("register_screen_name_placeholder")}
              label={t("register_screen_name_label")}
              onBlur={onBlur}
              onChangeText={onChange}
              value={value}
              error={t(errors.name?.message || "")}
            />
          )}
        />
        <Controller
          control={control}
          name="email"
          render={({ field: { onChange, onBlur, value } }) => (
            <InputBasic
              placeholder={t("register_screen_email_placeholder")}
              label={t("register_screen_email_label")}
              onBlur={onBlur}
              onChangeText={onChange}
              value={value}
              error={t(errors.email?.message || "")}
            />
          )}
        />
        <Controller
          control={control}
          name="password"
          render={({ field: { onChange, onBlur, value } }) => (
            <InputPassword
              placeholder={t("register_screen_password_placeholder")}
              label={t("register_screen_password_label")}
              onBlur={onBlur}
              onChangeText={onChange}
              value={value}
              error={t(errors.password?.message || "")}
            />
          )}
        />
        <Controller
          control={control}
          name="confirmPassword"
          render={({ field: { onChange, onBlur, value } }) => (
            <InputPassword
              placeholder={t("register_screen_confirm_password_placeholder")}
              label={t("register_screen_confirm_password_label")}
              onBlur={onBlur}
              onChangeText={onChange}
              value={value}
              error={t(errors.confirmPassword?.message || "")}
            />
          )}
        />
      </>
    );
  };

  const renderConsents = () => {
    return (
      <View style={{ width: "100%" }}>
        <View style={stylesConfig(theme).checkbox}>
          {consents.map((consent) => (
            <CheckboxBasic
              key={consent.id}
              label={`${consent.shortDescription} ${
                consent.isRequired ? "*" : ""
              }`}
              onLabelPress={() => {
                router.push({
                  pathname: "/consentConent/[id]",
                  params: { id: consent.id },
                });
              }}
              onPress={() => handleConsentChange(consent)}
              checked={checkedConsents.includes(consent)}
            />
          ))}
        </View>
      </View>
    );
  };

  const renderButtons = () => {
    return (
      <>
        <ButtonBasic onPress={handleSubmit(onSubmit)}>
          {t("register_screen_button_register")}
        </ButtonBasic>
        <ForgotPasswordLink />
      </>
    );
  };

  if (loading) {
    return <LoaderSpinnerPage />;
  }
  return (
    <KeyboardAvoidingView
      behavior={"height"}
      style={[styles.container, { backgroundColor: theme.colors.grey[900] }]}
    >
      {renderHeader()}
      <ContainerBottomModalWithAuthLayout>
        <Typography variant="Heading4" color={theme.colors.grey[50]}>
          {t("register_screen_title")}
        </Typography>

        {renderForm()}
        {renderConsents()}
        {renderButtons()}
      </ContainerBottomModalWithAuthLayout>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "space-between",
  },
  logoContainer: {
    alignItems: "center",
    position: "relative",
    flex: 1,
  },
  logo: {
    width: 200,
    height: 200,
    right: 0,
    bottom: -48,
    position: "absolute",
    resizeMode: "contain",
    transform: [{ rotate: "16deg" }],
  },
  formContainer: {
    paddingHorizontal: 24,
    marginBottom: 40,
  },
  inputContainer: {
    marginBottom: 16,
  },
  footer: {
    flexDirection: "row",
    justifyContent: "center",
    marginTop: 24,
  },
});

const stylesConfig = (theme: Theme) =>
  StyleSheet.create({
    checkbox: {
      gap: theme.spacing[4],
      justifyContent: "flex-start",
      alignItems: "flex-start",
      maxWidth: 300,
    },
  });
