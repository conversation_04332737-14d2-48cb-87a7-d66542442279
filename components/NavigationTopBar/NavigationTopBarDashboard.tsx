import { View, Image } from "react-native";
import { Theme, useTheme } from "@vs/kit-ui-expo";
import { StyleSheet } from "react-native";
import { NavigationHamburgerIcon } from "@vs/kit-ui-assets";

export const NavigationTopBarDashboard = () => {
  const { theme } = useTheme();
  const styles = stylesConfig(theme);

  return (
    <View style={styles.container}>
      <Image
        source={require("../../assets/images/logo.png")}
        style={styles.logo}
        resizeMode="contain"
      />
      <NavigationHamburgerIcon fill={theme.colors.accent[300]} />
    </View>
  );
};

const stylesConfig = (theme: Theme) =>
  StyleSheet.create({
    container: {
      height: 64,
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      paddingHorizontal: theme.spacing[6],
      backgroundColor: theme.colors.grey[900],
    },
    logo: {
      width: 64,
      height: 64,
    },
  });
