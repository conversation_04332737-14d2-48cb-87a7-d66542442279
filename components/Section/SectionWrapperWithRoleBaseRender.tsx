import { useRoleSwitcherProvider } from "@/contexts/RoleSwitcherProvider";
import { EUserRole } from "@/enums/roles";
import { useUserStore } from "@/stores/useUserStore";
import { ReactNode } from "react";

interface Props {
  children: ReactNode;
  allowedRoles?: EUserRole | EUserRole[];
  restrictedRoles?: EUserRole | EUserRole[];
  useActiveRole?: boolean;
}

export const SectionWrapperWithRoleBaseRender = ({
  children,
  allowedRoles,
  restrictedRoles,
  useActiveRole = true,
}: Props) => {
  const { user } = useUserStore();
  const userRoles = user?.roles || [];
  const { activeRole } = useRoleSwitcherProvider();

  // Helper function to check if role matches allowed/restricted roles
  const checkRoleMatch = (
    roles: EUserRole | EUserRole[],
    targetRole: EUserRole
  ): boolean => {
    return Array.isArray(roles)
      ? roles.includes(targetRole)
      : roles === targetRole;
  };

  // Determine which roles to check based on useActiveRole flag
  const rolesToCheck = useActiveRole ? [activeRole] : userRoles;

  // Check allowed roles
  const hasAllowedRole = allowedRoles
    ? rolesToCheck.some((role) => checkRoleMatch(allowedRoles, role))
    : true; // If no allowedRoles specified, allow by default

  // Check restricted roles
  const hasRestrictedRole = restrictedRoles
    ? rolesToCheck.some((role) => checkRoleMatch(restrictedRoles, role))
    : false;

  if (hasRestrictedRole || (!hasAllowedRole && allowedRoles)) {
    return null;
  }

  return <>{children}</>;
};
