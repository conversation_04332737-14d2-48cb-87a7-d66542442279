import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { LinkGradient } from "@vs/kit-ui-next";

const meta: Meta<typeof LinkGradient> = {
  title: "Components/Links/LinkGradient",
  component: LinkGradient,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof LinkGradient>;

export const Default: Story = {
  args: {
    href: "#",
    children: "Click me",
  },
};

export const WithCustomText: Story = {
  args: {
    href: "/example",
    children: "Custom Link Text",
  },
};

export const WithoutChildren: Story = {
  args: {
    href: "/default",
  },
};

export const ExternalLink: Story = {
  args: {
    href: "https://example.com",
    children: "External Link",
    target: "_blank",
    rel: "noopener noreferrer",
  },
};
