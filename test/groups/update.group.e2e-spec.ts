import 'reflect-metadata';
import { INestApplication } from '@nestjs/common';
import request from 'supertest';

import { loginAndAssertUser, registerAndAssertUser } from '../__helpers/auth';
import { buildTestApp } from '../__setup/app.factory';
import { generateTestUser } from '../__helpers/user';
import { ConsentDefinitionEntity } from '@app/consents/entities/consent-definition.entity';
import { createAndAssertConsent, generateGDPRConsentPayload } from '../__helpers/consents';
import { CreateGroupRequestDto } from '@app/groups/dto/create-group.dto';
import { addAndAssertUserToGroup, createAndAssertGroup } from '../__helpers/group';
import { UpdateGroupRequestDto } from '@app/groups/dto/update-group.dto';
import { randomUUID } from 'crypto';

describe('/UPDATE /v1/group/:groupId', () => {
  let app: INestApplication;
  let accessToken: string;
  let consent: ConsentDefinitionEntity;

  beforeAll(async () => {
    app = await buildTestApp();

    const consentPayload = generateGDPRConsentPayload();
    consent = await createAndAssertConsent(app, consentPayload);

    const testUser = generateTestUser([consent.id]);
    await registerAndAssertUser(app, testUser);
    accessToken = (await loginAndAssertUser(app, testUser)).accessToken;
  });

  afterAll(async () => {
    await app.close();
  });

  // TODO: only trainer should be able to create a group
  it('should update a group', async () => {
    const payloadToCreateGroup: CreateGroupRequestDto = {
      name: 'Test Exercise E2E',
      description: 'This is a test exercise',
      tags: 'e2e|test',
    };

    const group = await createAndAssertGroup(app, payloadToCreateGroup, accessToken);

    const payloadToUpdateGroup: UpdateGroupRequestDto = {
      name: 'Updated name',
      description: 'Updated description',
    };

    const response = await request(app.getHttpServer())
      .put(`/v1/group/${group.id}`)
      .set('Authorization', `Bearer ${accessToken}`)
      .send(payloadToUpdateGroup);

    expect(response.status).toBe(200);
    expect(response.body.id).toBeDefined();
    expect(response.body.name).toBe(payloadToUpdateGroup.name);
    expect(response.body.description).toBe(payloadToUpdateGroup.description);
    expect(response.body.tags).toBe(payloadToCreateGroup.tags);
    expect(response.body.createdAt).toBeDefined();
    expect(response.body.updatedAt).toBeDefined();
    expect(response.body.ownerId).toBeDefined();
  });

  it('should return 401 when user is not authenticated', async () => {
    const payloadToCreateGroup: CreateGroupRequestDto = {
      name: 'Test Exercise E2E',
      description: 'This is a test exercise',
      tags: 'e2e|test',
    };

    const group = await createAndAssertGroup(app, payloadToCreateGroup, accessToken);

    const response = await request(app.getHttpServer()).put(`/v1/group/${group.id}`);

    expect(response.status).toBe(401);
    expect(response.body.message).toBe('Unauthorized');
  });

  it('should return 404 when group not found', async () => {
    const id = randomUUID();
    const response = await request(app.getHttpServer())
      .put(`/v1/group/${id}`)
      .set('Authorization', `Bearer ${accessToken}`);

    expect(response.status).toBe(404);
    expect(response.body.message).toBe(`Group with id ${id} not found`);
  });

  it('should return 403 when user is not owner', async () => {
    const payloadToCreateGroup: CreateGroupRequestDto = {
      name: 'Test Exercise E2E',
      description: 'This is a test exercise',
      tags: 'e2e|test',
    };

    const group = await createAndAssertGroup(app, payloadToCreateGroup, accessToken);

    const notOwner = generateTestUser([consent.id]);
    const notOwnerUser = await registerAndAssertUser(app, notOwner);
    await addAndAssertUserToGroup(app, group.id, notOwnerUser.id, accessToken);
    const notOwnerAccessToken = (await loginAndAssertUser(app, notOwner)).accessToken;

    const response = await request(app.getHttpServer())
      .put(`/v1/group/${group.id}`)
      .set('Authorization', `Bearer ${notOwnerAccessToken}`);

    expect(response.status).toBe(403);
    expect(response.body.message).toBe('Only the group owner can perform this operation');
  });
});
