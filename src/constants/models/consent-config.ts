/**
 * Types of consents available in the system
 */
export enum ConsentType {
  GDPR = 'GDPR',
  POLICY = 'POLICY',
}

/**
 * Configuration for each consent type
 */
export interface ConsentConfig {
  /**
   * Type of consent
   */
  type: ConsentType;

  /**
   * Whether this consent is required for user registration
   */
  required: boolean;
}

export const CONSENTS_CONFIG: ConsentConfig[] = [
  {
    type: ConsentType.GDPR,
    required: true,
  },
  {
    type: ConsentType.POLICY,
    required: false,
  },
];
