import React from "react";
import { StyleSheet, View, ViewStyle } from "react-native";
import { Typography, useTheme } from "@vs/kit-ui-expo";

type BadgeProps = {
  labels: string[];
  containerStyle?: ViewStyle;
  badgeColor?: string;
  textColor?: string;
  align?: "left" | "center" | "right";
};

export const Badges: React.FC<BadgeProps> = ({
  labels,
  containerStyle,
  badgeColor,
  textColor,
  align = "right",
}) => {
  const { theme } = useTheme();

  const alignment: ViewStyle["justifyContent"] =
    align === "left"
      ? "flex-start"
      : align === "center"
      ? "center"
      : "flex-end";

  return (
    <View
      style={[styles.badges, { justifyContent: alignment }, containerStyle]}
    >
      {labels.map((text, idx) => (
        <View
          key={idx}
          style={[
            styles.badge,
            { backgroundColor: badgeColor || theme.colors.accent[400] },
          ]}
        >
          <Typography
            variant="BodyM"
            color={textColor || theme.colors.grey[50]}
          >
            {text}
          </Typography>
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  badges: {
    flexDirection: "row",
    gap: 16,
    paddingBottom: 16,
  },
  badge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderBottomEndRadius: 8,
    borderBottomStartRadius: 8,
  },
});
