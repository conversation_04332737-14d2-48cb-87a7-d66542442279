import React from "react";
import { StyleSheet, View, StyleProp, ViewStyle } from "react-native";
import { LinearGradient } from "expo-linear-gradient";

type Props = {
  children: React.ReactNode;
  style?: StyleProp<ViewStyle>;
  borderColor?: string;
  gradientColors?: readonly [string, string, ...string[]];
  gradientStart?: { x: number; y: number };
  gradientEnd?: { x: number; y: number };
  useGradient?: boolean;
};

export const BorderBoxWithGradient = ({
  children,
  style,
  borderColor = "#354720",
  gradientColors = ["#354720", "#28351a", "#1b2214"],
  gradientStart = { x: 0, y: 0 },
  gradientEnd = { x: 1, y: 1 },
  useGradient = true,
}: Props) => {
  const composedStyle = StyleSheet.compose(styles.container, style);

  if (useGradient) {
    return (
      <LinearGradient
        colors={gradientColors}
        start={gradientStart}
        end={gradientEnd}
        style={[composedStyle, { borderColor: borderColor, borderWidth: 1 }]}
      >
        {children}
      </LinearGradient>
    );
  }

  return <View style={composedStyle}>{children}</View>;
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    overflow: "hidden",
  },
});
