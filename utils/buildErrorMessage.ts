import { errorInstances } from "@/errors";
import { <PERSON>rror<PERSON>lassFields } from "@/errors/types";
import { ToastProps } from "@vs/kit-ui-expo";

export const buildErrorMessage = (error: ErrorClassFields): ToastProps => {
  if (errorInstances.some((instance) => instance(error))) {
    return {
      title: `${error.type} - ${error.code}`,
      message: error.message,
    };
  } else {
    return {
      title: "Unknown error",
      message: error.message,
    };
  }
};
