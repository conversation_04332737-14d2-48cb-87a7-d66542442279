import {
  ApiTags,
  ApiOperation,
  ApiBearerAuth,
  ApiOkResponse,
  ApiUnauthorizedResponse,
  ApiCreatedResponse,
  ApiBadRequestResponse,
  ApiNotFoundResponse,
  ApiForbiddenResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { applyDecorators } from '@nestjs/common';
import { GroupEntity } from '../entities/group.entity';

export class GroupsSwaggerDocs {
  static apiTags() {
    return applyDecorators(ApiTags('Groups'));
  }

  static apiBearerAuth() {
    return applyDecorators(ApiBearerAuth('JWT'));
  }

  static createGroupDocs() {
    return applyDecorators(
      ApiBearerAuth(),
      ApiOperation({
        summary: 'Create a new group',
        description: 'Creates a new group with the provided details. The current user becomes the owner of the group.',
      }),
      ApiCreatedResponse({
        description: 'Group successfully created',
        type: GroupEntity,
      }),
      ApiBadRequestResponse({
        description: 'Invalid input data',
      }),
      ApiUnauthorizedResponse({
        description: 'Unauthorized access - valid JWT token required',
      }),
    );
  }

  static getGroupsDocs() {
    return applyDecorators(
      ApiBearerAuth(),
      ApiOperation({
        summary: 'Get all groups for the current user',
        description: 'Retrieves a list of all groups the current user belongs to.',
      }),
      ApiOkResponse({
        description: 'List of groups retrieved successfully',
        type: [GroupEntity],
      }),
      ApiUnauthorizedResponse({
        description: 'Unauthorized access - valid JWT token required',
      }),
    );
  }

  static getGroupByIdDocs() {
    return applyDecorators(
      ApiBearerAuth(),
      ApiOperation({
        summary: 'Get group details by ID',
        description: 'Retrieves detailed information about a specific group, including its members.',
      }),
      ApiParam({
        name: 'groupId',
        description: 'ID of the group to retrieve',
        type: 'string',
      }),
      ApiOkResponse({
        description: 'Group details retrieved successfully',
        type: GroupEntity,
      }),
      ApiUnauthorizedResponse({
        description: 'Unauthorized access - valid JWT token required',
      }),
      ApiForbiddenResponse({
        description: 'User is not a member of the group',
      }),
      ApiNotFoundResponse({
        description: 'Group not found',
      }),
    );
  }

  static updateGroupDocs() {
    return applyDecorators(
      ApiBearerAuth(),
      ApiOperation({
        summary: 'Update group details',
        description: 'Updates the details of a specific group. Only the group owner can perform this action.',
      }),
      ApiParam({
        name: 'groupId',
        description: 'ID of the group to update',
        type: 'string',
      }),
      ApiOkResponse({
        description: 'Group updated successfully',
        type: GroupEntity,
      }),
      ApiUnauthorizedResponse({
        description: 'Unauthorized access - valid JWT token required',
      }),
      ApiForbiddenResponse({
        description: 'Only the group owner can perform this operation',
      }),
      ApiNotFoundResponse({
        description: 'Group not found',
      }),
    );
  }

  static deleteGroupDocs() {
    return applyDecorators(
      ApiBearerAuth(),
      ApiOperation({
        summary: 'Delete group',
        description: 'Deletes a specific group. Only the group owner can perform this action.',
      }),
      ApiParam({
        name: 'groupId',
        description: 'ID of the group to delete',
        type: 'string',
      }),
      ApiOkResponse({
        description: 'Group deleted successfully',
      }),
      ApiUnauthorizedResponse({
        description: 'Unauthorized access - valid JWT token required',
      }),
      ApiForbiddenResponse({
        description: 'Only the group owner can perform this operation',
      }),
      ApiNotFoundResponse({
        description: 'Group not found',
      }),
    );
  }

  static addUserToGroupDocs() {
    return applyDecorators(
      ApiBearerAuth(),
      ApiOperation({
        summary: 'Add a user to a group',
        description: 'Adds a user to a specific group. Used after scanning an invitation QR code.',
      }),
      ApiParam({
        name: 'groupId',
        description: 'ID of the group to add the user to',
        type: 'string',
      }),
      ApiParam({
        name: 'userId',
        description: 'ID of the user to add to the group',
        type: 'string',
      }),
      ApiOkResponse({
        description: 'User added to group successfully',
      }),
      ApiUnauthorizedResponse({
        description: 'Unauthorized access - valid JWT token required',
      }),
      ApiBadRequestResponse({
        description: 'User is already a member of the group',
      }),
      ApiNotFoundResponse({
        description: 'Group not found',
      }),
    );
  }

  static joinGroupDocs() {
    return applyDecorators(
      ApiBearerAuth(),
      ApiOperation({
        summary: 'Join a group',
        description: 'Joins a specific group. Used after scanning an invitation QR code.',
      }),
      ApiQuery({
        name: 'invitationToken',
        required: true,
        description: 'Invitation token from group invitation url',
      }),
      ApiOkResponse({
        description: 'User joined group successfully',
      }),
      ApiUnauthorizedResponse({
        description: 'Unauthorized access - valid JWT token required',
      }),
      ApiBadRequestResponse({
        description: 'User is already a member of the group',
      }),
      ApiNotFoundResponse({
        description: 'Group not found',
      }),
    );
  }

  static removeUserFromGroupDocs() {
    return applyDecorators(
      ApiBearerAuth(),
      ApiOperation({
        summary: 'Remove a user from a group',
        description:
          'Removes a user from a specific group. Only the group owner or the user themselves can perform this action.',
      }),
      ApiParam({
        name: 'groupId',
        description: 'ID of the group to remove the user from',
        type: 'string',
      }),
      ApiParam({
        name: 'userId',
        description: 'ID of the user to remove from the group',
        type: 'string',
      }),
      ApiOkResponse({
        description: 'User removed from group successfully',
      }),
      ApiUnauthorizedResponse({
        description: 'Unauthorized access - valid JWT token required',
      }),
      ApiForbiddenResponse({
        description: 'Only the group owner or the user themselves can remove a user from the group',
      }),
      ApiNotFoundResponse({
        description: 'Group not found or user is not a member of the group',
      }),
    );
  }
}
