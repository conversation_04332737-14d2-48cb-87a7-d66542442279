import React, { useContext } from "react";
import { createContext, ReactNode, useState } from "react";
import { ToastWrapper } from "../components";

interface ToastContextProps {
  setToastContent: (content: ReactNode | null) => void;
}

export const ToastContext = createContext<ToastContextProps>({
  setToastContent: () => {},
});

export const ToastProvider = ({ children }: { children: ReactNode }) => {
  const [toastContent, setToastContent] = useState<ReactNode | null>(null);

  return (
    <ToastContext.Provider value={{ setToastContent }}>
      {children}
      <ToastWrapper>{toastContent}</ToastWrapper>
    </ToastContext.Provider>
  );
};

export const useToast = () => {
  const { setToastContent } = useContext(ToastContext);

  const showToast = (content: ReactNode) => {
    setToastContent(content);
  };

  const hideToast = () => {
    setToastContent(null);
  };

  return {
    showToast,
    hideToast,
  };
};
