import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-local';
import { authMessages } from '@app/constants/messages/auth-messages';
import { AuthService } from '@app/auth/services/auth.service';

@Injectable()
export class LocalStrategy extends PassportStrategy(Strategy, 'local') {
  constructor(private readonly authService: AuthService) {
    super({ usernameField: 'email' });
  }

  async validate(email: string, password: string) {
    try {
      return await this.authService.verifyUser(email, password);
    } catch {
      throw new HttpException(authMessages.user.invalidCredentials, HttpStatus.UNAUTHORIZED);
    }
  }
}
