import React from "react";
import { View, StyleSheet } from "react-native";
import { CameraView as ExpoCameraView } from "expo-camera";

interface Props {
  scanned: boolean;
  handleBarCodeScanned: any;
}

export const CameraView = ({ scanned, handleBarCodeScanned }: Props) => {
  return (
    <View style={styles.container}>
      <ExpoCameraView 
        style={styles.camera}
        facing="back"
        barcodeScannerSettings={{
          barcodeTypes: ["qr"],
        }}
        onBarcodeScanned={scanned ? undefined : handleBarCodeScanned}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    borderRadius: 8,
    overflow: "hidden",
    justifyContent: "center",
  },
  camera: {
    width: "100%",
    height: "100%",
  },
});
