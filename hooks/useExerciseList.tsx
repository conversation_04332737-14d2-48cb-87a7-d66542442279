import { getAllExercises } from "@/api/exercise";
import { useExerciseStore } from "@/stores/useExerciseStore";
import { useQuery } from "@tanstack/react-query";
import { useEffect } from "react";
import { useErrorHandling } from "./useErrorHandling";

export const useExerciseListQuery = () => {
  const { setExercises } = useExerciseStore();
  const { withErrorHandling } = useErrorHandling();

  const {
    data: exercises,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ["exercises"],
    queryFn: async () => {
      const result = await withErrorHandling(async () => {
        return await getAllExercises();
      });
      return result || [];
    },
    retry: false, // Wyłączamy retry bo błędy obsługuje withErrorHandling
  });

  useEffect(() => {
    if (exercises) {
      setExercises(exercises);
    }
  }, [exercises, setExercises]);

  const handleRefetch = () =>
    withErrorHandling(async () => {
      await refetch();
    });

  return {
    exerciseList: exercises || [],
    loading: isLoading,
    refetch: handleRefetch,
  };
};
