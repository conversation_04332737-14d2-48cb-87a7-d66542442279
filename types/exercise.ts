import { DifficultyLevel } from "@/enums/difficultyLevel";
import { ActivityStatus } from "@/enums/activityStatus";

export type NewExerciseRequestPayload = {
  name: string;
  description: string;
  tags: string;
  difficultyLevel: DifficultyLevel;
  videoUrl: string;
  imageUrl: string;
  instructions: string;
};

export type NewExerciseResponse = {
  id: string;
  name: string;
  description: string;
  tags: string;
  difficultyLevel: DifficultyLevel;
  videoUrl: string;
  imageUrl: string;
  instructions: string;
  status: ActivityStatus;
  statusMessage: string | null;
  createdAt: string;
  updatedAt: string;
  creatorId: string;
};

export type DeleteExerciseResponse = {
  id: string;
  status: ActivityStatus;
};
