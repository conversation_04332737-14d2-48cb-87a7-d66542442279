import { <PERSON><PERSON><PERSON><PERSON>, IEventHandler } from '@nestjs/cqrs';
import { OtcGeneratedEvent } from '@app/users/events/otc-generated.event';
import { MailService } from '@app/mail/services/mail.service';

@EventsHandler(OtcGeneratedEvent)
export class SendOtcEmailHandler implements IEventHandler<OtcGeneratedEvent> {
  constructor(private readonly mailService: MailService) {}

  async handle(event: OtcGeneratedEvent) {
    await this.mailService.sendOtc(event.email, event.code);
  }
}
