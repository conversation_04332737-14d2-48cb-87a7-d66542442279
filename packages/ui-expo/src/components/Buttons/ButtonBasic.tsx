import React, { forwardRef } from "react";
import { TouchableOpacity, Text, StyleSheet, StyleProp, ViewStyle, TextStyle, View } from "react-native";
import { LoaderSpinnerSmall } from "../Loaders";
import { Sizes, buttonSizeStyles } from "../../utils/sizes";
import { useTheme } from "../../theme";

interface Props {
  children?: React.ReactNode;
  size?: Sizes;
  isLoading?: boolean;
  onPress?: () => void;
  disabled?: boolean;
  style?: StyleProp<ViewStyle>;
  textStyle?: StyleProp<TextStyle>;
}

export const ButtonBasic = forwardRef<TouchableOpacity, Props>((props, ref) => {
  const { children, isLoading, size = Sizes.Medium, onPress, disabled, style, textStyle } = props;

  const { theme } = useTheme();
  const sizeStyle = buttonSizeStyles[size];

  return (
    <TouchableOpacity
      ref={ref}
      style={[
        styles.button,
        sizeStyle,
        {
          backgroundColor: disabled ? theme.colors.grey[400] : theme.colors.accent[300],
          borderRadius: theme.borderRadius.lg,
        },
        style,
      ]}
      onPress={onPress}
      disabled={disabled || isLoading}
    >
      {isLoading ? (
        <LoaderSpinnerSmall />
      ) : (
        <Text
          style={[
            styles.text,
            {
              color: disabled ? theme.colors.grey[500] : theme.colors.grey[900],
            },
            textStyle,
          ]}
        >
          {children || "Click"}
        </Text>
      )}
    </TouchableOpacity>
  );
});

const styles = StyleSheet.create({
  button: {
    paddingHorizontal: 24,
    width: "100%",
    alignItems: "center",
    justifyContent: "center",
  },
  text: {
    fontSize: 16,
  },
});
