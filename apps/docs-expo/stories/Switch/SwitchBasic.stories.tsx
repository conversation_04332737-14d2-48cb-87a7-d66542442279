import React, { useState } from "react";
import { View, Text } from "react-native";
import type { Meta, StoryObj } from "@storybook/react";
import { SwitchBasic } from "@vs/kit-ui-expo";

const meta = {
  title: "Components/Switch/SwitchBasic",
  component: SwitchBasic,
  argTypes: {
    onValueChange: { action: "switch value changed" },
    size: {
      control: "select",
      options: ["small", "medium", "large"],
    },
  },
} satisfies Meta<typeof SwitchBasic>;

export default meta;
type Story = StoryObj<typeof SwitchBasic>;

export const Default: Story = {
  render: () => {
    const [value, setValue] = useState(false);
    
    return (
      <View style={{ padding: 20 }}>
        <View style={{ marginBottom: 30 }}>
          <Text style={{ marginBottom: 10, fontSize: 18, fontWeight: "bold" }}>Small Size</Text>
          <View style={{ flexDirection: "row", justifyContent: "space-around", marginBottom: 20 }}>
            <SwitchBasic value={false} size="small" />
            <SwitchBasic value={true} size="small" />
            <SwitchBasic value={false} disabled size="small" />
            <SwitchBasic value={true} disabled size="small" />
          </View>
        </View>
        
        <View style={{ marginBottom: 30 }}>
          <Text style={{ marginBottom: 10, fontSize: 18, fontWeight: "bold" }}>Medium Size (Default)</Text>
          <View style={{ flexDirection: "row", justifyContent: "space-around", marginBottom: 20 }}>
            <SwitchBasic value={false} />
            <SwitchBasic value={true} />
            <SwitchBasic value={false} disabled />
            <SwitchBasic value={true} disabled />
          </View>
        </View>
        
        <View style={{ marginBottom: 30 }}>
          <Text style={{ marginBottom: 10, fontSize: 18, fontWeight: "bold" }}>Large Size</Text>
          <View style={{ flexDirection: "row", justifyContent: "space-around", marginBottom: 20 }}>
            <SwitchBasic value={false} size="large" />
            <SwitchBasic value={true} size="large" />
            <SwitchBasic value={false} disabled size="large" />
            <SwitchBasic value={true} disabled size="large" />
          </View>
        </View>
        
        <View style={{ marginTop: 40 }}>
          <Text style={{ marginBottom: 10, fontSize: 18, fontWeight: "bold" }}>Interactive Example</Text>
          <SwitchBasic 
            value={value} 
            onValueChange={setValue} 
          />
        </View>
      </View>
    );
  },
};