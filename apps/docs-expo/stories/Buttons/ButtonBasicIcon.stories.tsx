import React from "react";
import type { Meta } from "@storybook/react";
import { BurgerIcon } from "@vs/kit-ui-assets";
import { ButtonBasicIcon, ThemeProvider, Sizes } from "@vs/kit-ui-expo";
import { View } from "react-native";

const meta = {
  title: "Components/Buttons/ButtonBasicIcon",
  component: ButtonBasicIcon,
  decorators: [
    Story => (
      <ThemeProvider>
        <View style={{ padding: 16, alignItems: "flex-start", width: "100%" }}>
          <Story />
        </View>
      </ThemeProvider>
    ),
  ],
} satisfies Meta<typeof ButtonBasicIcon>;

export default meta;

export const Default = () => {
  return <ButtonBasicIcon size={Sizes.Medium}>Basic Button</ButtonBasicIcon>;
};

export const Small = () => {
  return <ButtonBasicIcon size={Sizes.Small}>Small Button</ButtonBasicIcon>;
};

export const Medium = () => {
  return <ButtonBasicIcon size={Sizes.Medium}>Medium Button</ButtonBasicIcon>;
};

export const Large = () => {
  return <ButtonBasicIcon size={Sizes.Large}>Large Button</ButtonBasicIcon>;
};

export const WithLeftIcon = () => {
  return (
    <ButtonBasicIcon size={Sizes.Medium} leftIcon={<BurgerIcon fill="#000" stroke="transparent" />}>
      Left Icon
    </ButtonBasicIcon>
  );
};

export const WithRightIcon = () => {
  return (
    <ButtonBasicIcon size={Sizes.Medium} rightIcon={<BurgerIcon fill="#000" stroke="transparent" />}>
      Right Icon
    </ButtonBasicIcon>
  );
};

export const WithBothIcons = () => {
  return (
    <ButtonBasicIcon
      size={Sizes.Medium}
      leftIcon={<BurgerIcon fill="#000" stroke="transparent" />}
      rightIcon={<BurgerIcon fill="#000" stroke="transparent" />}
    >
      Both Icons
    </ButtonBasicIcon>
  );
};

export const Loading = () => {
  return (
    <ButtonBasicIcon size={Sizes.Medium} isLoading>
      Loading
    </ButtonBasicIcon>
  );
};

export const Disabled = () => {
  return (
    <ButtonBasicIcon size={Sizes.Medium} disabled>
      Disabled
    </ButtonBasicIcon>
  );
};

export const CustomStyle = () => {
  return (
    <ButtonBasicIcon
      size={Sizes.Medium}
      style={{ backgroundColor: "#ff5722", borderRadius: 25 }}
      textStyle={{ color: "white", fontWeight: "bold" }}
    >
      Custom Style
    </ButtonBasicIcon>
  );
};
