import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsNotEmpty, IsArray } from 'class-validator';
import { TrainingStatus } from '../enums/training-status.enum';
import { UserEntity } from '@app/users/entities/user.entity';
import { ExerciseEntity } from '@app/exercises/entities/exercise.entity';

export class CreateTrainingRequestDto {
  @ApiProperty({
    description: 'Training name',
    example: 'Trening na biceps',
  })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Training description',
    example: 'Trening opis super ciało w 2godziny',
  })
  @IsNotEmpty()
  @IsString()
  description: string;

  @ApiProperty({
    description: 'Exercise IDs to include in the training',
    example: ['exercise_id_1', 'exercise_id_2'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  exercises: string[];

  @ApiProperty({
    description: 'Training tags (separated by |)',
    example: 'na_cale_cialo|i_na_nogi',
    required: false,
  })
  @IsOptional()
  @IsString()
  tags?: string;
}

export class CreateTrainingResponseDto {
  @ApiProperty({
    description: 'Unique training identifier',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Training name',
    example: 'Trening na biceps',
  })
  name: string;

  @ApiProperty({
    description: 'Training description',
    example: 'Trening opis super ciało w 2godziny',
  })
  description: string;

  @ApiProperty({
    description: 'Training tags',
    example: 'na_cale_cialo|i_na_nogi',
    required: false,
  })
  tags: string;

  @ApiProperty({
    description: 'Training status',
    enum: TrainingStatus,
    example: TrainingStatus.ACTIVE,
  })
  status: TrainingStatus;

  @ApiProperty({
    description: 'Training creation timestamp',
    example: '2023-01-01T12:00:00Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Training last update timestamp',
    example: '2023-01-01T12:00:00Z',
  })
  updatedAt: Date;

  @ApiProperty({
    description: 'Creator of the training',
    type: () => UserEntity,
  })
  creator: UserEntity;

  creatorId: string;

  @ApiProperty({
    description: 'Exercises included in the training',
    type: () => [ExerciseEntity],
  })
  exercises: ExerciseEntity[];
}
