import React, { useEffect, useState } from "react";
import { View, StyleSheet, KeyboardAvoidingView } from "react-native";
import {
  ButtonBasic,
  ButtonBasicIcon,
  ButtonBorder,
  InputBasic,
  InputTextArea,
  LoaderSpinnerSmall,
  Sizes,
  Theme,
  Typography,
  useTheme,
} from "@vs/kit-ui-expo";
import { Controller } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { useLocalSearchParams } from "expo-router";
import { useNewTraining } from "@/hooks/useNewTraining";
import { useTrainingStore } from "@/stores/useTrainingStore";
import { ActionsAddCircleIcon } from "@vs/kit-ui-assets";
import { useExerciseListQuery } from "@/hooks/useExerciseList";
import { SingleActivityListCardWithCheckbox } from "@/components/Cards";
import { useExerciseStore } from "@/stores/useExerciseStore";
import {
  DragAndDropList,
  DragDropItemProps,
} from "@/components/DragAndDropList";
import { ContainerBottomModalWrapper } from "@/components/ContainerBottomModal";
import FlatListWithSpacing from "@/components/ScrollableWithSpacing/FlatListWithSpacing";

export default function NewTraining() {
  const { id } = useLocalSearchParams();
  const { getTrainingById } = useTrainingStore();
  const { exerciseList, loading } = useExerciseListQuery();
  const { getExerciseById } = useExerciseStore();
  const [isNewInstance, setIsNewInstance] = useState(true);
  const [isAddExercise, setIsAddExercise] = useState(false);
  const { theme } = useTheme();
  const fnStyles = styles(theme);
  const { t } = useTranslation();
  const {
    control,
    errors,
    trainingExercises,
    handleSubmit,
    onSubmit,
    reset,
    handleDeleteTraining,
    setTrainingExercises,
  } = useNewTraining({
    id: id as string,
    isNew: isNewInstance,
  });

  // EFFECTS
  useEffect(() => {
    if (id && typeof id === "string") {
      const training = getTrainingById(id);
      if (training) {
        setIsNewInstance(false);
        reset({
          name: training.name || "",
          description: training.description || "",
          tags: training.tags || "",
          trainingExercises: training.exercises?.map((item) => item.id) || [],
        });
      } else {
        setIsNewInstance(true);
      }
    }
  }, [id, getTrainingById, reset]);

  //VARIABLES AND UTILITIES METHODS
  const elements = trainingExercises
    .map((exercise) => {
      const data = getExerciseById(exercise);
      if (!data) return null;
      return {
        id: data.id,
        title: data.name,
        tags: data.tags.split(","),
      };
    })
    .filter((item): item is DragDropItemProps => item !== undefined);

  const onRemoveItemPress = (id: string) => {
    setTrainingExercises(trainingExercises.filter((item) => item !== id));
  };

  const onReorder = (newOrder: DragDropItemProps[]) => {
    setTrainingExercises(newOrder.map((item) => item.id));
  };

  // RENDER METHODS
  const renderActionButtons = () => {
    if (id && typeof id === "string" && !isNewInstance) {
      return (
        <View style={fnStyles.actionButtons}>
          <ButtonBorder
            onPress={() => handleDeleteTraining(id)}
            size={Sizes.Small}
            style={{ width: 200 }}
            textStyle={{ fontSize: 16 }}
          >
            {t("edit_training_delete_button")}
          </ButtonBorder>
        </View>
      );
    }
  };

  const renderHeader = () => {
    return (
      <View style={fnStyles.header}>
        <Typography variant="Heading4" color={theme.colors.grey[50]}>
          {t("activity_training_list_title")}
        </Typography>
      </View>
    );
  };

  const renderForm = () => (
    <>
      <Controller
        control={control}
        name="name"
        render={({ field: { onChange, onBlur, value } }) => (
          <InputBasic
            onBlur={onBlur}
            onChangeText={onChange}
            value={value}
            label={t("creators_form_new_training_name")}
            placeholder={t("creators_form_placeholder")}
            error={t(errors.name?.message || "")}
          />
        )}
      />
      <Controller
        control={control}
        name="description"
        render={({ field: { onChange, onBlur, value } }) => (
          <InputTextArea
            onBlur={onBlur}
            onChangeText={onChange}
            value={value}
            label={t("creators_form_new_training_description")}
            placeholder={t("creators_form_placeholder")}
            error={t(errors.description?.message || "")}
          />
        )}
      />
      <Controller
        control={control}
        name="tags"
        render={({ field: { onChange, onBlur, value } }) => (
          <InputBasic
            onBlur={onBlur}
            onChangeText={onChange}
            value={value || ""}
            label={t("creators_form_new_training_tags")}
            placeholder={t("creators_form_placeholder")}
            error={t(errors.tags?.message || "")}
          />
        )}
      />
    </>
  );

  const renderSubmitButton = () => {
    return (
      <ButtonBorder
        isLoading={false}
        disabled={false}
        textStyle={{ fontSize: 16 }}
        onPress={handleSubmit(onSubmit)}
      >
        {t("creators_form_new_training_submit_button")}
      </ButtonBorder>
    );
  };

  const renderExcerciseList = () => {
    if (loading) return <LoaderSpinnerSmall />;
    return (
      <FlatListWithSpacing
        data={exerciseList}
        numColumns={2}
        contentContainerStyle={{
          gap: theme.spacing[4],
        }}
        columnWrapperStyle={{
          justifyContent: "space-between",
          columnGap: theme.spacing[4],
        }}
        style={{
          flex: 1,
        }}
        keyExtractor={(item, index) => `excercise-item-${item.id}-${index}`}
        ListEmptyComponent={() => (
          <Typography
            variant="BodyM"
            color={theme.colors.grey[50]}
            style={{ textAlign: "center" }}
          >
            {t("activity_exercise_list_empty")}
          </Typography>
        )}
        renderItem={({ item }) => (
          <SingleActivityListCardWithCheckbox
            title={item.name}
            description={item.description}
            status={item.status}
            checked={trainingExercises.includes(item.id)}
            onChecked={() => {
              setTrainingExercises(
                trainingExercises.includes(item.id)
                  ? trainingExercises.filter((id) => id !== item.id)
                  : [...trainingExercises, item.id]
              );
            }}
            onPress={() => {}}
          />
        )}
      />
    );
  };

  const renderAddExerciseButton = () => {
    return (
      <ButtonBasicIcon
        size={Sizes.Small}
        rightIcon={
          <ActionsAddCircleIcon
            stroke={theme.colors.grey[900]}
            fill="transparent"
          />
        }
        onPress={() => setIsAddExercise(true)}
        style={{
          width: 200,
        }}
      >
        {t("creators_form_new_training_add_excercise")}
      </ButtonBasicIcon>
    );
  };

  const renderFooterComponent = () => {
    return (
      <>
        {errors.trainingExercises && (
          <Typography
            variant="BodyS"
            color={theme.colors.alert[400]}
            style={{ marginTop: theme.spacing[2], textAlign: "center" }}
          >
            {t(errors.trainingExercises.message || "")}
          </Typography>
        )}
      </>
    );
  };

  if (isAddExercise) {
    return (
      <KeyboardAvoidingView
        style={{
          flex: 1,
          backgroundColor: theme.colors.grey[900],
        }}
        behavior={"padding"}
        keyboardVerticalOffset={50}
      >
        <ContainerBottomModalWrapper
          modalChildren={
            <ButtonBasic onPress={() => setIsAddExercise(false)}>
              {t("activity_training_accept_exercise_list")}
            </ButtonBasic>
          }
        >
          <View style={fnStyles.container}>{renderExcerciseList()}</View>
        </ContainerBottomModalWrapper>
      </KeyboardAvoidingView>
    );
  }
  return (
    <KeyboardAvoidingView
      style={{
        flex: 1,
      }}
      behavior={"padding"}
      keyboardVerticalOffset={50}
    >
      <ContainerBottomModalWrapper modalChildren={renderSubmitButton()}>
        <View style={fnStyles.container}>
          <DragAndDropList
            ListHeaderComponent={
              <View style={fnStyles.headerContainer}>
                {renderActionButtons()}
                {renderHeader()}
                {renderForm()}
                {renderAddExerciseButton()}
                <Typography variant="Link" style={fnStyles.instructions}>
                  {t("activity_training_change_list")}
                </Typography>
                {renderFooterComponent()}
              </View>
            }
            elements={elements}
            onRemoveItemPress={onRemoveItemPress}
            onReorder={onReorder}
          />
        </View>
      </ContainerBottomModalWrapper>
    </KeyboardAvoidingView>
  );
}

const styles = (theme: Theme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      padding: theme.spacing[4],
      backgroundColor: theme.colors.grey[900],
    },
    headerContainer: {
      flex: 1,
      gap: theme.spacing[4],
      flexGrow: 1,
    },
    actionButtons: {
      flexDirection: "row",
      justifyContent: "flex-end",
    },
    header: {
      gap: theme.spacing[4],
      borderColor: "white",
      flexDirection: "column",
      alignItems: "center",
      justifyContent: "space-between",
    },
    instructions: {
      fontSize: 12,
      color: theme.colors.grey[50],
      textAlign: "center",
      marginTop: 16,
      paddingBottom: 8,
    },
  });
