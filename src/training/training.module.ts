import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TrainingController } from './controllers/training.controller';
import { TrainingService } from './services/training.service';
import { TrainingEntity } from './entities/training.entity';
import { TrainingExerciseEntity } from './entities/training-exercise.entity';
import { AuthModule } from '../auth/auth.module';
import { ExercisesModule } from '../exercises/exercises.module';

@Module({
  imports: [TypeOrmModule.forFeature([TrainingEntity, TrainingExerciseEntity]), AuthModule, ExercisesModule],
  controllers: [TrainingController],
  providers: [TrainingService],
  exports: [TrainingService],
})
export class TrainingModule {}
