import 'reflect-metadata';
import { INestApplication } from '@nestjs/common';
import request from 'supertest';

import { loginAndAssertUser, registerAndAssertUser } from '../__helpers/auth';
import { buildTestApp } from '../__setup/app.factory';
import { generateTestUser } from '../__helpers/user';
import { ConsentDefinitionEntity } from '@app/consents/entities/consent-definition.entity';
import { createAndAssertConsent, generateGDPRConsentPayload } from '../__helpers/consents';
import { CreateTrainingPlanRequestDto } from '@app/training-plan/dto/create-training-plan.dto';
import { createAndAssertTrainingPlan } from '../__helpers/training-plan';
import { randomUUID } from 'crypto';

describe('/DELETE /v1/training-plan/:id', () => {
  let app: INestApplication;
  let accessToken: string;
  let consent: ConsentDefinitionEntity;

  beforeAll(async () => {
    app = await buildTestApp();

    const consentPayload = generateGDPRConsentPayload();
    consent = await createAndAssertConsent(app, consentPayload);

    const testUser = generateTestUser([consent.id]);
    await registerAndAssertUser(app, testUser);
    accessToken = (await loginAndAssertUser(app, testUser)).accessToken;
  });

  afterAll(async () => {
    await app.close();
  });

  it('should soft delete a training plan', async () => {
    const payload: CreateTrainingPlanRequestDto = {
      name: 'Test Exercise E2E',
      description: 'This is a test exercise',
      trainings: [],
    };

    const trainingPlan = await createAndAssertTrainingPlan(app, payload, accessToken);

    const response = await request(app.getHttpServer())
      .delete(`/v1/training-plan/${trainingPlan.id}`)
      .set('Authorization', `Bearer ${accessToken}`);

    expect(response.status).toBe(200);
    expect(response.body.id).toBe(trainingPlan.id);
    expect(response.body.status).toBe('deleted');
  });

  it('should return 401 if user is not authenticated', async () => {
    const payload: CreateTrainingPlanRequestDto = {
      name: 'Test Exercise E2E',
      description: 'This is a test exercise',
      trainings: [],
    };

    const trainingPlan = await createAndAssertTrainingPlan(app, payload, accessToken);

    const response = await request(app.getHttpServer())
      .delete(`/v1/training-plan/${trainingPlan.id}`)
      .set('Authorization', `Bearer ${accessToken}`);

    expect(response.status).toBe(200);
    expect(response.body.id).toBe(trainingPlan.id);
    expect(response.body.status).toBe('deleted');
  });

  it('should return 404 if training plan does not exist', async () => {
    const id = randomUUID();
    const response = await request(app.getHttpServer())
      .delete(`/v1/training-plan/${id}`)
      .set('Authorization', `Bearer ${accessToken}`);

    expect(response.status).toBe(404);
    expect(response.body.message).toBe('Training plan with ID ' + id + ' not found');
    expect(response.body.error).toBe('Not Found');
    expect(response.body.statusCode).toBe(404);
  });

  it('should return 403 if accessing training plan of another user', async () => {
    const payload: CreateTrainingPlanRequestDto = {
      name: 'Test Exercise E2E',
      description: 'This is a test exercise',
      trainings: [],
    };

    const anotherUser = generateTestUser([consent.id]);

    await registerAndAssertUser(app, anotherUser);
    const anotherUserAccessToken = (await loginAndAssertUser(app, anotherUser)).accessToken;

    const trainingPlan = await createAndAssertTrainingPlan(app, payload, accessToken);

    const response = await request(app.getHttpServer())
      .delete(`/v1/training-plan/${trainingPlan.id}`)
      .set('Authorization', `Bearer ${anotherUserAccessToken}`);

    expect(response.status).toBe(403);
    expect(response.body.message).toBe('You do not have permission to access this training plan');
    expect(response.body.error).toBe('Forbidden');
    expect(response.body.statusCode).toBe(403);
  });
});
