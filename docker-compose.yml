version: '3.9'
services:
  vs-app:
    container_name: vitamin-station-app
    build:
      context: .
      dockerfile: ./Dockerfile
    environment:
      NODE_ENV: ${NODE_ENV}
    env_file:
      - .env
    ports:
      - '3000:3000'
    volumes:
      - .:/usr/src/app:rw,cached
    command: >
      sh -c "if [ ${NODE_ENV} = development ]; 
             then mkdir -p /usr/src/app/dist && rm -rf /usr/src/app/dist/* && npm install && npm run start:dev; 
             else npm install --only=production && node dist/main; 
             fi"
    depends_on:
      - db
    networks:
      - vs-network

  db:
    image: postgres:15
    container_name: vitamin-station-db
    restart: always
    ports:
      - '5495:5432'
    environment:
      - POSTGRES_DB=vs-db
      - POSTGRES_USER=vs-user
      - POSTGRES_PASSWORD=vs-password
    volumes:
      - vs_data:/var/lib/postgresql/data
    networks:
      - vs-network

volumes:
  vs_data:
    driver: 'local'

networks:
  vs-network:
