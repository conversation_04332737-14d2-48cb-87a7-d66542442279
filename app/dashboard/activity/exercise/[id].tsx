import React, { useEffect, useState } from "react";
import { View, StyleSheet, KeyboardAvoidingView } from "react-native";
import {
  ButtonBasicIcon,
  ButtonBorder,
  InputBasic,
  InputSelect,
  InputTextArea,
  Sizes,
  Theme,
  Typography,
  useTheme,
} from "@vs/kit-ui-expo";
import { Controller } from "react-hook-form";
import { useNewExercise } from "@/hooks/useNewExercise";
import { useTranslation } from "react-i18next";
import { ActionsAddCircleIcon } from "@vs/kit-ui-assets";
import { useLocalSearchParams } from "expo-router";
import { useExerciseStore } from "@/stores/useExerciseStore";
import { ContainerBottomModalWrapper } from "@/components/ContainerBottomModal";
import ScrollViewWithSpacing from "@/components/ScrollableWithSpacing/ScrollViewWithSpacing";

export default function NewExercise() {
  const { id } = useLocalSearchParams();
  const { getExerciseById } = useExerciseStore();
  const [isNewInstance, setIsNewInstance] = useState(true);
  const { theme } = useTheme();
  const fnStyles = styles(theme);
  const { t } = useTranslation();
  const {
    control,
    errors,
    handleSubmit,
    onSubmit,
    reset,
    handleDeleteExercise,
  } = useNewExercise({
    id: id as string,
    isNew: isNewInstance,
  });

  useEffect(() => {
    if (id && typeof id === "string") {
      const exercise = getExerciseById(id);
      if (exercise) {
        setIsNewInstance(false);
        reset({
          name: exercise.name || "",
          description: exercise.description || "",
          tags: exercise.tags || "",
          difficultyLevel: exercise.difficultyLevel,
          instructions: exercise.instructions || "",
        });
      } else {
        setIsNewInstance(true);
      }
    }
  }, [id, getExerciseById, reset]);

  const renderActionButtons = () => {
    if (id && typeof id === "string" && !isNewInstance) {
      return (
        <View style={fnStyles.actionButtons}>
          <ButtonBorder
            onPress={() => handleDeleteExercise(id)}
            size={Sizes.Small}
            style={{ width: 200 }}
            textStyle={{ fontSize: 16 }}
          >
            {t("edit_exercise_delete_button")}
          </ButtonBorder>
        </View>
      );
    }
  };

  const renderHeader = () => {
    return (
      <View style={fnStyles.header}>
        <Typography variant="Heading4" color={theme.colors.grey[50]}>
          {t("activity_exercise_list_title")}
        </Typography>
      </View>
    );
  };

  const renderForm = () => (
    <>
      <Controller
        control={control}
        name="name"
        render={({ field: { onChange, onBlur, value } }) => (
          <InputBasic
            onBlur={onBlur}
            onChangeText={onChange}
            value={value}
            label={t("creators_form_new_exercise_name")}
            placeholder={t("creators_form_placeholder")}
            error={t(errors.name?.message || "")}
          />
        )}
      />
      <Controller
        control={control}
        name="description"
        render={({ field: { onChange, onBlur, value } }) => (
          <InputBasic
            onBlur={onBlur}
            onChangeText={onChange}
            value={value}
            label={t("creators_form_new_exercise_description")}
            placeholder={t("creators_form_placeholder")}
            error={t(errors.description?.message || "")}
          />
        )}
      />
      <Controller
        control={control}
        name="difficultyLevel"
        render={({ field: { onChange, onBlur, value } }) => (
          <InputSelect
            onBlur={onBlur}
            options={[
              { label: "Easy", value: "easy" },
              { label: "Medium", value: "medium" },
              { label: "Hard", value: "hard" },
            ]}
            onChangeText={onChange}
            value={value || ""}
            label={t("creators_form_new_exercise_level")}
            placeholder={t("creators_form_placeholder")}
            error={t(errors.difficultyLevel?.message || "")}
          />
        )}
      />
      <Controller
        control={control}
        name="tags"
        render={({ field: { onChange, onBlur, value } }) => (
          <InputBasic
            onBlur={onBlur}
            onChangeText={onChange}
            value={value || ""}
            label={t("creators_form_new_exercise_tags")}
            placeholder={t("creators_form_placeholder")}
            error={t(errors.tags?.message || "")}
          />
        )}
      />
      <Controller
        control={control}
        name="instructions"
        render={({ field: { onChange, onBlur, value } }) => (
          <InputTextArea
            onBlur={onBlur}
            onChangeText={onChange}
            value={value || ""}
            label={t("creators_form_new_exercise_instructions")}
            placeholder={t("creators_form_placeholder")}
            error={t(errors.instructions?.message || "")}
          />
        )}
      />
    </>
  );

  const renderSubmitButton = () => {
    return (
      <ButtonBorder
        isLoading={false}
        disabled={false}
        textStyle={{ fontSize: 16 }}
        onPress={handleSubmit(onSubmit)}
      >
        {t("creators_form_new_exercise_submit_button")}
      </ButtonBorder>
    );
  };

  const renderAddMultimediaButton = () => {
    return (
      <ButtonBasicIcon
        size={Sizes.Small}
        rightIcon={
          <ActionsAddCircleIcon
            stroke={theme.colors.grey[900]}
            fill="transparent"
          />
        }
        style={{
          width: 200,
        }}
      >
        {t("creators_form_new_exercise_add_media")}
      </ButtonBasicIcon>
    );
  };

  return (
    <KeyboardAvoidingView
      style={{
        flex: 1,
      }}
      behavior={"padding"}
      keyboardVerticalOffset={50}
    >
      <ContainerBottomModalWrapper modalChildren={renderSubmitButton()}>
        <ScrollViewWithSpacing
          contentContainerStyle={fnStyles.container}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          {renderActionButtons()}
          {renderHeader()}
          {renderForm()}
          {renderAddMultimediaButton()}
        </ScrollViewWithSpacing>
      </ContainerBottomModalWrapper>
    </KeyboardAvoidingView>
  );
}

const styles = (theme: Theme) =>
  StyleSheet.create({
    container: {
      padding: theme.spacing[4],
      gap: theme.spacing[4],
      backgroundColor: theme.colors.grey[900],
      flexGrow: 1,
    },
    actionButtons: {
      flexDirection: "row",
      justifyContent: "flex-end",
    },
    header: {
      gap: theme.spacing[4],
      borderColor: "white",
      flexDirection: "column",
      alignItems: "center",
      justifyContent: "space-between",
    },
  });
