import { ErrorRegisterFlow } from "@/errors/ErrorRegisterFlow";
import axiosInstance from "@/middleware/axiosInstance";
import {
  LoginRequestPayload,
  LoginResponse,
  RegisterRequestPayload,
  RegisterResponse,
} from "@/types/authorization";

export const registerRequest = async (
  payload: RegisterRequestPayload
): Promise<RegisterResponse> => {
  try {
    const response = await axiosInstance.post("/user/create", payload);
    return response.data;
  } catch (error) {
    throw new ErrorRegisterFlow(error);
  }
};

export const loginRequest = async (
  payload: LoginRequestPayload
): Promise<LoginResponse> => {
  try {
    const response = await axiosInstance.post("/user/login", payload);
    return response.data;
  } catch (error) {
    throw new ErrorRegisterFlow(error);
  }
};
