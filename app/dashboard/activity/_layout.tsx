import { Stack } from "expo-router";
import { RoleSwitcherProvider } from "@/contexts/RoleSwitcherProvider";
import { EUserRole } from "@/enums/roles";
import React from "react";

export default function TrainerLayout() {
  return (
    <RoleSwitcherProvider defaultRole={EUserRole.TRAINER}>
      <Stack
        screenOptions={{
          headerShown: false,
        }}
      >
        <Stack.Screen name="index" />
        <Stack.Screen name="excercise" />
        <Stack.Screen name="training" />
        <Stack.Screen name="training-plan" />
      </Stack>
    </RoleSwitcherProvider>
  );
}
