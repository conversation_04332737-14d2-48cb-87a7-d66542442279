import 'reflect-metadata';
import { INestApplication } from '@nestjs/common';
import request from 'supertest';

import { loginAndAssertUser, registerAndAssertUser } from '../__helpers/auth';
import { buildTestApp } from '../__setup/app.factory';
import { generateTestUser } from '../__helpers/user';
import { CreateTrainingRequestDto } from '@app/training/dto/create-training.dto';
import { randomUUID } from 'crypto';
import { CreateExerciseRequestDto } from '@app/exercises/dto/create-exercise.dto';
import { DifficultyLevel } from '@app/exercises/enums/difficulty-level.enum';
import { createAndAssertExercise } from '../__helpers/exercise';
import { generateGDPRConsentPayload } from '../__helpers/consents';
import { createAndAssertConsent } from '../__helpers/consents';
import { ConsentDefinitionEntity } from '@app/consents/entities/consent-definition.entity';

describe('/POST /v1/training', () => {
  let app: INestApplication;
  let accessToken: string;
  let consent: ConsentDefinitionEntity;

  beforeAll(async () => {
    app = await buildTestApp();

    const consentPayload = generateGDPRConsentPayload();
    consent = await createAndAssertConsent(app, consentPayload);

    const testUser = generateTestUser([consent.id]);
    await registerAndAssertUser(app, testUser);
    accessToken = (await loginAndAssertUser(app, testUser)).accessToken;
  });

  afterAll(async () => {
    await app.close();
  });

  it('should create a training with valid exercises', async () => {
    const exercisePayload: CreateExerciseRequestDto = {
      name: 'Test Exercise E2E',
      description: 'This is a test exercise',
      tags: 'e2e|test',
      difficultyLevel: DifficultyLevel.MEDIUM,
      instructions: 'Step 1: … Step 2: …',
    };

    const exercise = await createAndAssertExercise(app, exercisePayload, accessToken);

    const trainingPayload: CreateTrainingRequestDto = {
      name: 'Test training name',
      description: 'Test training description',
      exercises: [exercise.id],
      tags: '',
    };

    const response = await request(app.getHttpServer())
      .post('/v1/training')
      .set('Authorization', `Bearer ${accessToken}`)
      .send(trainingPayload);

    expect(response.status).toBe(201);
    expect(response.body).toMatchObject({
      name: trainingPayload.name,
      description: trainingPayload.description,
      tags: trainingPayload.tags,
      status: 'active',
      exercises: [
        {
          createdAt: expect.any(String),
          creatorId: expect.any(String),
          description: expect.any(String),
          difficultyLevel: expect.any(String),
          id: expect.any(String),
          name: expect.any(String),
          status: expect.any(String),
          tags: expect.any(String),
          updatedAt: expect.any(String),
        },
      ],
    });
    expect(response.body.id).toMatch(/^[\w-]{36}$/);
    expect(response.body.createdAt).toBeDefined();
    expect(response.body.updatedAt).toBeDefined();
    expect(response.body.creatorId).toBeDefined();
  });

  it('should fail creating training with non-exisintg exercise', async () => {
    const randomID = randomUUID();
    const trainingPayload: CreateTrainingRequestDto = {
      name: 'Test training name',
      description: 'Test training description',
      exercises: [randomID],
      tags: '',
    };

    const response = await request(app.getHttpServer())
      .post('/v1/training')
      .set('Authorization', `Bearer ${accessToken}`)
      .send(trainingPayload);

    expect(response.status).toBe(400);
    expect(response.body.message.startsWith('One or more exercises do not exist')).toBe(true);
  });

  it('should return 401 if user is not authenticated', async () => {
    const trainingPayload: CreateTrainingRequestDto = {
      name: 'Test training name',
      description: 'Test training description',
      exercises: [],
      tags: '',
    };

    const response = await request(app.getHttpServer()).post('/v1/training').send(trainingPayload);

    expect(response.status).toBe(401);
    expect(response.body.message).toBe('Unauthorized');
  });
});
