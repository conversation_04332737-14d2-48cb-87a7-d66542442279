import { View, StyleSheet, Text } from "react-native";
import { ButtonBorder, ButtonBasic, theme } from "@vs/kit-ui-expo";
import { useTranslation } from "react-i18next";

interface Props {
  isLast: boolean;
  onNext: () => void;
}

export const ButtonSection = ({ isLast, onNext }: Props) => {
  const { t } = useTranslation();

  return (
    <View style={styles.buttonContainer}>
      {isLast ? (
        <ButtonBasic onPress={onNext}>
          <Text style={styles.lastButtonText}>
            {t("onboarding_start_button")}
          </Text>
        </ButtonBasic>
      ) : (
        <ButtonBorder onPress={onNext}>
          <Text style={styles.nextButtonText}>
            {t("onboarding_next_button")}
          </Text>
        </ButtonBorder>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  buttonContainer: {
    backgroundColor: theme.colors.grey[800],
    height: 120,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    alignItems: "center",
    justifyContent: "center",
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    paddingHorizontal: 24,
    marginHorizontal: 0,
    zIndex: 10,
  },
  nextButtonText: {
    color: "#ffffff",
  },
  lastButtonText: {
    color: theme.colors.grey[900],
  },
});
