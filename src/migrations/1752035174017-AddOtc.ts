import { MigrationInterface, QueryRunner } from "typeorm";

export class AddOtc1752035174017 implements MigrationInterface {
    name = 'AddOtc1752035174017'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "user_otcs" ("email" character varying NOT NULL, "code" character varying NOT NULL, "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "expiresAt" TIMESTAMP WITH TIME ZONE NOT NULL, CONSTRAINT "PK_1e3f2fa8905fc6611b29a2d41cd" PRIMARY KEY ("email"))`);
        await queryRunner.query(`ALTER TABLE "users" ADD "isActive" boolean NOT NULL DEFAULT true`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "isActive"`);
        await queryRunner.query(`DROP TABLE "user_otcs"`);
    }

}
