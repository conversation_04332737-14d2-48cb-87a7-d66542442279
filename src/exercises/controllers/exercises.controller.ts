import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
  Put,
  Query,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import { ExercisesService } from '../services/exercises.service';
import { CreateExerciseRequestDto, CreateExerciseResponseDto } from '../dto/create-exercise.dto';
import { UpdateExerciseRequestDto, UpdateExerciseResponseDto } from '../dto/update-exercise.dto';
// import { TrainerGuard } from '../guards/trainer.guard';
import { ExerciseStatus } from '../enums/exercise-status.enum';
import { JwtAuthGuard } from '@app/tools/guards/jwt-auth.guard';
import { ExercisesSwaggerDocs } from '../docs/exercises.docs';
import { EXERCISE_ROUTES } from '@app/constants/routes/excercises-routes-names';
import { UserEntity } from '@app/users/entities/user.entity';
import { CurrentBaseUser } from '@app/tools/decorators/current-user.decorator';
import { GetExerciseResponseDto } from '../dto/get-exercise.dto';

@ExercisesSwaggerDocs.apiTags()
@Controller({ version: '1' })
@UseGuards(JwtAuthGuard /*TrainerGuard -> temp commented due to missing user creation with trainer role*/)
@ExercisesSwaggerDocs.apiBearerAuth()
export class ExercisesController {
  constructor(private readonly exercisesService: ExercisesService) {}

  @Post(EXERCISE_ROUTES.EXCERCISE)
  @HttpCode(HttpStatus.CREATED)
  @ExercisesSwaggerDocs.createExerciseDocs()
  async create(
    @Body() data: CreateExerciseRequestDto,
    @CurrentBaseUser() user: UserEntity,
  ): Promise<CreateExerciseResponseDto> {
    return this.exercisesService.create(data, user);
  }

  @Get(EXERCISE_ROUTES.EXCERCISE)
  @HttpCode(HttpStatus.OK)
  @ExercisesSwaggerDocs.findAllExercisesDocs()
  async findAll(
    @Query('status') status: ExerciseStatus | undefined,
    @CurrentBaseUser() user: UserEntity,
  ): Promise<GetExerciseResponseDto[]> {
    return this.exercisesService.findAll(user, status);
  }

  @Get(EXERCISE_ROUTES.SINGLE_EXERCISE)
  @HttpCode(HttpStatus.OK)
  @ExercisesSwaggerDocs.findOneExerciseDocs()
  async findOne(@Param('id') id: string, @CurrentBaseUser() user: UserEntity): Promise<GetExerciseResponseDto> {
    return this.exercisesService.findOne(id, user);
  }

  @Put(EXERCISE_ROUTES.SINGLE_EXERCISE)
  @HttpCode(HttpStatus.OK)
  @ExercisesSwaggerDocs.updateExerciseDocs()
  async update(
    @Param('id') id: string,
    @Body() data: UpdateExerciseRequestDto,
    @CurrentBaseUser() user: UserEntity,
  ): Promise<UpdateExerciseResponseDto> {
    return this.exercisesService.update(id, data, user);
  }

  @Delete(EXERCISE_ROUTES.SINGLE_EXERCISE)
  @HttpCode(HttpStatus.OK)
  @ExercisesSwaggerDocs.removeExerciseDocs()
  async remove(@Param('id') id: string, @CurrentBaseUser() user: UserEntity) {
    return this.exercisesService.remove(id, user);
  }
}
