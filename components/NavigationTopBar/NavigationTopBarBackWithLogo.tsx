import { View, Image, TouchableOpacity } from "react-native";
import { NavigationArrowBackBrokeIcon } from "@vs/kit-ui-assets";
import { Theme, useTheme } from "@vs/kit-ui-expo";
import { StyleSheet } from "react-native";
import { useRouter } from "expo-router";

export const NavigationTopBarBackWithLogo = () => {
  const { theme } = useTheme();
  const styles = stylesConfig(theme);
  const router = useRouter();

  const handleBack = () => {
    router.back();
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity onPress={handleBack} style={styles.icon} hitSlop={32}>
        <NavigationArrowBackBrokeIcon
          fill={"transparent"}
          stroke={theme.colors.grey[50]}
          width={24}
          height={24}
        />
      </TouchableOpacity>
      <Image
        source={require("../../assets/images/logo.png")}
        style={styles.logo}
        resizeMode="contain"
      />
    </View>
  );
};

const stylesConfig = (theme: Theme) =>
  StyleSheet.create({
    container: {
      height: 64,
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      backgroundColor: theme.colors.grey[900],
    },
    icon: {
      flex: 1,
      position: "absolute",
      left: theme.spacing[4],
    },
    logo: {
      width: 64,
      height: 64,
    },
  });
