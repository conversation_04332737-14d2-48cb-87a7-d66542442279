import { theme, Typography } from "@vs/kit-ui-expo";
import React from "react";
import { View } from "react-native";

export default {
  title: "Components/Typography/Typography",
  component: Typography,
};

export const AllVariants = () => (
  <View style={{ gap: 16 }}>
    <Typography variant="Display1">Display 1</Typography>
    <Typography variant="Heading1">Heading 1</Typography>
    <Typography variant="Heading2">Heading 2</Typography>
    <Typography variant="Heading3">Heading 3</Typography>
    <Typography variant="Heading4">Heading 4</Typography>
    <Typography variant="Heading5">Heading 5</Typography>
    <Typography variant="BodyL">Body L</Typography>
    <Typography variant="BodyM">Body M</Typography>
    <Typography variant="BodyS">Body S</Typography>
    <Typography variant="BodyXS">Body XS</Typography>
    <Typography variant="SubtitleL">Subtitle L</Typography>
    <Typography variant="SubtitleM">Subtitle M</Typography>
    <Typography variant="SubtitleS">Subtitle S</Typography>
    <Typography variant="SubtitleXS">Subtitle XS</Typography>
    <Typography variant="Link">Link</Typography>
  </View>
);

export const WithCustomColor = () => (
  <Typography variant="Heading1" color={theme.colors.accent[300]}>
    Colored Heading
  </Typography>
);

export const WithCustomStyle = () => (
  <Typography
    variant="BodyL"
    style={{
      textAlign: "center",
      textDecorationLine: "underline",
    }}
  >
    Custom Styled Text
  </Typography>
);
