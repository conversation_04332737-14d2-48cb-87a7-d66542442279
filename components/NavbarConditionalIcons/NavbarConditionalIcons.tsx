import { View } from "react-native";
import { useTheme } from "@vs/kit-ui-expo";

import {
  NavigationChefHatMinimalisticIcon,
  NavigationChefHatMinimalisticOutlineIcon,
  NavigationDumbbellLargeMinimalisticIcon,
  NavigationDumbbellLargeMinimalisticOutlineIcon,
  NavigationHomeAngleIcon,
  NavigationHomeAngleOutlineIcon,
  NavigationSettingsIcon,
  NavigationSettingsOutlineIcon,
  NavigationUserIcon,
  NavigationUserOutlineIcon,
} from "@vs/kit-ui-assets";

export type TabIconProps = {
  color: string;
  focused: boolean;
};

export const HomeIcon = ({ color, focused }: TabIconProps) =>
  focused ? (
    <NavigationHomeAngleIcon fill={color} />
  ) : (
    <NavigationHomeAngleOutlineIcon fill={color} />
  );

export const TrainerIcon = ({ color, focused }: TabIconProps) =>
  focused ? (
    <NavigationDumbbellLargeMinimalisticIcon fill={color} />
  ) : (
    <NavigationDumbbellLargeMinimalisticOutlineIcon fill={color} />
  );

export const DietitianIcon = ({ color, focused }: TabIconProps) =>
  focused ? (
    <NavigationChefHatMinimalisticIcon fill={color} />
  ) : (
    <NavigationChefHatMinimalisticOutlineIcon fill={color} />
  );

export const SettingsIcon = ({ color, focused }: TabIconProps) =>
  focused ? (
    <NavigationSettingsIcon fill={color} />
  ) : (
    <NavigationSettingsOutlineIcon fill={color} />
  );

export const UserIcon = ({ color, focused }: TabIconProps) =>
  focused ? (
    <NavigationUserIcon fill={color} />
  ) : (
    <NavigationUserOutlineIcon fill={color} />
  );

/**
 * Centralized home button with accent background for User role
 */
export const HomeAccentIcon = ({ focused }: TabIconProps) => {
  const { theme } = useTheme();
  const { colors, borderRadius } = theme;

  return (
    <View
      style={{
        width: 50,
        height: 50,
        alignItems: "center",
        justifyContent: "center",
        borderRadius: borderRadius.full,
        backgroundColor: colors.accent[300],
      }}
    >
      {focused ? (
        <NavigationHomeAngleIcon fill="black" />
      ) : (
        <NavigationHomeAngleOutlineIcon fill="black" />
      )}
    </View>
  );
};
