import React from "react";
import { View, StyleSheet, Image } from "react-native";
import {
  LinkBasic,
  LinkBorder,
  LinkBorderIcon,
  Theme,
  Typography,
  useTheme,
} from "@vs/kit-ui-expo";
import { ForgotPasswordLink } from "@/components/ForgotPasswordLink";
import { SecurityQrCodeIcon } from "@vs/kit-ui-assets";
import { useTranslation } from "react-i18next";
import { ContainerBottomModalWithAuthLayout } from "@/components/ContainerBottomModal";

export default function StartScreen() {
  const { theme } = useTheme();
  const styles = stylesConfig(theme);
  const { t } = useTranslation();

  const renderHeader = () => {
    return (
      <View style={styles.logoContainer}>
        <Typography
          variant="Heading1"
          color={theme.colors.grey[50]}
          style={styles.title}
        >
          {t("start_screen_title")}
        </Typography>
        <Image
          source={require("../assets/images/logo.png")}
          style={styles.logoText}
          resizeMode="contain"
        />
        <Typography
          variant="BodyM"
          color={theme.colors.grey[50]}
          style={styles.infoText}
        >
          {t("start_screen_description")}
        </Typography>
      </View>
    );
  };

  const renderButtonsModal = () => {
    return (
      <ContainerBottomModalWithAuthLayout>
        {/* <LanguageSelector /> */}
        <LinkBasic href="/login">{t("start_screen_button_login")}</LinkBasic>
        <LinkBorder href="/register">
          {t("start_screen_button_register")}
        </LinkBorder>
        <ForgotPasswordLink />
        <LinkBorderIcon
          href="/qr-scanner"
          rightIcon={<SecurityQrCodeIcon fill={theme.colors.grey[50]} />}
        >
          {t("start_screen_button_qr")}
        </LinkBorderIcon>
        {/* <LinkBorder href="/onboarding">
          {t("start_screen_button_onboarding")}
        </LinkBorder> */}
      </ContainerBottomModalWithAuthLayout>
    );
  };

  return (
    <View
      style={[styles.container, { backgroundColor: theme.colors.grey[900] }]}
    >
      {renderHeader()}
      {renderButtonsModal()}
    </View>
  );

  // ---- Temporary redirect to dashboard menu ----
  // return <Redirect href="/(app)/dietitian" />;
  // ---- Temporary redirect to dashboard menu ----
}

const stylesConfig = (theme: Theme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      justifyContent: "space-between",
    },
    logoContainer: {
      flex: 1,
      alignItems: "center",
      justifyContent: "center",
    },
    logoText: {
      width: 240,
      height: 240,
      marginTop: theme.spacing[2],
    },
    title: {
      textAlign: "center",
    },
    infoContainer: {
      alignItems: "center",
      marginBottom: theme.spacing[10],
    },
    infoText: {
      textAlign: "center",
      paddingHorizontal: theme.spacing[16],
      lineHeight: theme.typography.BodyM.lineHeight,
    },
  });
