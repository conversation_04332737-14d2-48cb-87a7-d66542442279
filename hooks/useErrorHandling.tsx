import { ErrorClassFields } from "@/errors/types";
import { buildErrorMessage } from "@/utils/buildErrorMessage";
import { ToastError, useToast, ToastProps } from "@vs/kit-ui-expo";

export const useErrorHandling = () => {
  const { showToast } = useToast();

  const withErrorHandling = async <T,>(
    operation: () => Promise<T>,
    extraOnError?: () => void
  ): Promise<T | undefined> => {
    try {
      return await operation();
    } catch (error) {
      const errorMessage: ToastProps = buildErrorMessage(
        error as ErrorClassFields
      );
      showToast(<ToastError {...errorMessage} />);

      if (extraOnError) {
        extraOnError();
      }
    }
  };

  return {
    withErrorHandling,
  };
};
