import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
  Put,
  Query,
  Request,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import { TrainingPlanService } from '../services/training-plan.service';
import { CreateTrainingPlanRequestDto, CreateTrainingPlanResponseDto } from '../dto/create-training-plan.dto';
import { UpdateTrainingPlanRequestDto, UpdateTrainingPlanResponseDto } from '../dto/update-training-plan.dto';
import { TrainingPlanStatus } from '../enums/training-plan-status.enum';
import { JwtAuthGuard } from '@app/tools/guards/jwt-auth.guard';
// import { TrainerGuard } from '@app/training-plan/guards/trainer.guard';
import { TrainingPlanSwaggerDocs } from '../docs/training-plan.docs';
import { AssignTrainingPlanToGroupDto } from '../dto/assign-training-plan-to-group.dto';
import { TrainingPlanGroupResponseDto } from '../dto/training-plan-group-response.dto';
import { TRAINING_PLAN_ROUTES } from '@app/constants/routes/training-plan-routes-names';
import { CurrentBaseUser } from '@app/tools/decorators/current-user.decorator';
import { UserEntity } from '@app/users/entities/user.entity';
import { GetTrainingPlanResponseDto } from '../dto/get-training-plan.dto';

@TrainingPlanSwaggerDocs.apiTags()
@Controller({ version: '1' })
@UseGuards(JwtAuthGuard /*TrainerGuard -> temp commented due to missing user creation with trainer role*/)
@TrainingPlanSwaggerDocs.apiBearerAuth()
export class TrainingPlanController {
  constructor(private readonly trainingPlanService: TrainingPlanService) {}

  @Post(TRAINING_PLAN_ROUTES.TRAINING_PLAN)
  @HttpCode(HttpStatus.CREATED)
  @TrainingPlanSwaggerDocs.createTrainingPlanDocs()
  async create(
    @Body() data: CreateTrainingPlanRequestDto,
    @CurrentBaseUser() user: UserEntity,
  ): Promise<CreateTrainingPlanResponseDto> {
    return this.trainingPlanService.createTrainingPlan(data, user);
  }

  @Get(TRAINING_PLAN_ROUTES.TRAINING_PLAN)
  @HttpCode(HttpStatus.OK)
  @TrainingPlanSwaggerDocs.findAllTrainingPlansDocs()
  async findAll(
    @Query('status') status: TrainingPlanStatus | undefined,
    @CurrentBaseUser() user: UserEntity,
  ): Promise<GetTrainingPlanResponseDto[]> {
    return this.trainingPlanService.findAll(user, status);
  }

  @Get(TRAINING_PLAN_ROUTES.SINGLE_TRAINING_PLAN)
  @HttpCode(HttpStatus.OK)
  @TrainingPlanSwaggerDocs.findOneTrainingPlanDocs()
  async findOne(@Param('id') id: string, @CurrentBaseUser() user: UserEntity): Promise<GetTrainingPlanResponseDto> {
    return this.trainingPlanService.findOne(id, user);
  }

  @Put(TRAINING_PLAN_ROUTES.SINGLE_TRAINING_PLAN)
  @HttpCode(HttpStatus.OK)
  @TrainingPlanSwaggerDocs.updateTrainingPlanDocs()
  async update(
    @Param('id') id: string,
    @Body() data: UpdateTrainingPlanRequestDto,
    @CurrentBaseUser() user: UserEntity,
  ): Promise<UpdateTrainingPlanResponseDto> {
    return this.trainingPlanService.update(id, data, user);
  }

  @Delete(TRAINING_PLAN_ROUTES.SINGLE_TRAINING_PLAN)
  @HttpCode(HttpStatus.OK)
  @TrainingPlanSwaggerDocs.removeTrainingPlanDocs()
  async remove(@Param('id') id: string, @CurrentBaseUser() user: UserEntity) {
    return this.trainingPlanService.remove(id, user);
  }

  @Post(TRAINING_PLAN_ROUTES.ASSIGN_TO_GROUP)
  @HttpCode(HttpStatus.OK)
  @TrainingPlanSwaggerDocs.assignTrainingPlanToGroupDocs()
  async assignToGroup(
    @Param('training_plan_id') trainingPlanId: string,
    @Param('group_id') groupId: string,
    @Body() assignDto: AssignTrainingPlanToGroupDto,
    @Request() req,
  ): Promise<TrainingPlanGroupResponseDto> {
    return this.trainingPlanService.assignToGroup(trainingPlanId, groupId, assignDto, req.user);
  }

  @Delete(TRAINING_PLAN_ROUTES.UNASSIGN_FROM_GROUP)
  @HttpCode(HttpStatus.OK)
  @TrainingPlanSwaggerDocs.unassignTrainingPlanFromGroupDocs()
  async unassignFromGroup(
    @Param('training_plan_id') trainingPlanId: string,
    @Param('group_id') groupId: string,
    @Request() req,
  ): Promise<void> {
    return this.trainingPlanService.unassignFromGroup(trainingPlanId, groupId, req.user);
  }
}
