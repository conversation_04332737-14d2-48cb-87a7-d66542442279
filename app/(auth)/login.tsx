import React from "react";
import {
  StyleSheet,
  Image,
  KeyboardAvoidingView,
  ScrollView,
  TouchableWithoutFeedback,
  Keyboard,
  View,
} from "react-native";
import {
  ButtonBasic,
  ButtonBorder,
  InputBasic,
  InputPassword,
  Typography,
  useTheme,
} from "@vs/kit-ui-expo";
import { Controller } from "react-hook-form";
import { ForgotPasswordLink } from "@/components/ForgotPasswordLink";
import { useLogin } from "@/hooks/useLogin";
import { useTranslation } from "react-i18next";
import { ContainerBottomModalWithAuthLayout } from "@/components/ContainerBottomModal";

export default function LoginScreen() {
  const { theme } = useTheme();
  const { t } = useTranslation();
  const { control, handleSubmit, errors, onSubmit, handleScanQR } = useLogin();

  const renderHeader = () => (
    <View style={styles.logoContainer}>
      <Image
        source={require("../../assets/bottles/bottle-phone.png")}
        style={[styles.logo, { marginBottom: theme.spacing[8] }]}
        resizeMode="contain"
      />
    </View>
  );

  const renderForm = () => (
    <>
      <Controller
        control={control}
        name="email"
        render={({ field: { onChange, onBlur, value } }) => (
          <InputBasic
            onBlur={onBlur}
            onChangeText={onChange}
            value={value}
            label={t("login_screen_email_label")}
            placeholder={t("login_screen_email_placeholder")}
            error={t(errors.email?.message || "")}
          />
        )}
      />
      <Controller
        control={control}
        name="password"
        render={({ field: { onChange, onBlur, value } }) => (
          <InputPassword
            onBlur={onBlur}
            onChangeText={onChange}
            value={value}
            label={t("login_screen_password_label")}
            placeholder={t("login_screen_password_placeholder")}
            error={t(errors.password?.message || "")}
          />
        )}
      />
    </>
  );

  const renderButtons = () => (
    <>
      <ButtonBasic onPress={handleSubmit(onSubmit)}>
        {t("login_screen_button_login")}
      </ButtonBasic>
      <ForgotPasswordLink />
      <ButtonBorder onPress={handleScanQR}>
        {t("login_screen_button_qr")}
      </ButtonBorder>
    </>
  );

  return (
    <KeyboardAvoidingView
      style={[styles.container, { backgroundColor: theme.colors.grey[900] }]}
      behavior={"padding"}
      keyboardVerticalOffset={50}
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <ScrollView keyboardShouldPersistTaps="handled">
          {renderHeader()}
          <ContainerBottomModalWithAuthLayout>
            <Typography variant="Heading4" color={theme.colors.grey[50]}>
              {t("login_screen_title")}
            </Typography>
            {renderForm()}
            {renderButtons()}
          </ContainerBottomModalWithAuthLayout>
        </ScrollView>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  logoContainer: {
    alignItems: "center",
    justifyContent: "center",
    marginTop: 40,
    marginBottom: 20,
  },
  logo: {
    width: 240,
    height: 240,
    transform: [{ rotate: "16deg" }],
  },
});
