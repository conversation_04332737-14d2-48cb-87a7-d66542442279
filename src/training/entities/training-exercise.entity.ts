import { <PERSON><PERSON><PERSON>, PrimaryGeneratedC<PERSON>umn, <PERSON>umn, ManyTo<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CreateDateColumn } from 'typeorm';
import { TrainingEntity } from './training.entity';
import { ExerciseEntity } from '@app/exercises/entities/exercise.entity';
import { BaseEntity } from 'typeorm';

@Entity({ name: 'training_exercises' })
export class TrainingExerciseEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'training_id' })
  trainingId: string;

  @Column({ name: 'exercise_id' })
  exerciseId: string;

  @Column({ name: 'order_index' })
  orderIndex: number;

  @CreateDateColumn()
  createdAt: Date;

  @ManyToOne(() => TrainingEntity, (training) => training.trainingExercises, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  @JoinColumn({ name: 'training_id' })
  training: TrainingEntity;

  @ManyToOne(() => ExerciseEntity, { eager: true })
  @JoinC<PERSON>umn({ name: 'exercise_id' })
  exercise: ExerciseEntity;
}
