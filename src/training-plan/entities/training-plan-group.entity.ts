import { <PERSON><PERSON><PERSON>, PrimaryGenerated<PERSON><PERSON>umn, <PERSON><PERSON><PERSON>, ManyTo<PERSON>ne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CreateDateColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { TrainingPlanEntity } from './training-plan.entity';
import { GroupEntity } from '@app/groups/entities/group.entity';
import { BaseEntity } from 'typeorm';

@Entity({ name: 'training_plan_groups' })
export class TrainingPlanGroupEntity extends BaseEntity {
  @ApiProperty({
    description: 'Unique training plan group relationship identifier',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({
    description: 'Training plan ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @Column({ name: 'training_plan_id' })
  trainingPlanId: string;

  @ApiProperty({
    description: 'Group ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @Column({ name: 'group_id' })
  groupId: string;

  @ApiProperty({
    description: 'Start date of the training plan for the group',
    example: '2025-05-06',
  })
  @Column({ name: 'start_date', type: 'date' })
  startDate: Date;

  @ApiProperty({
    description: 'Relationship creation timestamp',
    example: '2023-01-01T12:00:00Z',
  })
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @ManyToOne(() => TrainingPlanEntity, (trainingPlan) => trainingPlan.trainingPlanGroups, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'training_plan_id' })
  trainingPlan: TrainingPlanEntity;

  @ManyToOne(() => GroupEntity, (group) => group.trainingPlanGroups, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'group_id' })
  group: GroupEntity;
}
