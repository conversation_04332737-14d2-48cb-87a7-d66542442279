import { INestApplication } from '@nestjs/common';
import { CreateExerciseRequestDto } from '@app/exercises/dto/create-exercise.dto';
import request from 'supertest';
import { ExerciseEntity } from '@app/exercises/entities/exercise.entity';

export const createAndAssertExercise = async (
  app: INestApplication,
  payload: CreateExerciseRequestDto,
  accessToken: string,
): Promise<ExerciseEntity> => {
  const response = await request(app.getHttpServer())
    .post('/v1/exercise')
    .set('Authorization', `Bearer ${accessToken}`)
    .send(payload);

  expect(response.status).toBe(201);
  expect(response.body.id).toBeDefined();
  expect(response.body.name).toBe(payload.name);
  expect(response.body.description).toBe(payload.description);
  expect(response.body.tags).toBe(payload.tags);
  expect(response.body.difficultyLevel).toBe(payload.difficultyLevel);
  expect(response.body.videoUrl).toBeNull();
  expect(response.body.imageUrl).toBeNull();
  expect(response.body.instructions).toBeDefined();
  expect(response.body.status).toBe('pending');
  expect(response.body.statusMessage).toBeNull();
  expect(response.body.createdAt).toBeDefined();
  expect(response.body.updatedAt).toBeDefined();
  expect(response.body.creatorId).toBeDefined();

  return response.body as ExerciseEntity;
};
