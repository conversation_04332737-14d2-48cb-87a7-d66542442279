import { Modu<PERSON> } from '@nestjs/common';
import { MailService } from './services/mail.service';
import { MailerModule } from '@nestjs-modules/mailer';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { join } from 'path';
import { HandlebarsAdapter } from '@nestjs-modules/mailer/dist/adapters/handlebars.adapter';
import { MailController } from './controllers/mail/mail.controller';

@Module({
  imports: [
    MailerModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (config: ConfigService) => {
        // Check if Gmail configuration is available, otherwise use SendGrid
        const useGmail = config.get<string>('GMAIL_HOST') && config.get<string>('GMAIL_USER');

        return {
          transport: useGmail
            ? {
                host: config.get<string>('GMAIL_HOST'),
                port: config.get<number>('GMAIL_PORT') || 587,
                secure: false, // true for 465, false for other ports
                auth: {
                  user: config.get<string>('GMAIL_USER'),
                  pass: config.get<string>('GMAIL_PASSWORD'),
                },
              }
            : {
                host: config.get<string>('SENDGRID_HOST'),
                auth: {
                  user: config.get<string>('SENDGRID_API_USERNAME'),
                  pass: config.get<string>('SENDGRID_API_KEY'),
                },
              },
          defaults: {
            from: useGmail ? config.get<string>('GMAIL_FROM') : config.get<string>('SENDGRID_MAIL_FROM'),
          },
          template: {
            dir: join(__dirname, 'templates'),
            adapter: new HandlebarsAdapter(),
            options: {
              strict: true,
            },
          },
        };
      },
      inject: [ConfigService],
    }),
  ],
  providers: [MailService],
  exports: [MailService],
  controllers: [MailController],
})
export class MailModule {}
