import { <PERSON><PERSON><PERSON>, <PERSON>NotEmpty, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateOtcDto {
  @ApiProperty({
    description: 'User email address',
    example: '<EMAIL>',
  })
  @IsEmail()
  email: string;

  @ApiProperty({
    description: 'Purpose for which the OTP is being generated',
    example: 'registration',
  })
  @IsString()
  @IsNotEmpty()
  purpose: string;
}
