import 'reflect-metadata';
import { INestApplication } from '@nestjs/common';
import request from 'supertest';

import { loginAndAssertUser, registerAndAssertUser } from '../__helpers/auth';
import { buildTestApp } from '../__setup/app.factory';
import { generateTestUser } from '../__helpers/user';
import { CreateTrainingPlanRequestDto } from '@app/training-plan/dto/create-training-plan.dto';
import { CreateTrainingRequestDto } from '@app/training/dto/create-training.dto';
import { createAndAssertTraining } from '../__helpers/training';
import { randomUUID } from 'crypto';
import { createAndAssertConsent, generateGDPRConsentPayload } from '../__helpers/consents';
import { ConsentDefinitionEntity } from '@app/consents/entities/consent-definition.entity';

describe('/POST /v1/training-plan', () => {
  let app: INestApplication;
  let accessToken: string;
  let consent: ConsentDefinitionEntity;

  beforeAll(async () => {
    app = await buildTestApp();

    const consentPayload = generateGDPRConsentPayload();
    consent = await createAndAssertConsent(app, consentPayload);

    const testUser = generateTestUser([consent.id]);
    await registerAndAssertUser(app, testUser);
    accessToken = (await loginAndAssertUser(app, testUser)).accessToken;
  });

  afterAll(async () => {
    await app.close();
  });

  it('should assign a training plan to a group', async () => {});
  it.todo('should return 400 if training plan is not found');
  it.todo('should return 400 if group is not found');
  it.todo('should return 400 if group does not belong to user');

  it.todo('should unassign a training plan from a group');
  it.todo('should return 400 if training plan is not found');
  it.todo('should return 400 if group is not found');
  it.todo('should return 400 if group does not belong to user');
});

// ASSIGN_TO_GROUP: `${TRAINING_PLAN}/:training_plan_id/group/assign/:group_id`,
// UNASSIGN_FROM_GROUP: `${TRAINING_PLAN}/:training_plan_id/group/unassign/:group_id`,
