import { useForm } from "react-hook-form";
import { useErrorHandling } from "./useErrorHandling";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "expo-router";
import { ToastSuccess, ToastWarning, useToast } from "@vs/kit-ui-expo";
import { useTranslation } from "react-i18next";
import { useTrainingListQuery } from "./useTrainingList";
import { newTrainingSchema, NewTrainingType } from "@/validations";
import {
  deleteTraining,
  postNewTraining,
  putNewTraining,
} from "@/api/training";
import { useTrainingPlanListQuery } from "./useTrainingPlanList";
import {
  newTrainingPlanSchema,
  NewTrainingPlanType,
} from "@/validations/newTrainingPlan";
import {
  deleteTrainingPlan,
  postNewTrainingPlan,
  putNewTrainingPlan,
} from "@/api/trainingPlan";

interface Props {
  id?: string;
  isNew?: boolean;
}

export const useNewTrainingPlan = ({ id, isNew }: Props) => {
  const { withErrorHandling } = useErrorHandling();
  const { showToast } = useToast();
  const { t } = useTranslation();
  const { refetch } = useTrainingPlanListQuery();
  const router = useRouter();
  const {
    control,
    setValue,
    handleSubmit,
    watch,
    formState: { errors },
    reset,
  } = useForm<NewTrainingPlanType>({
    resolver: zodResolver(newTrainingPlanSchema),
    defaultValues: {
      trainings: [],
    },
  });

  const trainings = watch("trainings");

  const onSubmit = (data: NewTrainingPlanType) =>
    withErrorHandling(async () => {
      const payload = {
        name: data.name,
        description: data.description,
        trainings: data.trainings,
      };

      console.log("paylaod", payload);

      if (id && !isNew) {
        await putNewTrainingPlan(payload, id);
      } else {
        await postNewTrainingPlan(payload);
      }

      showToast(
        <ToastSuccess
          title={t("creators_form_toast_training_plan_success_title")}
          message={t("creators_form_toast_training_plan_success_description")}
        />
      );
      await refetch();
      router.back();
    });

  const handleDeleteTrainingPlan = (id: string) =>
    withErrorHandling(async () => {
      await deleteTrainingPlan(id);
      showToast(
        <ToastWarning
          title={t("creators_form_toast_training_plan_delete_title")}
          message={t("creators_form_toast_training_plan_delete_description")}
        />
      );
      await refetch();
      router.back();
    });

  const setTrainingPlanTrainings = (trainings: string[]) => {
    setValue("trainings", trainings);
  };

  return {
    control,
    handleSubmit,
    errors,
    trainings,
    onSubmit,
    reset,
    handleDeleteTrainingPlan,
    setTrainingPlanTrainings,
  };
};
