import { Column, <PERSON>tity, <PERSON>To<PERSON>ne, PrimaryGeneratedColumn } from 'typeorm';
import { UserEntity } from '@app/users/entities/user.entity';
import { ConsentDefinitionEntity } from '@app/consents/entities/consent-definition.entity';
import { BaseEntity } from 'typeorm';

@Entity({ name: 'user_consents' })
export class UserConsentEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => UserEntity, (user: UserEntity) => user.consents, { onDelete: 'CASCADE' })
  user: UserEntity;

  @ManyToOne(() => ConsentDefinitionEntity, { eager: true })
  consentDefinition: ConsentDefinitionEntity;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  acceptedAt: Date;
}
