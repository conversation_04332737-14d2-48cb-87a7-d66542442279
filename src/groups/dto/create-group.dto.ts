import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { GroupUserEntity } from '../entities/group-user.entity';
import { TrainingPlanGroupEntity } from '@app/training-plan/entities/training-plan-group.entity';

export class CreateGroupRequestDto {
  @ApiProperty({
    description: 'Group name',
    example: 'Super grupa',
  })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Group description',
    example: 'Super grupa z siłowni ZIELONA',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Group tags separated by |',
    example: 'grupa1|workout|żeńska',
    required: false,
  })
  @IsOptional()
  @IsString()
  tags?: string;
}

export class CreateGroupResponseDto {
  @ApiProperty({
    description: 'Unique group identifier',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Group name',
    example: 'Super grupa',
  })
  name: string;

  @ApiProperty({
    description: 'Group description',
    example: 'Super grupa z siłowni ZIELONA',
    required: false,
  })
  description?: string;

  @ApiProperty({
    description: 'Group tags separated by |',
    example: 'grupa1|workout|żeńska',
    required: false,
  })
  tags?: string;

  @ApiProperty({
    description: 'Group invitation URL',
    example: 'https://invite-to-group.pl/id=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  invitationUrl: string;

  @ApiProperty({
    description: 'Group creation timestamp',
    example: '2023-01-01T12:00:00Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Group last update timestamp',
    example: '2023-01-01T12:00:00Z',
  })
  updatedAt: Date;

  @ApiProperty({
    description: 'Group owner ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  ownerId: string;

  groupUsers: GroupUserEntity[];

  @ApiProperty({
    description: 'Training plans assigned to this group',
    type: () => [TrainingPlanGroupEntity],
  })
  trainingPlanGroups: TrainingPlanGroupEntity[];
}
