import { defineConfig, Options } from "tsup";

export default defineConfig((options: Options) => ({
  entry: {
    index: "src/index.ts",
  },
  banner: {
    js: "'use client'",
  },
  clean: true,
  format: ["cjs", "esm"],
  external: [
    "react", 
    "react-native", 
    "@vs/kit-typescript-config", 
    "react-native-safe-area-context",
    "expo-router"
  ],
  dts: true,
  esbuildOptions(options) {
    options.loader = {
      ...options.loader,
      ".js": "jsx",
    };
  },
  ...options,
}));
