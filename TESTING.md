# Testing Documentation

This document describes the testing approach for the VS Backend application, focusing on end-to-end (E2E) tests.

## E2E Testing Structure

The E2E tests are located in the `/test` directory and follow a consistent naming pattern:

- `test/<module-name>.e2e-spec.ts` - Tests for specific modules
- `test/app.e2e-spec.ts` - General application tests

### Test Configuration

E2E tests use <PERSON><PERSON> as the test runner with the following configuration:

- Configuration file: `test/jest-e2e.json`
- Path aliases: Uses `@app/*` to reference files in the `src` directory
- Test environment: Node.js

## Main Test Cases

### Exercises Module (`test/exercises.e2e-spec.ts`)

The exercises module E2E tests cover the following scenarios:

#### Authentication and Authorization

- Access control for authenticated vs. unauthenticated users
- Role-based access control (TRAINER vs. USER roles)
- Permission checks for accessing resources owned by other users
- Testing with invalid authentication tokens

#### CRUD Operations

- **Create Exercise**

  - Success case: Creating a valid exercise
  - Validation errors: Missing required fields, invalid data formats
  - Authorization: Only trainers can create exercises

- **Read Exercises**

  - Get all exercises for the authenticated user
  - Filter exercises by status
  - Get a specific exercise by ID
  - Handle non-existent exercise IDs
  - Authorization: Users can only access their own exercises

- **Update Exercise**

  - Success case: Updating an existing exercise
  - Validation errors: Invalid data formats
  - Authorization: Users can only update their own exercises
  - Status reset: Exercise status resets to PENDING after update

- **Delete Exercise**
  - Soft deletion: Setting status to DELETED instead of removing from database
  - Authorization: Users can only delete their own exercises

#### Edge Cases

- Invalid UUIDs
- Non-existent resources
- Invalid data formats
- Missing required fields
- Unauthorized access attempts

### Training Module (`test/training.e2e-spec.ts`)

The training module E2E tests cover the following scenarios:

#### Authentication and Authorization

- Access control for authenticated vs. unauthenticated users
- Role-based access control (only TRAINER role can access training endpoints)
- Testing with invalid authentication tokens

#### CRUD Operations

- **Create Training**

  - Success case: Creating a valid training with exercises
  - Validation errors: Missing required fields (name, exercises)
  - Handling non-existent exercises
  - Authorization: Only trainers can create trainings

- **Read Trainings**

  - Get all trainings for the authenticated user
  - Filter trainings by status (ACTIVE, DELETED)
  - Get a specific training by ID
  - Handle non-existent training IDs
  - Authorization: Users can only access their own trainings

- **Update Training**

  - Success case: Updating an existing training
  - Partial updates: Updating only specific fields
  - Validation errors: Invalid data formats
  - Authorization: Users can only update their own trainings

- **Delete Training**
  - Soft deletion: Setting status to DELETED instead of removing from database
  - Authorization: Users can only delete their own trainings
  - Verification that deleted trainings can still be retrieved but have DELETED status

#### Edge Cases

- Invalid UUIDs
- Non-existent resources
- Invalid data formats
- Missing required fields
- Unauthorized access attempts
- Profanity filtering in training name and description

### Training Plan Module (`test/training-plan.e2e-spec.ts`)

The training plan module E2E tests cover the following scenarios:

#### Authentication and Authorization

- Access control for authenticated vs. unauthenticated users
- Role-based access control (only TRAINER role can access training plan endpoints)
- Testing with invalid authentication tokens

#### CRUD Operations

- **Create Training Plan**

  - Success case: Creating a valid training plan with trainings
  - Validation errors: Missing required fields (name, trainings)
  - Handling non-existent trainings
  - Authorization: Only trainers can create training plans
  - Profanity filtering in training plan name and description

- **Read Training Plans**

  - Get all training plans for the authenticated user
  - Filter training plans by status (ACTIVE, DELETED)
  - Get a specific training plan by ID
  - Handle non-existent training plan IDs
  - Authorization: Users can only access their own training plans

- **Update Training Plan**

  - Success case: Updating an existing training plan
  - Partial updates: Updating only specific fields (name, description)
  - Updating trainings in a training plan
  - Validation errors: Invalid data formats
  - Authorization: Users can only update their own training plans

- **Delete Training Plan**
  - Soft deletion: Setting status to DELETED instead of removing from database
  - Authorization: Users can only delete their own training plans
  - Verification that deleted training plans can still be retrieved but have DELETED status

#### Group Assignment Operations

- **Assign Training Plan to Group**

  - Success case: Assigning a training plan to a group with start date
  - Validation errors: Missing required fields (startDate)
  - Handling invalid date formats
  - Handling duplicate assignments
  - Handling non-existent training plans or groups

- **Unassign Training Plan from Group**
  - Success case: Removing a training plan assignment from a group
  - Handling non-existent training plans or groups
  - Handling non-existent assignments

#### Edge Cases

- Invalid UUIDs
- Non-existent resources
- Invalid data formats
- Missing required fields
- Unauthorized access attempts
- Duplicate assignments
- Invalid date formats

## Running Tests

### Locally

To run all E2E tests:

```bash
npm run test:e2e
```

To run a specific E2E test file:

```bash
npm run test:e2e -- exercises.e2e-spec.ts
```

### In CI/CD

The E2E tests are automatically run as part of the CI/CD pipeline. The pipeline:

1. Sets up the test database
2. Runs migrations to create the schema
3. Executes the E2E tests
4. Reports test results

## Test Data Management

The E2E tests create and manage their own test data:

- Test users with different roles are created at the start of the test suite
- Test exercises are created during the tests
- All test data is cleaned up after the tests complete

This approach ensures that tests are isolated and don't interfere with each other or depend on pre-existing data.

## Authentication in Tests

The tests authenticate by:

1. Creating test users with known credentials
2. Using the login endpoint to obtain JWT tokens
3. Including the tokens in the Authorization header for subsequent requests

## Best Practices

When writing new E2E tests:

1. Follow the existing pattern of test organization
2. Create and clean up your own test data
3. Test both success and failure cases
4. Test authorization and validation
5. Use descriptive test names that explain what is being tested
6. Keep tests independent of each other
7. Avoid hardcoded IDs or dependencies on existing data
