import {
  ApiTags,
  <PERSON>piOperation,
  ApiBearerAuth,
  ApiOkResponse,
  ApiUnauthorizedResponse,
  ApiCreatedResponse,
  ApiBadRequestResponse,
  ApiUnprocessableEntityResponse,
} from '@nestjs/swagger';
import { applyDecorators } from '@nestjs/common';
import { UserEntity } from '../entities/user.entity';

export class UsersSwaggerDocs {
  static apiTags() {
    return applyDecorators(ApiTags('Users'));
  }

  static apiBearerAuth() {
    return applyDecorators(ApiBearerAuth('JWT'));
  }

  static createOtcCode() {
    return applyDecorators(
      ApiBearerAuth(),
      ApiOperation({
        summary: 'Create OTC code',
        description: 'Create OTC code for registration',
      }),
      ApiOkResponse({
        description: 'List of users retrieved successfully',
      }),
      ApiUnauthorizedResponse(),
    );
  }

  static createUserDocs() {
    return applyDecorators(
      ApiOperation({
        summary: 'Create a new user',
        description:
          'Creates a new user with the provided details including email, name, password, and required consents.',
      }),
      ApiCreatedResponse({
        description: 'User successfully created',
        type: UserEntity,
      }),
      ApiBadRequestResponse({
        description: 'Missing required consents or invalid input data',
      }),
      ApiUnprocessableEntityResponse({
        description: 'User already exists or error during user creation',
      }),
    );
  }

  static getUsersDocs() {
    return applyDecorators(
      ApiBearerAuth(),
      ApiOperation({
        summary: 'Get all users',
        description: 'Retrieves a list of all users. Requires authentication.',
      }),
      ApiOkResponse({
        description: 'List of users retrieved successfully',
        type: [UserEntity],
      }),
      ApiUnauthorizedResponse({
        description: 'Unauthorized access - valid JWT token required',
      }),
    );
  }
}
