import { BeforeInsert, BeforeUpdate, Column, CreateDateColumn, Entity, PrimaryColumn } from 'typeorm';

@Entity({ name: 'user_otcs' })
export class UserOtcEntity {
  @PrimaryColumn()
  email: string;

  @Column()
  code: string;

  @CreateDateColumn({ type: 'timestamp with time zone' })
  createdAt: Date;

  @Column({ type: 'timestamp with time zone' })
  expiresAt: Date;

  @BeforeInsert()
  @BeforeUpdate()
  setExpiresAt() {
    this.expiresAt = new Date();
    this.expiresAt.setMinutes(this.expiresAt.getMinutes() + 10);
  }
}
