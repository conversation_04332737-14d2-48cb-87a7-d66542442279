import { NewTrainingResponse } from "@/types/training";
import { create } from "zustand";
import { devtools } from "zustand/middleware";

interface State {
  training: NewTrainingResponse[];
  setTraining: (training: NewTrainingResponse[]) => void;
  getTrainingById: (id: string) => NewTrainingResponse | undefined;
}

export const useTrainingStore = create<State>()(
  devtools(
    (set, get) => ({
      training: [],
      setTraining: async (training) => {
        try {
          set({ training });
        } catch (error) {
          throw error;
        }
      },
      getTrainingById: (id) => {
        return get().training.find((training) => training.id === id);
      },
    }),
    { name: "TrainingStore" }
  )
);
