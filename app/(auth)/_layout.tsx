import { NavigationTopBarBackWithLogo } from "@/components/NavigationTopBar";
import { Stack } from "expo-router";
import React from "react";
import { PathEnum } from "@/enums/path";

export default function AuthLayout() {
  return (
    <Stack screenOptions={{ header: () => <NavigationTopBarBackWithLogo /> }}>
      <Stack.Screen name={PathEnum.LOGIN.substring(1)} />
      <Stack.Screen name={PathEnum.REGISTER.substring(1)} />
      <Stack.Screen
        name={PathEnum.EMAIL_OTC.substring(1)}
        options={{ headerShown: false }}
      />
      <Stack.Screen name={PathEnum.QR_SCANNER.substring(1)} />
      <Stack.Screen name={PathEnum.CONSENTS_CONTENT.substring(1)} />
    </Stack>
  );
}
