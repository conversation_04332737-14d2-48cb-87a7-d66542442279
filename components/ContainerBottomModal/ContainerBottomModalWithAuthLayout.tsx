import React from "react";
import { ContainerModalDown, Theme, useTheme } from "@vs/kit-ui-expo";
import { StyleSheet } from "react-native";

interface Props {
  children: any;
}

export const ContainerBottomModalWithAuthLayout = ({ children }: Props) => {
  const { theme } = useTheme();
  const styles = stylesConfig(theme);
  return (
    <ContainerModalDown style={styles.container}>{children}</ContainerModalDown>
  );
};

const stylesConfig = (theme: Theme) =>
  StyleSheet.create({
    container: {
      alignItems: "center",
      flexDirection: "column",
      justifyContent: "space-between",
      gap: theme.spacing[4],
      padding: theme.spacing[8],
    },
  });
