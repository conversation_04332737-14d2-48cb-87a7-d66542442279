import 'reflect-metadata';
import { INestApplication } from '@nestjs/common';
import request from 'supertest';

import { loginAndAssertUser, registerAndAssertUser } from '../__helpers/auth';
import { buildTestApp } from '../__setup/app.factory';
import { generateTestUser } from '../__helpers/user';
import { CreateTrainingRequestDto } from '@app/training/dto/create-training.dto';
import { createAndAssertTraining } from '../__helpers/training';
import { randomUUID } from 'node:crypto';
import { ConsentDefinitionEntity } from '@app/consents/entities/consent-definition.entity';
import { createAndAssertConsent, generateGDPRConsentPayload } from '../__helpers/consents';
import { UpdateTrainingPlanRequestDto } from '@app/training-plan/dto/update-training-plan.dto';
import { CreateTrainingPlanRequestDto } from '@app/training-plan/dto/create-training-plan.dto';
import { createAndAssertTrainingPlan } from '../__helpers/training-plan';

describe('/PUT /v1/training-plan/:id', () => {
  let app: INestApplication;
  let accessToken: string;
  let consent: ConsentDefinitionEntity;

  beforeAll(async () => {
    app = await buildTestApp();

    const consentPayload = generateGDPRConsentPayload();
    consent = await createAndAssertConsent(app, consentPayload);

    const testUser = generateTestUser([consent.id]);
    await registerAndAssertUser(app, testUser);
    accessToken = (await loginAndAssertUser(app, testUser)).accessToken;
  });

  afterAll(async () => {
    await app.close();
  });

  it('should update a training plan name', async () => {
    const payloadToCreate: CreateTrainingPlanRequestDto = {
      name: 'Test training name',
      description: 'Test training description',
      trainings: [],
    };

    const payloadToUpdate: UpdateTrainingPlanRequestDto = {
      name: 'Updated training name',
    };

    const trainingPlan = await createAndAssertTrainingPlan(app, payloadToCreate, accessToken);

    const response = await request(app.getHttpServer())
      .put(`/v1/training-plan/${trainingPlan.id}`)
      .set('Authorization', `Bearer ${accessToken}`)
      .send(payloadToUpdate);

    expect(response.status).toBe(200);
    expect(response.body.id).toBe(trainingPlan.id);
    expect(response.body.name).toBe(payloadToUpdate.name);
    expect(response.body.description).toBe(payloadToCreate.description);
    expect(response.body.trainings).toEqual(payloadToCreate.trainings);
    expect(response.body.status).toBe('active');
    expect(response.body.createdAt).toBeDefined();
    expect(response.body.updatedAt).toBeDefined();
    expect(response.body.creatorId).toBeDefined();
  });

  it('should update a training plan description', async () => {
    const payloadToCreate: CreateTrainingPlanRequestDto = {
      name: 'Test training name',
      description: 'Test training description',
      trainings: [],
    };

    const payloadToUpdate: UpdateTrainingPlanRequestDto = {
      description: 'Updated training description',
    };

    const trainingPlan = await createAndAssertTrainingPlan(app, payloadToCreate, accessToken);

    const response = await request(app.getHttpServer())
      .put(`/v1/training-plan/${trainingPlan.id}`)
      .set('Authorization', `Bearer ${accessToken}`)
      .send(payloadToUpdate);

    expect(response.status).toBe(200);
    expect(response.body.id).toBe(trainingPlan.id);
    expect(response.body.name).toBe(payloadToCreate.name);
    expect(response.body.description).toBe(payloadToUpdate.description);
    expect(response.body.trainings).toEqual(payloadToCreate.trainings);
    expect(response.body.status).toBe('active');
    expect(response.body.createdAt).toBeDefined();
    expect(response.body.updatedAt).toBeDefined();
    expect(response.body.creatorId).toBeDefined();
  });

  it('should update a training plan trainings', async () => {
    const payloadToCreateTrainingPlan: CreateTrainingPlanRequestDto = {
      name: 'Test training name',
      description: 'Test training description',
      trainings: [],
    };
    const payloadToCreateTraining: CreateTrainingRequestDto = {
      name: 'Test training name',
      description: 'Test training description',
      exercises: [],
      tags: '',
    };

    const trainingPlan = await createAndAssertTrainingPlan(app, payloadToCreateTrainingPlan, accessToken);
    const training = await createAndAssertTraining(app, payloadToCreateTraining, accessToken);

    const payloadToUpdate: UpdateTrainingPlanRequestDto = {
      trainings: [training.id],
    };

    const response = await request(app.getHttpServer())
      .put(`/v1/training-plan/${trainingPlan.id}`)
      .set('Authorization', `Bearer ${accessToken}`)
      .send(payloadToUpdate);

    expect(response.status).toBe(200);
    expect(response.body.id).toBe(trainingPlan.id);
    expect(response.body.name).toBe(payloadToCreateTrainingPlan.name);
    expect(response.body.description).toBe(payloadToCreateTrainingPlan.description);
    expect(response.body.trainings[0].id).toEqual(training.id);
    expect(response.body.status).toBe('active');
    expect(response.body.createdAt).toBeDefined();
    expect(response.body.updatedAt).toBeDefined();
    expect(response.body.creatorId).toBeDefined();
  });

  it('should update a training plan trainings order', async () => {
    const payloadToCreateTraining1: CreateTrainingRequestDto = {
      name: 'Test training name',
      description: 'Test training description',
      exercises: [],
      tags: '',
    };
    const payloadToCreateTraining2: CreateTrainingRequestDto = {
      name: 'Test training name',
      description: 'Test training description',
      exercises: [],
      tags: '',
    };

    const training1 = await createAndAssertTraining(app, payloadToCreateTraining1, accessToken);
    const training2 = await createAndAssertTraining(app, payloadToCreateTraining2, accessToken);

    const payloadToCreateTrainingPlan: CreateTrainingPlanRequestDto = {
      name: 'Test training name',
      description: 'Test training description',
      trainings: [training1.id, training2.id],
    };

    const trainingPlan = await createAndAssertTrainingPlan(app, payloadToCreateTrainingPlan, accessToken);

    const payloadToUpdate: UpdateTrainingPlanRequestDto = {
      trainings: [training2.id, training1.id],
    };

    const response = await request(app.getHttpServer())
      .put(`/v1/training-plan/${trainingPlan.id}`)
      .set('Authorization', `Bearer ${accessToken}`)
      .send(payloadToUpdate);

    expect(response.status).toBe(200);
    expect(response.body.trainings[0].id).toEqual(training2.id);
    expect(response.body.trainings[1].id).toEqual(training1.id);
  });

  it('should return 400 if training is invalid or not found', async () => {
    const payloadToCreateTrainingPlan: CreateTrainingPlanRequestDto = {
      name: 'Test training name',
      description: 'Test training description',
      trainings: [],
    };

    const trainingPlan = await createAndAssertTrainingPlan(app, payloadToCreateTrainingPlan, accessToken);

    const payloadToUpdate: UpdateTrainingPlanRequestDto = {
      trainings: [randomUUID()],
    };

    const response = await request(app.getHttpServer())
      .put(`/v1/training-plan/${trainingPlan.id}`)
      .set('Authorization', `Bearer ${accessToken}`)
      .send(payloadToUpdate);

    expect(response.status).toBe(400);
    expect(response.body.message).toBe('One or more trainings do not exist or do not belong to you');
    expect(response.body.error).toBe('Bad Request');
    expect(response.body.statusCode).toBe(400);
  });

  it('should return 401 if user is not authenticated', async () => {
    const payloadToCreateTrainingPlan: CreateTrainingPlanRequestDto = {
      name: 'Test training name',
      description: 'Test training description',
      trainings: [],
    };

    const trainingPlan = await createAndAssertTrainingPlan(app, payloadToCreateTrainingPlan, accessToken);

    const payloadToUpdate: UpdateTrainingPlanRequestDto = {
      trainings: [randomUUID()],
    };

    const response = await request(app.getHttpServer())
      .put(`/v1/training-plan/${trainingPlan.id}`)
      .send(payloadToUpdate);

    expect(response.status).toBe(401);
    expect(response.body.message).toBe('Unauthorized');
  });
});
