{"name": "vitamin-station", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint", "devtools": "rndebugger://set-debugger-loc?host=localhost&port=19000"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^14.0.2", "@hookform/resolvers": "^5.0.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@react-navigation/native-stack": "^7.2.0", "@tanstack/react-query": "^5.81.5", "@vs/kit-ui-assets": "file:../vs_kit/packages/ui-assets", "@vs/kit-ui-expo": "file:../vs_kit/packages/ui-expo", "axios": "^1.8.4", "expo": "^53.0.0", "expo-barcode-scanner": "^13.0.1", "expo-blur": "~14.1.4", "expo-camera": "^16.0.18", "expo-constants": "~17.1.6", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-linear-gradient": "^14.0.2", "expo-linking": "~7.1.5", "expo-localization": "^16.0.1", "expo-router": "~5.0.7", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.7", "expo-web-browser": "~14.1.6", "i18next": "^24.2.3", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.55.0", "react-i18next": "^15.4.1", "react-native": "0.79.2", "react-native-draglist": "^3.9.6", "react-native-gesture-handler": "~2.24.0", "react-native-markdown-display": "^7.0.2", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-svg": "15.11.2", "react-native-swiper": "^1.6.0", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/jest": "^29.5.12", "@types/react": "~19.0.10", "@types/react-test-renderer": "^18.3.0", "eslint": "^8.57.0", "eslint-config-expo": "~9.2.0", "jest": "^29.2.1", "jest-expo": "~53.0.5", "react-test-renderer": "18.3.1", "typescript": "^5.3.3"}, "private": true, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}