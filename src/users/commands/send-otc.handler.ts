import { <PERSON><PERSON><PERSON><PERSON>, EventBus, ICommandHandler } from '@nestjs/cqrs';
import { SendOtcCommand } from '@app/users/commands/send-otc.command';
import { OtcGeneratedEvent } from '@app/users/events/otc-generated.event';
import { UsersService } from '@app/users/services/users.service';

@CommandHandler(SendOtcCommand)
export class SendOtcHandler implements ICommandHandler<SendOtcCommand> {
  constructor(
    private readonly eventBus: EventBus,
    private readonly usersService: UsersService,
  ) {}

  async execute(command: SendOtcCommand) {
    const otc = this.generateOtc();
    await this.usersService.saveOrUpdateOtcCode(command.email, otc);
    await this.eventBus.publish(new OtcGeneratedEvent(command.email, otc));
  }

  private generateOtc(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }
}
