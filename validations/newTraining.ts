import { z } from "zod";

export const newTrainingSchema = z.object({
  name: z
    .string({ message: "validation_messages_required" })
    .min(3, "validation_messages_min_length_string"),
  description: z.string({ message: "validation_messages_required" }),
  tags: z.string({ message: "validation_messages_required" }),
  trainingExercises: z
    .array(z.string(), { message: "validation_messages_required" })
    .min(3, "validation_messages_min_exercises_count"),
});

export type NewTrainingType = z.infer<typeof newTrainingSchema>;
