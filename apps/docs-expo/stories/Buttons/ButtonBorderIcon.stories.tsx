import React from "react";
import type { Meta } from "@storybook/react";
import { BurgerIcon } from "@vs/kit-ui-assets";
import { ButtonBorderIcon, ThemeProvider, Sizes } from "@vs/kit-ui-expo";
import { View } from "react-native";

const meta = {
  title: "Components/Buttons/ButtonBorderIcon",
  component: ButtonBorderIcon,
  decorators: [
    Story => (
      <ThemeProvider>
        <View style={{ padding: 16, alignItems: "flex-start", width: "100%" }}>
          <Story />
        </View>
      </ThemeProvider>
    ),
  ],
} satisfies Meta<typeof ButtonBorderIcon>;

export default meta;

export const Default = () => {
  return <ButtonBorderIcon size={Sizes.Medium}>Basic Button</ButtonBorderIcon>;
};

export const Small = () => {
  return <ButtonBorderIcon size={Sizes.Small}>Small Button</ButtonBorderIcon>;
};

export const Medium = () => {
  return <ButtonBorderIcon size={Sizes.Medium}>Medium Button</ButtonBorderIcon>;
};

export const Large = () => {
  return <ButtonBorderIcon size={Sizes.Large}>Large Button</ButtonBorderIcon>;
};

export const WithLeftIcon = () => {
  return (
    <ButtonBorderIcon size={Sizes.Medium} leftIcon={<BurgerIcon fill="#000" stroke="transparent" />}>
      Left Icon
    </ButtonBorderIcon>
  );
};

export const WithRightIcon = () => {
  return (
    <ButtonBorderIcon size={Sizes.Medium} rightIcon={<BurgerIcon fill="#000" stroke="transparent" />}>
      Right Icon
    </ButtonBorderIcon>
  );
};

export const WithBothIcons = () => {
  return (
    <ButtonBorderIcon
      size={Sizes.Medium}
      leftIcon={<BurgerIcon fill="#000" stroke="transparent" />}
      rightIcon={<BurgerIcon fill="#000" stroke="transparent" />}
    >
      Both Icons
    </ButtonBorderIcon>
  );
};

export const Loading = () => {
  return (
    <ButtonBorderIcon size={Sizes.Medium} isLoading>
      Loading
    </ButtonBorderIcon>
  );
};

export const Disabled = () => {
  return (
    <ButtonBorderIcon size={Sizes.Medium} disabled>
      Disabled
    </ButtonBorderIcon>
  );
};

export const CustomStyle = () => {
  return (
    <ButtonBorderIcon
      size={Sizes.Medium}
      style={{ backgroundColor: "#ff5722", borderRadius: 25 }}
      textStyle={{ color: "white", fontWeight: "bold" }}
    >
      Custom Style
    </ButtonBorderIcon>
  );
};
