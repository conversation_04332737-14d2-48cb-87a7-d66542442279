import React, { useState, forwardRef, useRef, useImperativeHandle } from "react";
import { View, TouchableOpacity, StyleSheet, Animated, Easing, ViewStyle } from "react-native";
import { useTheme } from "../../theme";

interface Props {
  value?: boolean;
  disabled?: boolean;
  size?: "small" | "medium" | "large";
  onValueChange?: (value: boolean) => void;
  style?: ViewStyle;
}

export const SwitchBasic = forwardRef<TouchableOpacity, Props>(
  ({ value = false, onValueChange, disabled = false, size = "medium", style }, ref) => {
    const { theme } = useTheme();
    const { colors } = theme;

    const activeColor = colors.accent[300]; // Green color for active state
    const disabledColor = colors.grey[300]; // Grey color for disabled state

    const [isEnabled, setIsEnabled] = useState(value);
    const animatedValue = useRef(new Animated.Value(value ? 1 : 0)).current;

    // Forward ref to TouchableOpacity
    const touchableRef = useRef<TouchableOpacity>(null);
    useImperativeHandle(ref, () => touchableRef.current as TouchableOpacity);

    // Update internal state when prop changes
    React.useEffect(() => {
      setIsEnabled(value);
      Animated.timing(animatedValue, {
        toValue: value ? 1 : 0,
        duration: 200,
        easing: Easing.bezier(0.4, 0.0, 0.2, 1),
        useNativeDriver: false,
      }).start();
    }, [value, animatedValue]);

    const getSizes = () => {
      switch (size) {
        case "small":
          return {
            track: { width: 40, height: 20, borderRadius: 10, borderWidth: 2 },
            thumb: { size: 12, borderWidth: 2 },
            padding: 8,
          };
        case "large":
          return {
            track: { width: 70, height: 36, borderRadius: 18, borderWidth: 2 },
            thumb: { size: 26, borderWidth: 2 },
            padding: 8,
          };
        default: // medium
          return {
            track: { width: 56, height: 28, borderRadius: 14, borderWidth: 2 },
            thumb: { size: 20, borderWidth: 2 },
            padding: 8,
          };
      }
    };

    const { track, thumb, padding } = getSizes();

    const toggleSwitch = () => {
      if (disabled) return;

      const newValue = !isEnabled;
      setIsEnabled(newValue);

      Animated.timing(animatedValue, {
        toValue: newValue ? 1 : 0,
        duration: 200,
        easing: Easing.bezier(0.4, 0.0, 0.2, 1),
        useNativeDriver: false,
      }).start();

      if (onValueChange) {
        onValueChange(newValue);
      }
    };

    // Calculate the exact position for the thumb to ensure equal padding on both sides
    const leftPosition = padding / 2;
    const rightPosition = track.width - thumb.size - padding;

    // Animated properties
    const translateX = animatedValue.interpolate({
      inputRange: [0, 1],
      outputRange: [leftPosition, rightPosition],
    });

    const trackBackgroundColor = animatedValue.interpolate({
      inputRange: [0, 1],
      outputRange: ["transparent", "transparent"],
    });

    // const trackBorderColor = animatedValue.interpolate({
    //   inputRange: [0, 1],
    //   outputRange: [disabled ? disabledColor : colors.grey[400], disabled ? disabledColor : activeColor],
    // });

    // const thumbBackgroundColor = animatedValue.interpolate({
    //   inputRange: [0, 1],
    //   outputRange: [disabled ? disabledColor : colors.grey[400], disabled ? disabledColor : activeColor],
    // });

    const styles = StyleSheet.create({
      track: {
        width: track.width,
        height: track.height,
        borderRadius: track.borderRadius,
        borderWidth: track.borderWidth,
        justifyContent: "center",
        padding: 0,
      },
      thumb: {
        width: thumb.size,
        height: thumb.size,
        borderRadius: thumb.size / 2,
        borderWidth: thumb.borderWidth,
        borderColor: "transparent",
      },
    });

    return (
      <TouchableOpacity
        ref={touchableRef}
        activeOpacity={0.8}
        onPress={toggleSwitch}
        disabled={disabled}
        style={[style]}
        accessibilityRole="switch"
        accessibilityState={{ checked: isEnabled, disabled }}
      >
        <Animated.View
          style={[
            styles.track,
            {
              backgroundColor: trackBackgroundColor,
              borderColor: disabled ? disabledColor : activeColor,
            },
          ]}
        >
          <Animated.View
            style={[
              styles.thumb,
              {
                backgroundColor: disabled ? disabledColor : activeColor,
                transform: [{ translateX }],
              },
            ]}
          />
        </Animated.View>
      </TouchableOpacity>
    );
  }
);
