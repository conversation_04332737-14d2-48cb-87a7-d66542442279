import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ConsentDefinitionEntity } from '@app/consents/entities/consent-definition.entity';
import { In, Repository } from 'typeorm';
import { UserConsentEntity } from '@app/consents/entities/user-consent.entity';
import { CONSENTS_CONFIG, ConsentType } from '@app/constants/models/consent-config';
import { AcceptConsentDto } from '@app/consents/dto/accept-consent.dto';

@Injectable()
export class ConsentsService {
  constructor(
    @InjectRepository(ConsentDefinitionEntity)
    private consentRepo: Repository<ConsentDefinitionEntity>,
    @InjectRepository(UserConsentEntity)
    private userConsentRepo: Repository<UserConsentEntity>,
  ) {}

  async createNewConsent(
    code: ConsentType,
    content: string,
    shortDescription?: string,
  ): Promise<ConsentDefinitionEntity> {
    const last = await this.consentRepo.findOne({
      where: { code },
      order: { createdAt: 'DESC' },
    });

    const lastVersion = last ? parseInt(last.version.replace('v', '')) : 0;
    const newVersion = `v${lastVersion + 1}`;

    // TODO: remove predefined consents and use only from DB
    const config = CONSENTS_CONFIG.find((c) => c.type === code);

    return await this.consentRepo.save({
      code,
      version: newVersion,
      content,
      shortDescription,
      isRequired: config?.required ?? false,
    });
  }

  async getLatestConsents(): Promise<ConsentDefinitionEntity[]> {
    const latest: ConsentDefinitionEntity[] = [];
    for (const config of CONSENTS_CONFIG) {
      const record = await this.consentRepo.findOne({
        where: { code: config.type },
        order: { createdAt: 'DESC' },
      });
      if (record) latest.push(record);
    }
    return latest;
  }

  async acceptConsent(data: AcceptConsentDto): Promise<UserConsentEntity[]> {
    if (data.consentIds.length === 0) {
      throw new BadRequestException('No consents provided');
    }

    const consents = await this.consentRepo.find({
      where: { id: In(data.consentIds) },
    });

    if (consents.length !== data.consentIds.length) {
      throw new BadRequestException('Some consents were not found');
    }

    const userConsents = consents.map((consent) =>
      this.userConsentRepo.create({
        user: { id: data.user },
        consentDefinition: consent,
      }),
    );

    return await this.userConsentRepo.save(userConsents);
  }

  async getUserConsents(userId: string): Promise<UserConsentEntity[]> {
    return await this.userConsentRepo.find({
      where: { user: { id: userId } },
    });
  }

  async hasAcceptedAllRequiredConsents(userId: string): Promise<boolean> {
    const latestConsents = await this.getLatestConsents();
    const userConsents = await this.getUserConsents(userId);

    for (const latest of latestConsents) {
      const accepted = userConsents.find(
        (uc) => uc.consentDefinition.code === latest.code && uc.consentDefinition.version === latest.version,
      );
      if (!accepted) return false;
    }

    return true;
  }

  async getRequiredConsents(): Promise<ConsentDefinitionEntity[]> {
    return await this.consentRepo.find({
      where: { isRequired: true },
      order: { createdAt: 'DESC' },
    });
  }
}
