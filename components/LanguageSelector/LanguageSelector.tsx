import { useLanguage } from "@/contexts/LanguageContext";
import { ButtonBasic } from "@vs/kit-ui-expo";
import { View } from "react-native";

export const LanguageSelector = () => {
  const { language, setLanguage } = useLanguage();

  const handleChangeLanguage = () => {
    if (language === "en") {
      setLanguage("pl");
    } else {
      setLanguage("en");
    }
  };

  return (
    <View>
      <ButtonBasic onPress={handleChangeLanguage}>
        {language === "en" ? "English" : "Polski"}
      </ButtonBasic>
    </View>
  );
};
