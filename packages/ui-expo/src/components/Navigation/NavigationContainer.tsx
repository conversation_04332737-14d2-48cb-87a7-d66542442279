import React from "react";
import { StyleSheet, View } from "react-native";
import { useTheme } from "../../theme";

interface Props {
  children: React.ReactNode;
}
export const NavigationContainer = ({ children }: Props) => {
  const { theme } = useTheme();
  const { colors, spacing, borderRadius } = theme;

  const styles = StyleSheet.create({
    container: {
      padding: spacing[4],
      alignItems: "center",
      flexDirection: "row",
      justifyContent: "space-between",
      borderRadius: borderRadius.lg,
      backgroundColor: colors.grey[700],
    },
  });

  return <View style={styles.container}>{children}</View>;
};
