import { PathEnum } from "@/enums/path";
import { useAuthStore } from "@/stores/useAuthStore";
import { useUserStore } from "@/stores/useUserStore";
import { useRouter } from "expo-router";

export const useLogout = () => {
  const { clearUser } = useUserStore();
  const { clearCredentials } = useAuthStore();
  const router = useRouter();

  const handleLogout = () => {
    clearCredentials();
    clearUser();
    router.push(PathEnum.HOME);
  };

  return {
    handleLogout,
  };
};
