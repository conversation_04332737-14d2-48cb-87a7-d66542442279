import React from "react";
import { View, StyleSheet } from "react-native";
import {
  ButtonBasic,
  InputPin,
  Theme,
  Typography,
  useTheme,
} from "@vs/kit-ui-expo";
import { useTranslation } from "react-i18next";
import { Image } from "react-native";
import { useOtc } from "@/hooks/useOtc";

export default function EmailOtcScreen() {
  const { theme } = useTheme();
  const styles = stylesConfig(theme);
  const { t } = useTranslation();
  const { value, setValue, handleOtcVerifire } = useOtc();

  const renderHeader = () => {
    return (
      <View style={styles.logoContainer}>
        <Image
          source={require("../../assets/images/logo.png")}
          style={styles.logoText}
          resizeMode="contain"
        />
        <Typography
          variant="Heading4"
          color={theme.colors.grey[50]}
          style={styles.infoText}
        >
          {t("email_otc_screen_title")}
        </Typography>
        <Typography
          variant="BodyL"
          color={theme.colors.grey[50]}
          style={styles.infoText}
        >
          {t("email_otc_screen_description")}
        </Typography>
      </View>
    );
  };

  const renderCodeInputs = () => {
    return (
      <View style={styles.pinContainer}>
        <InputPin value={value} onChange={setValue} />;
      </View>
    );
  };

  const renderVerifireButton = () => {
    if (value.length < 4) return null;
    return (
      <ButtonBasic onPress={handleOtcVerifire}>
        {t("email_otc_screen_button_next")}
      </ButtonBasic>
    );
  };

  return (
    <View
      style={[styles.container, { backgroundColor: theme.colors.grey[900] }]}
    >
      {renderHeader()}
      {renderCodeInputs()}
      <View style={styles.buttonContainer}>{renderVerifireButton()}</View>
    </View>
  );
}

const stylesConfig = (theme: Theme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      justifyContent: "space-between",
    },
    logoContainer: {
      flex: 6,
      gap: theme.spacing[4],
      alignItems: "center",
      justifyContent: "center",
    },
    logoText: {
      width: 240,
      height: 240,
      marginTop: theme.spacing[2],
    },
    infoContainer: {
      alignItems: "center",
      marginBottom: theme.spacing[10],
    },
    infoText: {
      textAlign: "center",
      paddingHorizontal: theme.spacing[16],
    },
    pinContainer: {
      gap: theme.spacing[4],
      flex: 5,
      paddingHorizontal: theme.spacing[8],
      alignItems: "center",
      justifyContent: "center",
    },
    buttonContainer: {
      gap: theme.spacing[4],
      flex: 1,
      paddingBottom: theme.spacing[8],
      paddingHorizontal: theme.spacing[8],
      alignItems: "center",
      justifyContent: "center",
    },
  });
