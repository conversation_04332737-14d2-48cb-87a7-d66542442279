import { NewTrainingPlanResponse } from "@/types/trainingPlan";
import { create } from "zustand";
import { devtools } from "zustand/middleware";

interface State {
  trainingPlans: NewTrainingPlanResponse[];
  setTrainingPlans: (trainingPlans: NewTrainingPlanResponse[]) => void;
  getTrainingPlanById: (id: string) => NewTrainingPlanResponse | undefined;
}

export const useTrainingPlanStore = create<State>()(
  devtools(
    (set, get) => ({
      trainingPlans: [],
      setTrainingPlans: async (trainingPlans) => {
        try {
          set({ trainingPlans });
        } catch (error) {
          throw error;
        }
      },
      getTrainingPlanById: (id) => {
        return get().trainingPlans.find(
          (trainingPlan) => trainingPlan.id === id
        );
      },
    }),
    { name: "TrainingPlanStore" }
  )
);
