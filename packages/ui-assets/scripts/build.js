const fs = require("fs/promises");
const path = require("path");
const rimraf = require("rimraf");
const svgr = require("@svgr/core").default;
const camelcase = require("camelcase");
const babel = require("@babel/core");
const { minify } = require("terser");

const outputPath = "./dist";
const optimizedPath = "./optimized";

// Nowa funkcja do rekurencyjnego zbierania plików SVG
async function getAllSvgFiles(dir, fileList = []) {
  const files = await fs.readdir(dir, { withFileTypes: true });

  for (const file of files) {
    const filePath = path.join(dir, file.name);

    if (file.isDirectory()) {
      // Rekurencyjne przeszukiwanie podkatalogów
      await getAllSvgFiles(filePath, fileList);
    } else if (file.name.endsWith(".svg")) {
      // Zapisujemy ścieżkę względną do optimizedPath
      const relativePath = path.relative(optimizedPath, filePath);
      fileList.push(relativePath);
    }
  }

  return fileList;
}

async function transformSVGtoJSX(filePath, componentName, format) {
  const content = await fs.readFile(path.join(optimizedPath, filePath), "utf-8");
  const svgReactContent = await svgr(
    content,
    {
      icon: false,
      replaceAttrValues: { "#00497A": "{props.color || '#00497A'}" },
      svgProps: {
        width: 24,
        height: 24,
      },
    },
    { componentName }
  );
  const { code } = await babel.transformAsync(svgReactContent, {
    presets: [["@babel/preset-react", { useBuiltIns: true }]],
  });

  if (format === "esm") {
    const { code: minifiedCode } = await minify(code);
    return minifiedCode;
  }

  const replaceESM = code
    .replace('import * as React from "react";', 'const React = require("react");')
    .replace("export default", "module.exports =");
  const { code: minifiedCode } = await minify(replaceESM);
  return minifiedCode;
}

// New function to transform SVG to React Native compatible JSX
async function transformSVGtoRNJSX(filePath, componentName) {
  const content = await fs.readFile(path.join(optimizedPath, filePath), "utf-8");
  
  // Use SVGR with React Native config
  const svgReactContent = await svgr(
    content,
    {
      icon: false,
      native: true, // Enable React Native output
      replaceAttrValues: { "#00497A": "{props.color || '#00497A'}" },
      svgProps: {
        width: 24,
        height: 24,
      },
    },
    { componentName }
  );
  
  const { code } = await babel.transformAsync(svgReactContent, {
    presets: [["@babel/preset-react", { useBuiltIns: true }]],
  });

  const { code: minifiedCode } = await minify(code);
  return minifiedCode;
}

function indexFileContent(files, format, includeExtension = true) {
  let content = "";
  const extension = includeExtension ? ".js" : "";
  files.map(filePath => {
    // Generowanie nazwy komponentu z uwzględnieniem ścieżki
    const componentName = generateComponentName(filePath);
    const directoryString = `'./${componentName}${extension}'`;
    content +=
      format === "esm"
        ? `export { default as ${componentName} } from ${directoryString};\n`
        : `module.exports.${componentName} = require(${directoryString});\n`;
  });
  return content;
}

// Nowa funkcja do generowania nazw komponentów z uwzględnieniem ścieżki
function generateComponentName(filePath) {
  // Zamiana separatorów ścieżki na podkreślniki
  const normalizedPath = filePath.replace(/\\/g, "/");
  // Usunięcie rozszerzenia .svg i zamiana na PascalCase z dodaniem "Icon" na końcu
  const nameWithoutExt = normalizedPath.replace(/.svg$/, "");
  // Zamiana separatorów ścieżki na podkreślniki przed konwersją na camelcase
  const nameWithUnderscores = nameWithoutExt.replace(/\//g, "_");
  return `${camelcase(nameWithUnderscores, { pascalCase: true })}Icon`;
}

async function buildIcons(format = "esm") {
  let outDir = outputPath;
  if (format === "esm") {
    outDir = `${outputPath}/esm`;
  } else if (format === "native") {
    outDir = `${outputPath}/native`;
  } else {
    outDir = `${outputPath}/cjs`;
  }

  await fs.mkdir(outDir, { recursive: true });

  // Pobieramy wszystkie pliki SVG rekurencyjnie
  const files = await getAllSvgFiles(optimizedPath);

  await Promise.all(
    files.map(async filePath => {
      const componentName = generateComponentName(filePath);
      let content;
      let types;
      
      if (format === "native") {
        content = await transformSVGtoRNJSX(filePath, componentName);
        types = `import * as React from 'react';\nimport { SvgProps } from 'react-native-svg';\ndeclare function ${componentName}(props: SvgProps): JSX.Element;\nexport default ${componentName};\n`;
      } else {
        content = await transformSVGtoJSX(filePath, componentName, format);
        types = `import * as React from 'react';\ndeclare function ${componentName}(props: React.SVGProps<SVGSVGElement>): JSX.Element;\nexport default ${componentName};\n`;
      }

      // console.log(`- Creating file: ${componentName}.js`);
      await fs.writeFile(`${outDir}/${componentName}.js`, content, "utf-8");
      await fs.writeFile(`${outDir}/${componentName}.d.ts`, types, "utf-8");
    })
  );

  console.log(`- Creating file: index.js for ${format}`);
  await fs.writeFile(`${outDir}/index.js`, indexFileContent(files, format === "cjs" ? "cjs" : "esm"), "utf-8");
  await fs.writeFile(`${outDir}/index.d.ts`, indexFileContent(files, "esm", false), "utf-8");
}

(function main() {
  console.log("🏗 Building icon package...");
  new Promise(resolve => {
    rimraf(`${outputPath}/*`, resolve);
  })
    .then(() => Promise.all([buildIcons("cjs"), buildIcons("esm"), buildIcons("native")]))
    .then(() => console.log("✅ Finished building package."));
})();
