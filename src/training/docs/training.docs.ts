import {
  ApiT<PERSON>s,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBearerAuth,
  ApiQuery,
  ApiBody,
  getSchemaPath,
} from '@nestjs/swagger';
import { CreateTrainingRequestDto } from '../dto/create-training.dto';
import { UpdateTrainingRequestDto } from '../dto/update-training.dto';
import { TrainingStatus } from '../enums/training-status.enum';
import { TrainingEntity } from '../entities/training.entity';
import { HttpStatus, applyDecorators } from '@nestjs/common';

export class TrainingSwaggerDocs {
  static apiTags() {
    return applyDecorators(ApiTags('Trainings'));
  }

  static apiBearerAuth() {
    return applyDecorators(ApiBearerAuth('JWT'));
  }

  // Create training endpoint docs
  static createTrainingDocs() {
    return applyDecorators(
      ApiOperation({
        summary: 'Create a new training',
        description:
          'Creates a new training for the authenticated trainer. The training will be initially set to ACTIVE status.',
      }),
      ApiBody({
        type: CreateTrainingRequestDto,
        description: 'Training data to create',
      }),
      ApiResponse({
        status: HttpStatus.CREATED,
        description: 'The training has been successfully created.',
        type: TrainingEntity,
        schema: {
          $ref: getSchemaPath(TrainingEntity),
        },
      }),
      ApiResponse({
        status: HttpStatus.BAD_REQUEST,
        description: 'Invalid input data provided.',
      }),
      ApiResponse({
        status: HttpStatus.UNAUTHORIZED,
        description: 'User is not authenticated.',
      }),
      ApiResponse({
        status: HttpStatus.FORBIDDEN,
        description: 'User is not a trainer or does not have permission.',
      }),
    );
  }

  // Find all trainings endpoint docs
  static findAllTrainingsDocs() {
    return applyDecorators(
      ApiOperation({
        summary: 'Get all trainings for the trainer',
        description: 'Retrieves all trainings created by the authenticated trainer. Can be filtered by status.',
      }),
      ApiQuery({
        name: 'status',
        required: false,
        enum: TrainingStatus,
        description: 'Filter trainings by status (active, deleted)',
        example: TrainingStatus.ACTIVE,
      }),
      ApiResponse({
        status: HttpStatus.OK,
        description: 'List of trainings',
        type: [TrainingEntity],
        schema: {
          type: 'array',
          items: { $ref: getSchemaPath(TrainingEntity) },
        },
      }),
      ApiResponse({
        status: HttpStatus.UNAUTHORIZED,
        description: 'User is not authenticated.',
      }),
      ApiResponse({
        status: HttpStatus.FORBIDDEN,
        description: 'User is not a trainer or does not have permission.',
      }),
    );
  }

  // Find one training endpoint docs
  static findOneTrainingDocs() {
    return applyDecorators(
      ApiOperation({
        summary: 'Get training by id',
        description: 'Retrieves a specific training by its ID. Only accessible to the trainer who created it.',
      }),
      ApiParam({
        name: 'id',
        description: 'Training unique identifier (UUID)',
        type: 'string',
        example: 'training_id',
      }),
      ApiResponse({
        status: HttpStatus.OK,
        description: 'The training details',
        type: TrainingEntity,
        schema: {
          $ref: getSchemaPath(TrainingEntity),
        },
      }),
      ApiResponse({
        status: HttpStatus.BAD_REQUEST,
        description: 'Invalid ID format.',
      }),
      ApiResponse({
        status: HttpStatus.NOT_FOUND,
        description: 'Training not found.',
      }),
      ApiResponse({
        status: HttpStatus.UNAUTHORIZED,
        description: 'User is not authenticated.',
      }),
      ApiResponse({
        status: HttpStatus.FORBIDDEN,
        description: 'User is not a trainer or does not have permission to access this training.',
      }),
    );
  }

  // Update training endpoint docs
  static updateTrainingDocs() {
    return applyDecorators(
      ApiOperation({
        summary: 'Update a training',
        description: 'Updates an existing training. Only accessible to the trainer who created it.',
      }),
      ApiParam({
        name: 'id',
        description: 'Training unique identifier (UUID)',
        type: 'string',
        example: 'training_id',
      }),
      ApiBody({
        type: UpdateTrainingRequestDto,
        description: 'Training data to update',
      }),
      ApiResponse({
        status: HttpStatus.OK,
        description: 'The training has been successfully updated.',
        type: TrainingEntity,
        schema: {
          $ref: getSchemaPath(TrainingEntity),
        },
      }),
      ApiResponse({
        status: HttpStatus.BAD_REQUEST,
        description: 'Invalid input data or ID format.',
      }),
      ApiResponse({
        status: HttpStatus.NOT_FOUND,
        description: 'Training not found.',
      }),
      ApiResponse({
        status: HttpStatus.UNAUTHORIZED,
        description: 'User is not authenticated.',
      }),
      ApiResponse({
        status: HttpStatus.FORBIDDEN,
        description: 'User is not a trainer or does not have permission to update this training.',
      }),
    );
  }

  // Remove training endpoint docs
  static removeTrainingDocs() {
    return applyDecorators(
      ApiOperation({
        summary: 'Delete a training',
        description:
          'Soft deletes a training by setting its status to DELETED. Only accessible to the trainer who created it.',
      }),
      ApiParam({
        name: 'id',
        description: 'Training unique identifier (UUID)',
        type: 'string',
        example: 'training_id',
      }),
      ApiResponse({
        status: HttpStatus.OK,
        description: 'The training has been successfully deleted.',
        schema: {
          type: 'object',
          properties: {
            status: {
              type: 'string',
              example: TrainingStatus.DELETED,
              description: 'The new status of the training (deleted)',
            },
          },
        },
      }),
      ApiResponse({
        status: HttpStatus.BAD_REQUEST,
        description: 'Invalid ID format.',
      }),
      ApiResponse({
        status: HttpStatus.NOT_FOUND,
        description: 'Training not found.',
      }),
      ApiResponse({
        status: HttpStatus.UNAUTHORIZED,
        description: 'User is not authenticated.',
      }),
      ApiResponse({
        status: HttpStatus.FORBIDDEN,
        description: 'User is not a trainer or does not have permission to delete this training.',
      }),
    );
  }
}
