import React, { useState, forwardRef, useRef, useImperativeHandle } from "react";
import { View, TouchableOpacity, StyleSheet, Animated, Easing, ViewStyle, Text, TextStyle } from "react-native";
import { useTheme } from "../../theme";

interface Props {
  value?: boolean;
  labelLeft: string;
  labelRight: string;
  disabled?: boolean;
  size?: "small" | "medium" | "large";
  onValueChange?: (value: boolean) => void;
  style?: ViewStyle;
}

export const SwitchWithLabel = forwardRef<TouchableOpacity, Props>(
  ({ value = false, onValueChange, disabled = false, size = "medium", style, labelLeft, labelRight }, ref) => {
    const { theme } = useTheme();
    const { colors } = theme;

    const activeColor = colors.accent[300]; // Green color for active state
    const disabledColor = colors.grey[300]; // Grey color for disabled state
    const backgroundColor = colors.accent[200]; // Background color

    const [isEnabled, setIsEnabled] = useState(value);
    const animatedValue = useRef(new Animated.Value(value ? 1 : 0)).current;

    // Forward ref to TouchableOpacity
    const touchableRef = useRef<TouchableOpacity>(null);
    useImperativeHandle(ref, () => touchableRef.current as TouchableOpacity);

    // Update internal state when prop changes
    React.useEffect(() => {
      setIsEnabled(value);
      Animated.timing(animatedValue, {
        toValue: value ? 1 : 0,
        duration: 200,
        easing: Easing.bezier(0.4, 0.0, 0.2, 1),
        useNativeDriver: false,
      }).start();
    }, [value, animatedValue]);

    // Calculate text dimensions for responsive sizing
    const getTextWidth = (text: string, fontSize: number): number => {
      // Approximate text width calculation (can be refined with actual text measurement)
      return text.length * fontSize * 0.8; // Adding padding
    };

    const getSizes = () => {
      const fontSize = size === "small" ? 8 : size === "large" ? 12 : 10;
      const height = size === "small" ? 20 : size === "large" ? 36 : 28;
      const padding = 4;

      const leftTextWidth = getTextWidth(labelLeft, fontSize);
      const rightTextWidth = getTextWidth(labelRight, fontSize);
      const maxTextWidth = Math.max(leftTextWidth, rightTextWidth);

      const trackWidth = maxTextWidth * 2 + padding * 4; // padding on sides and between
      const thumbWidth = maxTextWidth;
      const thumbHeight = height - padding * 2; // padding top and bottom

      return {
        track: {
          width: trackWidth,
          height: height,
          borderRadius: height / 2,
          borderWidth: 2,
        },
        thumb: {
          width: thumbWidth,
          height: thumbHeight,
          borderWidth: 0,
        },
        padding: padding,
        fontSize: fontSize,
        maxTextWidth,
      };
    };

    const { track, thumb, padding, fontSize, maxTextWidth } = getSizes();

    const toggleSwitch = () => {
      if (disabled) return;

      const newValue = !isEnabled;
      setIsEnabled(newValue);

      Animated.timing(animatedValue, {
        toValue: newValue ? 1 : 0,
        duration: 200,
        easing: Easing.bezier(0.4, 0.0, 0.2, 1),
        useNativeDriver: false,
      }).start();

      if (onValueChange) {
        onValueChange(newValue);
      }
    };

    // Calculate the exact position for the thumb
    const leftPosition = padding - 4;
    const rightPosition = track.width - thumb.width - padding * 2 + 4;

    // Animated properties
    const translateX = animatedValue.interpolate({
      inputRange: [0, 1],
      outputRange: [leftPosition, rightPosition],
    });

    const styles = StyleSheet.create({
      track: {
        width: track.width,
        height: track.height,
        borderRadius: track.borderRadius,
        borderWidth: track.borderWidth,
        justifyContent: "center",
      },
      thumb: {
        width: thumb.width,
        height: thumb.height + 4,
        borderRadius: thumb.height,
        borderWidth: thumb.borderWidth,
        borderColor: disabled ? disabledColor : activeColor,
        justifyContent: "center",
        alignItems: "center",
      },
      labelText: {
        fontSize: fontSize,
        fontWeight: "600",
        color: colors.grey[700],
        textAlign: "center",
      },
      inactiveLabel: {
        position: "absolute",
        fontSize: fontSize,
        fontWeight: "600",
        color: colors.grey[700],
        justifyContent: "center",
        alignItems: "center",
        top: 0,
        bottom: 0,
        textAlignVertical: "center",
      },
    });

    return (
      <TouchableOpacity
        ref={touchableRef}
        activeOpacity={0.8}
        onPress={toggleSwitch}
        disabled={disabled}
        style={[style]}
        accessibilityRole="switch"
        accessibilityState={{ checked: isEnabled, disabled }}
      >
        <View
          style={[
            {
              padding: 2,
              borderWidth: 2,
              width: track.width + 8,
              borderRadius: track.height,
              borderColor: disabled ? disabledColor : activeColor,
            },
          ]}
        >
          <View
            style={[
              styles.track,
              {
                backgroundColor: backgroundColor,
                //borderColor: disabled ? disabledColor : activeColor,
              },
            ]}
          >
            {/* Inactive label - always visible in background */}
            <Text
              style={[
                styles.inactiveLabel,
                {
                  left: isEnabled ? padding : padding + maxTextWidth + padding,
                  width: maxTextWidth,
                  lineHeight: track.height - 4, // Adjust for border
                  textAlign: "center", // Left align when left text is inactive, right align when right text is inactive
                },
              ]}
            >
              {isEnabled ? labelLeft : labelRight}
            </Text>

            {/* Active thumb with label */}
            <Animated.View
              style={[
                styles.thumb,
                {
                  backgroundColor: disabled ? disabledColor : activeColor,
                  transform: [{ translateX }],
                },
              ]}
            >
              <Text style={styles.labelText}>{isEnabled ? labelRight : labelLeft}</Text>
            </Animated.View>
          </View>
        </View>
      </TouchableOpacity>
    );
  }
);
