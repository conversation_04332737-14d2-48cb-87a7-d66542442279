import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
  Put,
  Query,
  HttpStatus,
  HttpCode,
  UseInterceptors,
  ClassSerializerInterceptor,
} from '@nestjs/common';
import { TrainingService } from '../services/training.service';
import { CreateTrainingRequestDto, CreateTrainingResponseDto } from '../dto/create-training.dto';
import { UpdateTrainingRequestDto, UpdateTrainingResponseDto } from '../dto/update-training.dto';
// import { TrainerGuard } from '../guards/trainer.guard';
import { TrainingStatus } from '../enums/training-status.enum';
import { JwtAuthGuard } from '@app/tools/guards/jwt-auth.guard';
import { TrainingSwaggerDocs } from '../docs/training.docs';
import { TRAINING_ROUTES } from '@app/constants/routes/training-routes-names';
// import { TrainingEntity } from '../entities/training.entity';
import { UserEntity } from '@app/users/entities/user.entity';
import { CurrentBaseUser } from '@app/tools/decorators/current-user.decorator';
import { GetTrainingResponseDto } from '../dto/get-training.dto';

@TrainingSwaggerDocs.apiTags()
@Controller({ version: '1' })
@UseGuards(JwtAuthGuard /*TrainerGuard -> temp commented due to missing user creation with trainer role */)
@UseInterceptors(ClassSerializerInterceptor)
@TrainingSwaggerDocs.apiBearerAuth()
export class TrainingController {
  constructor(private readonly trainingService: TrainingService) {}

  @Post(TRAINING_ROUTES.TRAINING)
  @HttpCode(HttpStatus.CREATED)
  @TrainingSwaggerDocs.createTrainingDocs()
  async create(
    @Body() data: CreateTrainingRequestDto,
    @CurrentBaseUser() creator: UserEntity,
  ): Promise<CreateTrainingResponseDto> {
    return this.trainingService.createTraining(data, creator);
  }

  @Get(TRAINING_ROUTES.TRAINING)
  @HttpCode(HttpStatus.OK)
  @TrainingSwaggerDocs.findAllTrainingsDocs()
  async findAll(
    @Query('status') status: TrainingStatus,
    @CurrentBaseUser() user: UserEntity,
  ): Promise<GetTrainingResponseDto[]> {
    return this.trainingService.findAll(user, status);
  }

  @Get(TRAINING_ROUTES.SINGLE_TRAINING)
  @HttpCode(HttpStatus.OK)
  @TrainingSwaggerDocs.findOneTrainingDocs()
  async findOne(@Param('id') id: string, @CurrentBaseUser() user: UserEntity): Promise<GetTrainingResponseDto> {
    return this.trainingService.findOne(id, user);
  }

  @Put(TRAINING_ROUTES.SINGLE_TRAINING)
  @HttpCode(HttpStatus.OK)
  @TrainingSwaggerDocs.updateTrainingDocs()
  async update(
    @Param('id') id: string,
    @Body() data: UpdateTrainingRequestDto,
    @CurrentBaseUser() user: UserEntity,
  ): Promise<UpdateTrainingResponseDto> {
    return this.trainingService.update(id, data, user);
  }

  @Delete(TRAINING_ROUTES.SINGLE_TRAINING)
  @HttpCode(HttpStatus.OK)
  @TrainingSwaggerDocs.removeTrainingDocs()
  async remove(@Param('id') id: string, @CurrentBaseUser() user: UserEntity) {
    return this.trainingService.remove(id, user);
  }
}
