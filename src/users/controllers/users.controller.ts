import { Body, Controller, Get, Post, UseGuards } from '@nestjs/common';
import { USER_ROUTES } from '@app/constants/routes/user-routes-names';
import { UsersService } from '@app/users/services/users.service';
import { CreateUserDto } from '@app/users/dto/create-user.dto';
import { UserEntity } from '@app/users/entities/user.entity';
import { JwtAuthGuard } from '@app/tools/guards/jwt-auth.guard';
import { UsersSwaggerDocs } from '../docs/users.docs';
import { CreateOtcDto } from '@app/users/dto/create-otc.dto';

@UsersSwaggerDocs.apiTags()
@Controller({ version: '1' })
@UsersSwaggerDocs.apiBearerAuth()
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Post(USER_ROUTES.CREATE_USER)
  @UsersSwaggerDocs.createUserDocs()
  async createUser(@Body() createUserDto: CreateUserDto): Promise<UserEntity> {
    return await this.usersService.create(createUserDto);
  }

  @Post(USER_ROUTES.OTC)
  @UsersSwaggerDocs.createOtcCode()
  async createOtc(@Body() createOtcDto: CreateOtcDto): Promise<void> {
    return await this.usersService.createOtc(createOtcDto);
  }

  @Get(USER_ROUTES.ALL_USERS)
  @UseGuards(JwtAuthGuard)
  @UsersSwaggerDocs.getUsersDocs()
  async getUsers(): Promise<UserEntity[]> {
    return this.usersService.findAll();
  }
}
