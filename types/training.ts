import { ActivityStatus } from "@/enums/activityStatus";
import { NewExerciseResponse } from "./exercise";

export type NewTrainingRequestPayload = {
  name: string;
  description: string;
  tags: string;
  exercises: string[];
};

export type NewTrainingResponse = {
  id: string;
  name: string;
  description: string;
  tags: string;
  status: ActivityStatus;
  createdAt: string;
  updatedAt: string;
  creatorId: string;
  exercises?: NewExerciseResponse[];
};

export type DeleteTrainingResponse = {
  id: string;
  status: ActivityStatus;
};
