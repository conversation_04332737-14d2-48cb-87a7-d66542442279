import { Column, Entity, Index, PrimaryGeneratedColumn, BaseEntity } from 'typeorm';
import { ConsentType } from '@app/constants/models/consent-config';

@Entity({ name: 'consent_definitions' })
@Index(['code', 'version'], { unique: true })
export class ConsentDefinitionEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'enum', enum: ConsentType })
  code: ConsentType;

  @Column()
  version: string;

  @Column()
  content: string;

  @Column()
  shortDescription: string;

  @Column({ default: false })
  isRequired: boolean;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;
}
