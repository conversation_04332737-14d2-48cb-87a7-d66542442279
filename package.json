{"private": true, "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "clean": "turbo run clean && rm -rf node_modules", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "changeset": "changeset", "version-packages": "changeset version", "release": "turbo run build --filter=docs^... && changeset publish", "preview-storybook": "turbo run preview-storybook"}, "devDependencies": {"@changesets/cli": "^2.27.1", "prettier": "^3.2.5", "turbo": "^2.4.2"}, "name": "@vs/kit", "packageManager": "yarn@1.22.22", "workspaces": ["apps/**", "packages/**"]}