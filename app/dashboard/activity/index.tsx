import React from "react";
import { StyleSheet, View } from "react-native";
import { SwitchWithLabel, Theme, useTheme } from "@vs/kit-ui-expo";
import { GroupCard, WithImageCard } from "@/components/Cards";
import { useGroupList } from "@/hooks/useGroupList";
import { SectionWrapperWithMoreButton } from "@/components/Section";
import { SectionWrapperWithRoleBaseRender } from "@/components/Section/SectionWrapperWithRoleBaseRender";
import { EUserRole } from "@/enums/roles";
import { useRoleSwitcher } from "@/hooks/useRoleSwitcher";
import { PathEnum } from "@/enums/path";
import { Href, useRouter } from "expo-router";
import { useTranslation } from "react-i18next";
import ScrollViewWithSpacing from "@/components/ScrollableWithSpacing/ScrollViewWithSpacing";

const ActivitySettings = [
  {
    image: require("@/assets/images/training-photo.png"),
    title: "<PERSON>wiczenia",
    href: PathEnum.EXERCISE,
    description: "Podgląd i edycja pojedynczych ćwiczeń",
  },
  {
    image: require("@/assets/images/training-photo.png"),
    title: "Treningi",
    href: PathEnum.TRAINING,
    description: "Podgląd i edycja treningów",
  },
  {
    image: require("@/assets/images/training-photo.png"),
    title: "Plany treningowe",
    href: PathEnum.TRAINING_PLAN,
    description: "Podgląd i edycja planów treningowych",
  },
];

export default function TrainingHome() {
  const { theme } = useTheme();
  const { t } = useTranslation();
  const { handleChangeRole, baseUserOn } = useRoleSwitcher({
    extRole: EUserRole.TRAINER,
  });
  const router = useRouter();
  const { groupList } = useGroupList();
  const fnStyles = styles(theme);

  const renderGroups = () => {
    return (
      <SectionWrapperWithMoreButton
        title={t("activity_group_section_title")}
        bottomText={t("activity_group_section_more")}
      >
        {groupList.map((item, index) => (
          <GroupCard key={`group-${index}`} {...item} />
        ))}
      </SectionWrapperWithMoreButton>
    );
  };

  const renderActivitySetting = () => {
    return (
      <SectionWrapperWithRoleBaseRender allowedRoles={EUserRole.TRAINER}>
        <SectionWrapperWithMoreButton
          title={t("activity_activity_section_title")}
        >
          {ActivitySettings.map((item, index) => (
            <WithImageCard
              key={`activity-setting-${index}`}
              image={item.image}
              title={item.title}
              description={item.description}
              onPress={() => router.push(item.href as Href)}
            />
          ))}
        </SectionWrapperWithMoreButton>
      </SectionWrapperWithRoleBaseRender>
    );
  };

  return (
    <View style={fnStyles.container}>
      <SwitchWithLabel
        style={fnStyles.label}
        labelLeft={t("activity_switch_trainer")}
        labelRight={t("activity_switch_user")}
        onValueChange={handleChangeRole}
        value={baseUserOn}
      />
      <ScrollViewWithSpacing contentContainerStyle={fnStyles.contentContainer}>
        {renderGroups()}
        {renderActivitySetting()}
      </ScrollViewWithSpacing>
    </View>
  );
}

const styles = (theme: Theme) =>
  StyleSheet.create({
    container: {
      gap: theme.spacing[8],
      flexDirection: "column",
      flex: 1,
      backgroundColor: theme.colors.grey[900],
    },
    contentContainer: {
      gap: theme.spacing[12],
      paddingHorizontal: theme.spacing[4],
      flexDirection: "column",
      alignItems: "center",
      minHeight: "100%",
    },
    label: {
      alignSelf: "center",
    },
    groupCardContainer: {
      gap: theme.spacing[4],
      width: "100%",
    },
  });
