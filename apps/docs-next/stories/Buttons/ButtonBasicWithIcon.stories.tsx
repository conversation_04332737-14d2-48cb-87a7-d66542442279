import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { ButtonBasicWithIcon, Sizes } from "@vs/kit-ui-next";

const meta: Meta<typeof ButtonBasicWithIcon> = {
  title: "Components/Buttons/ButtonBasicWithIcon",
  component: ButtonBasicWithIcon,
  tags: ["autodocs"],
  argTypes: {
    children: {
      control: "text",
      description: "Button content",
    },
    disabled: {
      control: "boolean",
      description: "Button disabled state",
    },
    isLoading: {
      control: "boolean",
      description: "Button loading state",
    },
    leftIcon: {
      control: "object",
      description: "Left icon",
    },
    rightIcon: {
      control: "object",
      description: "Right icon",
    },
    size: {
      control: "select",
      description: "Button size",
      options: Object.values(Sizes),
    },
    onClick: { action: "clicked" },
  },
  decorators: [
    Story => (
      <div className="bg-main-700 p-8">
        <Story />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof ButtonBasicWithIcon>;

export const Default: Story = {
  args: {
    children: "Click me",
  },
};

export const Disabled: Story = {
  args: {
    children: "Disabled Button",
    disabled: true,
  },
};

export const WithoutChildren: Story = {
  args: {},
};

export const LongText: Story = {
  args: {
    children: "This is a button with very long text",
  },
};

export const Loading: Story = {
  args: {
    isLoading: true,
  },
};

export const Small: Story = {
  args: {
    size: Sizes.Small,
  },
};

export const Medium: Story = {
  args: {
    size: Sizes.Medium,
  },
};

export const Large: Story = {
  args: {
    size: Sizes.Large,
  },
};
