import 'reflect-metadata';
import { INestApplication } from '@nestjs/common';
import request from 'supertest';

import { loginAndAssertUser, registerAndAssertUser } from '../__helpers/auth';
import { buildTestApp } from '../__setup/app.factory';
import { generateTestUser } from '../__helpers/user';
import { ConsentDefinitionEntity } from '@app/consents/entities/consent-definition.entity';
import { createAndAssertConsent, generateGDPRConsentPayload } from '../__helpers/consents';
import { CreateGroupRequestDto } from '@app/groups/dto/create-group.dto';
import { addAndAssertUserToGroup, createAndAssertGroup, getAndAssertGroupById } from '../__helpers/group';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';

describe('/POST /v1/group/join', () => {
  let app: INestApplication;
  let groupCreatorAccessToken: string;
  let consent: ConsentDefinitionEntity;

  beforeAll(async () => {
    app = await buildTestApp();

    const consentPayload = generateGDPRConsentPayload();
    consent = await createAndAssertConsent(app, consentPayload);

    const testUser = generateTestUser([consent.id]);
    await registerAndAssertUser(app, testUser);
    groupCreatorAccessToken = (await loginAndAssertUser(app, testUser)).accessToken;
  });

  afterAll(async () => {
    await app.close();
  });

  // TODO: only trainer should be able to create a group
  it('should user join a group', async () => {
    const createGroupPayload: CreateGroupRequestDto = {
      name: 'Test Exercise E2E',
      description: 'This is a test exercise',
      tags: 'e2e|test',
    };
    const group = await createAndAssertGroup(app, createGroupPayload, groupCreatorAccessToken);

    // Retrive invitation token from invitationUrl
    const url = new URL(group.invitationUrl);
    const invitationToken = url.searchParams.get('token');

    const testUser = generateTestUser([consent.id]);
    const testUserRegistered = await registerAndAssertUser(app, testUser);
    const accessToken = (await loginAndAssertUser(app, testUser)).accessToken;

    const response = await request(app.getHttpServer())
      .post(`/v1/group/join?invitationToken=${invitationToken}`)
      .set('Authorization', `Bearer ${accessToken}`)
      .send();

    expect(response.status).toBe(200);
    expect(response.body.message).toBe('User successfully joined group');

    // Verify that user is added to group
    const groupAfterJoining = await getAndAssertGroupById(app, group.id, accessToken);

    expect(groupAfterJoining.groupUsers.length).toBe(2);
    expect(groupAfterJoining.groupUsers[0].user.id).toBe(group.ownerId);
    expect(groupAfterJoining.groupUsers[1].user.id).toBe(testUserRegistered.id);
  });

  it('should return 401 when user is not authenticated', async () => {
    const createGroupPayload: CreateGroupRequestDto = {
      name: 'Test Exercise E2E',
      description: 'This is a test exercise',
      tags: 'e2e|test',
    };
    const group = await createAndAssertGroup(app, createGroupPayload, groupCreatorAccessToken);

    // Retrive invitation token from invitationUrl
    const url = new URL(group.invitationUrl);
    const invitationToken = url.searchParams.get('token');

    const response = await request(app.getHttpServer())
      .post(`/v1/group/join?invitationToken=${invitationToken}`)
      .send();

    expect(response.status).toBe(401);
    expect(response.body.message).toBe('Unauthorized');
  });

  it('should return 401 when invitation token is invalid', async () => {
    const response = await request(app.getHttpServer()).post(`/v1/group/join?invitationToken=invalid`).send();

    expect(response.status).toBe(401);
    expect(response.body.message).toBe('Unauthorized');
  });

  it('should return 401 when invitation token is expired', async () => {
    const jwtService = app.get(JwtService);
    const configService = app.get(ConfigService);

    const expiredToken = jwtService.sign(
      { groupId: 'dummy-id' },
      {
        secret: configService.getOrThrow('GROUP_INVITATION_SECRET'),
        expiresIn: '-1s',
      },
    );

    const response = await request(app.getHttpServer())
      .post(`/v1/group/join?invitationToken=${expiredToken}`)
      .set('Authorization', `Bearer ${groupCreatorAccessToken}`)
      .send();

    expect(response.status).toBe(401);
    expect(response.body.message).toBe('Invalid invitation token');
  });

  it('should return 404 if group from token does not exist', async () => {
    const jwtService = app.get(JwtService);
    const configService = app.get(ConfigService);

    const validButFakeToken = jwtService.sign(
      { groupId: '00000000-0000-0000-0000-000000000000' },
      {
        secret: configService.getOrThrow('GROUP_INVITATION_SECRET'),
        expiresIn: '1h',
      },
    );

    const response = await request(app.getHttpServer())
      .post(`/v1/group/join?invitationToken=${validButFakeToken}`)
      .set('Authorization', `Bearer ${groupCreatorAccessToken}`)
      .send();

    expect(response.status).toBe(404);
    expect(response.body.message).toBe('Group with id 00000000-0000-0000-0000-000000000000 not found');
  });
});

describe('Already joined group', () => {
  let app: INestApplication;
  let groupCreatorAccessToken: string;
  let consent: ConsentDefinitionEntity;

  beforeAll(async () => {
    app = await buildTestApp();

    const consentPayload = generateGDPRConsentPayload();
    consent = await createAndAssertConsent(app, consentPayload);

    const testUser = generateTestUser([consent.id]);
    await registerAndAssertUser(app, testUser);
    groupCreatorAccessToken = (await loginAndAssertUser(app, testUser)).accessToken;
  });

  afterAll(async () => {
    await app.close();
  });

  // TODO: only trainer should be able to create a group
  it('should return 400 when user is already a member of the group', async () => {
    const createGroupPayload: CreateGroupRequestDto = {
      name: 'Test Exercise E2E',
      description: 'This is a test exercise',
      tags: 'e2e|test',
    };
    const group = await createAndAssertGroup(app, createGroupPayload, groupCreatorAccessToken);

    // Retrive invitation token from invitationUrl
    const url = new URL(group.invitationUrl);
    const invitationToken = url.searchParams.get('token');

    const testUser = generateTestUser([consent.id]);
    const testUserRegistered = await registerAndAssertUser(app, testUser);
    const accessToken = (await loginAndAssertUser(app, testUser)).accessToken;

    await addAndAssertUserToGroup(app, group.id, testUserRegistered.id, groupCreatorAccessToken);

    const response = await request(app.getHttpServer())
      .post(`/v1/group/join?invitationToken=${invitationToken}`)
      .set('Authorization', `Bearer ${accessToken}`)
      .send();

    expect(response.status).toBe(400);
    expect(response.body.message).toBe('User is already a member of this group');
  });
});
