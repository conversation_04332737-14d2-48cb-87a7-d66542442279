import { ActivityStatus } from "@/enums/activityStatus";
import { NewTrainingResponse } from "./training";

export type NewTrainingPlanRequestPayload = {
  name: string;
  description: string;
  trainings: string[];
};

export type NewTrainingPlanResponse = {
  id: string;
  name: string;
  description: string;
  status: ActivityStatus;
  createdAt: string;
  updatedAt: string;
  creatorId: string;
  trainings?: NewTrainingResponse[];
};

export type DeleteTrainingPlanResponse = {
  id: string;
  status: ActivityStatus;
};
