import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule } from '@nestjs/config';
import { GroupsController } from './controllers/groups.controller';
import { GroupsService } from './services/groups.service';
import { GroupEntity } from './entities/group.entity';
import { GroupUserEntity } from './entities/group-user.entity';

@Module({
  imports: [TypeOrmModule.forFeature([GroupEntity, GroupUserEntity]), JwtModule.register({}), ConfigModule],
  controllers: [GroupsController],
  providers: [GroupsService],
  exports: [GroupsService],
})
export class GroupsModule {}
