import { ApiProperty } from '@nestjs/swagger';
import { ConsentType } from '@app/constants/models/consent-config';

export class CreateConsentDto {
  @ApiProperty({ enum: ConsentType })
  code: ConsentType;

  @ApiProperty()
  version: string;

  @ApiProperty()
  content: string;

  @ApiProperty({ description: 'Short description for consent display' })
  shortDescription: string;

  @ApiProperty()
  isRequired: boolean;
}
