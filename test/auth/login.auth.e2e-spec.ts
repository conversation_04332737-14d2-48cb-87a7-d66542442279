import { ConsentDefinitionEntity } from '@app/consents/entities/consent-definition.entity';
import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { registerAndAssertUser } from '../__helpers/auth';
import { createAndAssertConsent, generateGDPRConsentPayload } from '../__helpers/consents';
import { generateTestUser } from '../__helpers/user';
import { buildTestApp } from '../__setup/app.factory';

describe('Auth/Login (e2e)', () => {
  let app: INestApplication;
  let consent: ConsentDefinitionEntity;

  beforeAll(async () => {
    app = await buildTestApp();

    const consentPayload = generateGDPRConsentPayload();
    consent = await createAndAssertConsent(app, consentPayload);
  });

  afterAll(async () => {
    await app.close();
  });

  describe('/POST /v1/user/login', () => {
    it('should return access and refresh token for valid credentials', async () => {
      const user = generateTestUser([consent.id]);
      await registerAndAssertUser(app, user);
      const { email, password } = user;

      const response = await request(app.getHttpServer()).post('/v1/user/login').send({ email, password });

      // TODO: I think we should return only accessToken and refreshToken
      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        accessToken: expect.any(String),
        refreshToken: expect.any(String),
        expiresIn: {
          accessToken: expect.any(String),
          refreshToken: expect.any(String),
        },
        user: {
          id: expect.any(String),
          email: expect.any(String),
          roles: expect.any(Array),
          name: expect.any(String),
        },
      });
    });

    it('should return 401 for invalid password', async () => {
      const user = generateTestUser([consent.id]);
      await registerAndAssertUser(app, user);
      const { email } = user;

      const response = await request(app.getHttpServer())
        .post('/v1/user/login')
        .send({ email, password: 'wrongpassword123' });

      expect(response.status).toBe(401);
      expect(response.body.message).toBe('Invalid credentials');
    });

    it('should return 401 for non-existing user', async () => {
      const response = await request(app.getHttpServer())
        .post('/v1/user/login')
        .send({ email: '<EMAIL>', password: 'irrelevant' });

      expect(response.status).toBe(401);
      expect(response.body.message).toBe('Unauthorized');
    });
  });
});
