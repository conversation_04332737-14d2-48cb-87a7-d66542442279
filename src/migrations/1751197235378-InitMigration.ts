import { MigrationInterface, QueryRunner } from "typeorm";

export class InitMigration1751197235378 implements MigrationInterface {
    name = 'InitMigration1751197235378'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "refresh_tokens" ("id" SERIAL NOT NULL, "token" character varying, "ip" character varying, "device" character varying, "expiresAt" TIMESTAMP NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "userId" uuid, CONSTRAINT "PK_7d8bee0204106019488c4c50ffa" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."consent_definitions_code_enum" AS ENUM('GDPR', 'POLICY')`);
        await queryRunner.query(`CREATE TABLE "consent_definitions" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "code" "public"."consent_definitions_code_enum" NOT NULL, "version" character varying NOT NULL, "content" character varying NOT NULL, "shortDescription" character varying NOT NULL, "isRequired" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_835a6b5b22352bf16c20bd28fd4" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_26bd1d0dae6da6d3f87ebd7587" ON "consent_definitions" ("code", "version") `);
        await queryRunner.query(`CREATE TABLE "user_consents" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "acceptedAt" TIMESTAMP NOT NULL DEFAULT now(), "userId" uuid, "consentDefinitionId" uuid, CONSTRAINT "PK_65e4c6d6204ad8719abf4b30326" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "users" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "email" character varying NOT NULL, "name" character varying NOT NULL, "password" character varying NOT NULL, "roles" text NOT NULL DEFAULT 'USER', "createdAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "UQ_97672ac88f789774dd47f7c8be3" UNIQUE ("email"), CONSTRAINT "UQ_51b8b26ac168fbe7d6f5653e6cf" UNIQUE ("name"), CONSTRAINT "PK_a3ffb1c0c8416b9fc6f907b7433" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."exercises_difficultylevel_enum" AS ENUM('easy', 'medium', 'hard')`);
        await queryRunner.query(`CREATE TYPE "public"."exercises_status_enum" AS ENUM('pending', 'verified', 'rejected', 'deleted')`);
        await queryRunner.query(`CREATE TABLE "exercises" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" character varying NOT NULL, "description" text NOT NULL, "tags" character varying, "difficultyLevel" "public"."exercises_difficultylevel_enum" NOT NULL DEFAULT 'medium', "videoUrl" character varying, "imageUrl" character varying, "status" "public"."exercises_status_enum" NOT NULL DEFAULT 'pending', "statusMessage" character varying, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "creatorId" uuid NOT NULL, CONSTRAINT "PK_c4c46f5fa89a58ba7c2d894e3c3" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."trainings_status_enum" AS ENUM('active', 'deleted')`);
        await queryRunner.query(`CREATE TABLE "trainings" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" character varying NOT NULL, "description" text NOT NULL, "tags" character varying, "status" "public"."trainings_status_enum" NOT NULL DEFAULT 'active', "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "creatorId" uuid NOT NULL, CONSTRAINT "PK_b67237502b175163e47dc85018d" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "group_users" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "group_id" uuid, "user_id" uuid, CONSTRAINT "PK_5df8869cdeffc693bd083153bcf" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "groups" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" character varying NOT NULL, "description" character varying, "tags" character varying, "invitationUrl" character varying NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "ownerId" character varying NOT NULL, CONSTRAINT "PK_659d1483316afb28afd3a90646e" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "training_plan_groups" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "training_plan_id" uuid NOT NULL, "group_id" uuid NOT NULL, "start_date" date NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_b4416e8c40564bd5681a4350809" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."training_plans_status_enum" AS ENUM('active', 'deleted')`);
        await queryRunner.query(`CREATE TABLE "training_plans" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" character varying NOT NULL, "description" text NOT NULL, "status" "public"."training_plans_status_enum" NOT NULL DEFAULT 'active', "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "creatorId" uuid NOT NULL, CONSTRAINT "PK_246975cb895b51662b90515a390" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "training_exercises" ("training_id" uuid NOT NULL, "exercise_id" uuid NOT NULL, CONSTRAINT "PK_31b88086c29615c1c3a82a01374" PRIMARY KEY ("training_id", "exercise_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_2c1fcbc16208fe3bff536ddca5" ON "training_exercises" ("training_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_0a7c29d2b153dd18b577c9059d" ON "training_exercises" ("exercise_id") `);
        await queryRunner.query(`CREATE TABLE "training_plan_trainings" ("training_plan_id" uuid NOT NULL, "training_id" uuid NOT NULL, CONSTRAINT "PK_23a5cad7150ad24e9fc65c8eac9" PRIMARY KEY ("training_plan_id", "training_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_45ca5231e88b422575cd81b474" ON "training_plan_trainings" ("training_plan_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_2c485373a1f199fdb151421f8f" ON "training_plan_trainings" ("training_id") `);
        await queryRunner.query(`ALTER TABLE "refresh_tokens" ADD CONSTRAINT "FK_610102b60fea1455310ccd299de" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_consents" ADD CONSTRAINT "FK_7a8097efad75fcbc548d467d648" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_consents" ADD CONSTRAINT "FK_3a0fdc97d22dae17840b340dc17" FOREIGN KEY ("consentDefinitionId") REFERENCES "consent_definitions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "exercises" ADD CONSTRAINT "FK_1fffe0d38ec8748f537af87b276" FOREIGN KEY ("creatorId") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "trainings" ADD CONSTRAINT "FK_8c636eb3e5972bd1a72d6335fc4" FOREIGN KEY ("creatorId") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "group_users" ADD CONSTRAINT "FK_be6db0d7dabab05d97233d19f61" FOREIGN KEY ("group_id") REFERENCES "groups"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "group_users" ADD CONSTRAINT "FK_eba8af4e65056abb4c5f62556c6" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "training_plan_groups" ADD CONSTRAINT "FK_8944bf46f8677fe306182ceb9b5" FOREIGN KEY ("training_plan_id") REFERENCES "training_plans"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "training_plan_groups" ADD CONSTRAINT "FK_39b0d791a8e8f75a6b66672f6d3" FOREIGN KEY ("group_id") REFERENCES "groups"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "training_plans" ADD CONSTRAINT "FK_926ca086365248594d09efb000d" FOREIGN KEY ("creatorId") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "training_exercises" ADD CONSTRAINT "FK_2c1fcbc16208fe3bff536ddca57" FOREIGN KEY ("training_id") REFERENCES "trainings"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "training_exercises" ADD CONSTRAINT "FK_0a7c29d2b153dd18b577c9059d1" FOREIGN KEY ("exercise_id") REFERENCES "exercises"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "training_plan_trainings" ADD CONSTRAINT "FK_45ca5231e88b422575cd81b474c" FOREIGN KEY ("training_plan_id") REFERENCES "training_plans"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "training_plan_trainings" ADD CONSTRAINT "FK_2c485373a1f199fdb151421f8fd" FOREIGN KEY ("training_id") REFERENCES "trainings"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "training_plan_trainings" DROP CONSTRAINT "FK_2c485373a1f199fdb151421f8fd"`);
        await queryRunner.query(`ALTER TABLE "training_plan_trainings" DROP CONSTRAINT "FK_45ca5231e88b422575cd81b474c"`);
        await queryRunner.query(`ALTER TABLE "training_exercises" DROP CONSTRAINT "FK_0a7c29d2b153dd18b577c9059d1"`);
        await queryRunner.query(`ALTER TABLE "training_exercises" DROP CONSTRAINT "FK_2c1fcbc16208fe3bff536ddca57"`);
        await queryRunner.query(`ALTER TABLE "training_plans" DROP CONSTRAINT "FK_926ca086365248594d09efb000d"`);
        await queryRunner.query(`ALTER TABLE "training_plan_groups" DROP CONSTRAINT "FK_39b0d791a8e8f75a6b66672f6d3"`);
        await queryRunner.query(`ALTER TABLE "training_plan_groups" DROP CONSTRAINT "FK_8944bf46f8677fe306182ceb9b5"`);
        await queryRunner.query(`ALTER TABLE "group_users" DROP CONSTRAINT "FK_eba8af4e65056abb4c5f62556c6"`);
        await queryRunner.query(`ALTER TABLE "group_users" DROP CONSTRAINT "FK_be6db0d7dabab05d97233d19f61"`);
        await queryRunner.query(`ALTER TABLE "trainings" DROP CONSTRAINT "FK_8c636eb3e5972bd1a72d6335fc4"`);
        await queryRunner.query(`ALTER TABLE "exercises" DROP CONSTRAINT "FK_1fffe0d38ec8748f537af87b276"`);
        await queryRunner.query(`ALTER TABLE "user_consents" DROP CONSTRAINT "FK_3a0fdc97d22dae17840b340dc17"`);
        await queryRunner.query(`ALTER TABLE "user_consents" DROP CONSTRAINT "FK_7a8097efad75fcbc548d467d648"`);
        await queryRunner.query(`ALTER TABLE "refresh_tokens" DROP CONSTRAINT "FK_610102b60fea1455310ccd299de"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_2c485373a1f199fdb151421f8f"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_45ca5231e88b422575cd81b474"`);
        await queryRunner.query(`DROP TABLE "training_plan_trainings"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_0a7c29d2b153dd18b577c9059d"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_2c1fcbc16208fe3bff536ddca5"`);
        await queryRunner.query(`DROP TABLE "training_exercises"`);
        await queryRunner.query(`DROP TABLE "training_plans"`);
        await queryRunner.query(`DROP TYPE "public"."training_plans_status_enum"`);
        await queryRunner.query(`DROP TABLE "training_plan_groups"`);
        await queryRunner.query(`DROP TABLE "groups"`);
        await queryRunner.query(`DROP TABLE "group_users"`);
        await queryRunner.query(`DROP TABLE "trainings"`);
        await queryRunner.query(`DROP TYPE "public"."trainings_status_enum"`);
        await queryRunner.query(`DROP TABLE "exercises"`);
        await queryRunner.query(`DROP TYPE "public"."exercises_status_enum"`);
        await queryRunner.query(`DROP TYPE "public"."exercises_difficultylevel_enum"`);
        await queryRunner.query(`DROP TABLE "users"`);
        await queryRunner.query(`DROP TABLE "user_consents"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_26bd1d0dae6da6d3f87ebd7587"`);
        await queryRunner.query(`DROP TABLE "consent_definitions"`);
        await queryRunner.query(`DROP TYPE "public"."consent_definitions_code_enum"`);
        await queryRunner.query(`DROP TABLE "refresh_tokens"`);
    }

}
