import React from "react";
import {
  View,
  Image,
  StyleSheet,
  TouchableOpacity,
  ImageSourcePropType,
} from "react-native";
import { Typography, useTheme, BorderBoxWithGradient } from "@vs/kit-ui-expo";

interface Props {
  image: ImageSourcePropType;
  title: string;
  description: string;
  onPress?: () => void;
}

export const WithImageCard = ({
  image,
  title,
  description,
  onPress,
}: Props) => {
  const { theme } = useTheme();

  const renderImage = () => (
    <Image source={image} style={styles.image} resizeMode="cover" />
  );

  const renderContent = () => (
    <View
      style={[
        {
          gap: theme.spacing[1],
          width: 200,
        },
      ]}
    >
      <Typography variant="Heading5" color={theme.colors.grey[50]}>
        {title}
      </Typography>
      <Typography variant="BodyS" color={theme.colors.grey[50]}>
        {description}
      </Typography>
    </View>
  );

  const renderCardBody = () => (
    <View
      style={[
        styles.rowLayout,
        {
          gap: theme.spacing[4],
        },
      ]}
    >
      {renderImage()}
      {renderContent()}
    </View>
  );

  return (
    <TouchableOpacity onPress={onPress}>
      <BorderBoxWithGradient
        style={[
          {
            borderColor: theme.colors.accent[400],
            padding: theme.spacing[4],
          },
        ]}
        gradientColors={["#1b2214", "#28351a", "#354720"]}
      >
        {renderCardBody()}
      </BorderBoxWithGradient>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  rowLayout: {
    flexDirection: "row",
    alignItems: "center",
  },
  textContent: {},
  image: {
    width: 86,
    height: 86,
    borderRadius: 8,
  },
});
