import Link from "next/link";
import { LinkProps } from "next/link";

interface Props
  extends LinkProps,
    Omit<React.AnchorHTMLAttributes<HTMLAnchorElement>, "href"> {
  children?: React.ReactNode;
}

export const LinkBorder = ({ children, href, ...props }: Props) => {
  return (
    <Link
      href={href}
      className="border-gradient disabled:opacity-50 whitespace-nowrap disabled:cursor-not-allowed text-main-600 p-4 overflow-hidden w-full text-center rounded-md"
      {...props}
    >
      {children ?? "Click"}
    </Link>
  );
};
