import {
  ClassSerializerInterceptor,
  Controller,
  Get,
  Post,
  Req,
  UseGuards,
  UseInterceptors,
  HttpCode,
  HttpStatus,
  UnprocessableEntityException,
} from '@nestjs/common';
import { USER_ROUTES } from '@app/constants/routes/user-routes-names';
import { LocalAuthGuard } from '@app/tools/guards/local-auth.guard';
import { CurrentBaseUser } from '@app/tools/decorators/current-user.decorator';
import { UserEntity } from '@app/users/entities/user.entity';
import { AuthService } from '@app/auth/services/auth.service';
import { JwtRefreshAuthGuard } from '@app/tools/guards/jwt-refresh-auth.guard';
import { AUTH_ROUTES } from '@app/constants/routes/auth-routes-names';
import { GoogleAuthGuard } from '@app/tools/guards/google-auth.guard';
import { type CustomRequest } from '@app/constants/interfaces/custom-request.interface';
import { JwtAuthGuard } from '@app/tools/guards/jwt-auth.guard';
import { AuthSwaggerDocs } from '../docs/auth.docs';
import { type GoogleAuthResult } from '@app/constants/interfaces/google-auth-result.interface';
import { ConsentsService } from '@app/consents/services/consents.service';
import { authMessages } from '@app/constants/messages/auth-messages';
import { LoginResponseDto } from '@app/auth/dto/login-response.dto';

@AuthSwaggerDocs.apiTags()
@Controller({ version: '1' })
@UseInterceptors(ClassSerializerInterceptor)
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly consentsService: ConsentsService,
  ) {}

  // TODO: I think we should return only accessToken and refreshToken
  @Post(USER_ROUTES.LOGIN_USER)
  @UseGuards(LocalAuthGuard)
  @HttpCode(HttpStatus.OK)
  @AuthSwaggerDocs.loginDocs()
  async login(@CurrentBaseUser() user: UserEntity): Promise<LoginResponseDto> {
    return await this.authService.login(user);
  }

  // TODO: I think we should return only accessToken and refreshToken
  @Post(USER_ROUTES.REFRESH_TOKEN)
  @UseGuards(JwtRefreshAuthGuard)
  @HttpCode(HttpStatus.OK)
  @AuthSwaggerDocs.refreshTokenDocs()
  // TODO: We could use @CurrentBaseUser() user: UserEntity
  async refreshToken(@Req() req: CustomRequest) {
    const user: UserEntity = req.user as UserEntity;
    const refreshToken = req.refreshToken as string;
    return this.authService.refreshToken(user, refreshToken, req);
  }

  @Get(AUTH_ROUTES.GOOGLE_LOGIN)
  @UseGuards(GoogleAuthGuard)
  @AuthSwaggerDocs.loginGoogleDocs()
  loginGoogle() {}

  @Get(AUTH_ROUTES.GOOGLE_CALLBACK)
  @UseGuards(GoogleAuthGuard)
  @AuthSwaggerDocs.googleCallbackDocs()
  async googleCallback(@CurrentBaseUser() data: GoogleAuthResult) {
    const { user, hasAllConsents } = data;

    if (!hasAllConsents) {
      const latestConsents = await this.consentsService.getLatestConsents();
      const userConsents = await this.consentsService.getUserConsents(user.id);

      const missingConsents = latestConsents.filter((latestConsent) => {
        return !userConsents.some(
          (userConsent) =>
            userConsent.consentDefinition.code === latestConsent.code &&
            userConsent.consentDefinition.version === latestConsent.version,
        );
      });

      throw new UnprocessableEntityException({
        message: authMessages.consents.requiredUserAcceptance,
        userId: user.id,
        missingConsents: missingConsents.map((c) => ({
          id: c.id,
          code: c.code,
          version: c.version,
          isRequired: c.isRequired,
          content: c.content,
        })),
      });
    }

    return this.authService.login(user);
  }

  @Post(USER_ROUTES.LOGOUT)
  @UseGuards(JwtRefreshAuthGuard)
  @HttpCode(HttpStatus.OK)
  @AuthSwaggerDocs.apiBearerAuth()
  @AuthSwaggerDocs.logoutDocs()
  // TODO: We could use @CurrentBaseUser() user: UserEntity
  async logout(@Req() req: CustomRequest) {
    const user = req.user as UserEntity;
    const refreshToken = req.refreshToken as string;
    return this.authService.logout(user, refreshToken);
  }

  // TODO: Why do we need this endpoint?
  @Post(USER_ROUTES.LOGOUT_ALL)
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.OK)
  @AuthSwaggerDocs.apiBearerAuth()
  @AuthSwaggerDocs.logoutAllDocs()
  async logoutAll(@CurrentBaseUser() user: UserEntity) {
    return this.authService.logoutAll(user);
  }

  // TODO: Why do we need this endpoint?
  @Get(USER_ROUTES.SESSIONS)
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.OK)
  @AuthSwaggerDocs.apiBearerAuth()
  @AuthSwaggerDocs.getSessionsDocs()
  async getSessions(@CurrentBaseUser() user: UserEntity) {
    return this.authService.getActiveSessions(user);
  }
}
