import React, { useState } from "react";
import { View, Text } from "react-native";
import type { Meta, StoryObj } from "@storybook/react";
import { RadioButtonBasic, ThemeProvider } from "@vs/kit-ui-expo";

// Define the props type for RadioButtonBasic
interface RadioButtonProps {
  label?: string;
  checked?: boolean;
  disabled?: boolean;
  size?: 'small' | 'medium' | 'large';
  onPress?: () => void;
}

const meta = {
  title: "Components/RadioButtons/RadioButtonBasic",
  component: RadioButtonBasic,
  argTypes: {
    onPress: { action: "pressed the radio button" },
    size: {
      control: "select",
      options: ["small", "medium", "large"],
    },
    checked: {
      control: "boolean",
    },
    disabled: {
      control: "boolean",
    },
    label: {
      control: "text",
    },
  },
  args: {
    label: "Radio Button",
    size: "medium",
    checked: false,
    disabled: false,
  },
  decorators: [
    Story => (
      <ThemeProvider>
        <View style={{ padding: 16, alignItems: "flex-start" }}>
          <Story />
        </View>
      </ThemeProvider>
    ),
  ],
} satisfies Meta<typeof RadioButtonBasic>;

export default meta;

type Story = StoryObj<typeof meta>;

// Interactive radio button component
const InteractiveRadioButton = (props: RadioButtonProps) => {
  const [isChecked, setIsChecked] = useState(props.checked || false);
  
  return (
    <RadioButtonBasic 
      {...props} 
      checked={isChecked} 
      onPress={() => {
        if (!props.disabled) {
          setIsChecked(!isChecked);
        }
      }} 
    />
  );
};

export const Default: Story = {
  render: (args) => <InteractiveRadioButton {...args} />
};

export const Small: Story = {
  render: (args) => <InteractiveRadioButton {...args} size="small" />
};

export const Medium: Story = {
  render: (args) => <InteractiveRadioButton {...args} size="medium" />
};

export const Large: Story = {
  render: (args) => <InteractiveRadioButton {...args} size="large" />
};

export const Checked: Story = {
  render: (args) => <InteractiveRadioButton {...args} checked={true} />
};

export const Disabled: Story = {
  render: (args) => <InteractiveRadioButton {...args} disabled={true} />
};

export const CheckedDisabled: Story = {
  render: (args) => <InteractiveRadioButton {...args} checked={true} disabled={true} />
};

export const WithoutLabel: Story = {
  render: (args) => <InteractiveRadioButton {...args} label={undefined} />
};

// Radio button group component
const RadioButtonGroup = () => {
  const [selectedOption, setSelectedOption] = useState("option1");
  
  const options = [
    { id: "option1", label: "Option 1" },
    { id: "option2", label: "Option 2" },
    { id: "option3", label: "Option 3" },
    { id: "option4", label: "Option 4 (Disabled)", disabled: true }
  ];
  
  return (
    <View>
      <Text style={{ marginBottom: 16, fontWeight: "bold" }}>Select an option:</Text>
      {options.map((option) => (
        <View key={option.id} style={{ marginBottom: 12 }}>
          <RadioButtonBasic
            label={option.label}
            checked={selectedOption === option.id}
            disabled={option.disabled}
            onPress={() => {
              if (!option.disabled) {
                setSelectedOption(option.id);
              }
            }}
          />
        </View>
      ))}
      <Text style={{ marginTop: 8 }}>Selected: {options.find(o => o.id === selectedOption)?.label}</Text>
    </View>
  );
};

export const Group: Story = {
  render: () => <RadioButtonGroup />
};

// All sizes demonstration
export const AllSizes: Story = {
  render: () => {
    const [selectedSize, setSelectedSize] = useState("medium");
    
    return (
      <View>
        <Text style={{ marginBottom: 16, fontWeight: "bold" }}>Select a size:</Text>
        <View style={{ marginBottom: 12 }}>
          <RadioButtonBasic
            label="Small"
            size="small"
            checked={selectedSize === "small"}
            onPress={() => setSelectedSize("small")}
          />
        </View>
        <View style={{ marginBottom: 12 }}>
          <RadioButtonBasic
            label="Medium"
            size="medium"
            checked={selectedSize === "medium"}
            onPress={() => setSelectedSize("medium")}
          />
        </View>
        <View style={{ marginBottom: 12 }}>
          <RadioButtonBasic
            label="Large"
            size="large"
            checked={selectedSize === "large"}
            onPress={() => setSelectedSize("large")}
          />
        </View>
      </View>
    );
  }
};

// All states demonstration
export const AllStates: Story = {
  render: () => (
    <View>
      <Text style={{ marginBottom: 16, fontWeight: "bold" }}>All possible states:</Text>
      <View style={{ marginBottom: 12 }}>
        <RadioButtonBasic label="Unchecked" checked={false} />
      </View>
      <View style={{ marginBottom: 12 }}>
        <RadioButtonBasic label="Checked" checked={true} />
      </View>
      <View style={{ marginBottom: 12 }}>
        <RadioButtonBasic label="Disabled Unchecked" checked={false} disabled={true} />
      </View>
      <View style={{ marginBottom: 12 }}>
        <RadioButtonBasic label="Disabled Checked" checked={true} disabled={true} />
      </View>
    </View>
  )
};
