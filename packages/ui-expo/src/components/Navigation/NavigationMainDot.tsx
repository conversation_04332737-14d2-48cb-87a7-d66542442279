import React from "react";
import { StyleSheet, View } from "react-native";
import { useTheme } from "../../theme";

interface Props {
  children: React.ReactNode;
}
export const NavigationMainDot = ({ children }: Props) => {
  const { theme } = useTheme();
  const { colors, borderRadius } = theme;

  const styles = StyleSheet.create({
    container: {
      width: 64,
      height: 64,
      alignItems: "center",
      justifyContent: "center",
      borderRadius: borderRadius.full,
      backgroundColor: colors.accent[300],
    },
  });

  return <View style={styles.container}>{children}</View>;
};
