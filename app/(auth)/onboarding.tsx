import { useTranslation } from "react-i18next";
import { BottleAssets } from "@/enums/bottleAssets";
import { OnboardingScreen } from "@/components/Onboarding";
import { useUserStore } from "@/stores/useUserStore";
import { useOnboardingStore } from "@/stores/useOnboardingStore";
import { useRouter } from "expo-router";
import { PathEnum } from "@/enums/path";

export default function Onboarding() {
  const { t } = useTranslation();
  const { user } = useUserStore();
  const { setOnboardingComplete } = useOnboardingStore();
  const router = useRouter();

  const screens = [
    {
      title: t("onboarding_screen1_title"),
      description: t("onboarding_screen1_description"),
      image: BottleAssets.bottleWelcome,
    },
    {
      title: t("onboarding_screen2_title"),
      description: t("onboarding_screen2_description"),
      image: BottleAssets.bottleWorkoutSquare,
      imageSize: { width: 250, height: 220 },
    },
    {
      title: t("onboarding_screen3_title"),
      description: t("onboarding_screen3_description"),
      image: BottleAssets.bottlePhone,
    },
  ];

  const handleComplete = () => {
    if (user?.id) {
      setOnboardingComplete(user.id);
    }
    router.push(PathEnum.DASHBOARD);
  };

  return (
    <OnboardingScreen
      items={screens}
      onComplete={handleComplete}
      onSkip={handleComplete}
    />
  );
}
