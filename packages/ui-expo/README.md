# UI-Expo

Biblioteka komponentów UI dla aplikacji React Native z Expo.

## Instalacja

```bash
npm install @vs_kit/ui-expo
# lub
yarn add @vs_kit/ui-expo
```

## Użycie

Aby poprawnie korzystać z komponentów, musisz owinąć swoją aplikację w `ThemeProvider` z tej biblioteki:

```jsx
import { ThemeProvider, theme } from '@vs_kit/ui-expo';

export default function App() {
  return (
    <ThemeProvider theme={theme}>
      {/* Twoja aplikacja */}
    </ThemeProvider>
  );
}
```

### Własny motyw

Możesz dostosować motyw, rozszerzając domyślny motyw:

```jsx
import { ThemeProvider, theme as defaultTheme } from '@vs_kit/ui-expo';

const customTheme = {
  ...defaultTheme,
  colors: {
    ...defaultTheme.colors,
    accent: {
      ...defaultTheme.colors.accent,
      300: '#FF0000', // Zmiana koloru akcentu
    },
  },
};

export default function App() {
  return (
    <ThemeProvider theme={customTheme}>
      {/* Twoja aplikacja */}
    </ThemeProvider>
  );
}
```

## Komponenty

Biblioteka zawiera różne komponenty UI, takie jak:

- Buttons (ButtonBasic, ButtonBasicIcon, ButtonBorder, ButtonBorderIcon)
- Links (LinkBasic, LinkBasicIcon, LinkBorder, LinkBorderIcon)
- Loaders (LoaderSpinner, LoaderSpinnerSmall)
- i inne...

Przykład użycia:

```jsx
import { ButtonBasic, LinkBorder } from '@vs_kit/ui-expo';

function MyComponent() {
  return (
    <>
      <ButtonBasic>Kliknij mnie</ButtonBasic>
      <LinkBorder href="/about">O nas</LinkBorder>
    </>
  );
}
```
