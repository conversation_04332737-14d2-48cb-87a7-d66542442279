import React from "react";
import { View, StyleSheet, Animated, Easing } from "react-native";
import { useTheme } from "../../theme";

export const LoaderSpinner = () => {
  const { theme } = useTheme();
  const spinValues = Array(12)
    .fill(0)
    .map(() => new Animated.Value(0));

  React.useEffect(() => {
    const animations = spinValues.map((value, index) => {
      return Animated.loop(
        Animated.timing(value, {
          toValue: 1,
          duration: 1200,
          easing: Easing.linear,
          useNativeDriver: true,
          delay: -100 * index,
        })
      );
    });

    Animated.parallel(animations).start();
  }, []);

  return (
    <View style={styles.container}>
      {spinValues.map((value, index) => {
        const rotate = value.interpolate({
          inputRange: [0, 1],
          outputRange: [`${index * 30}deg`, `${index * 30 + 360}deg`],
        });

        const opacity = value.interpolate({
          inputRange: [0, 0.5, 1],
          outputRange: [1, 0.3, 1],
        });

        return (
          <Animated.View
            key={index}
            style={[
              styles.line,
              {
                backgroundColor: theme.colors.accent[500],
                transform: [{ rotate }, { translateY: -20 }],
                opacity,
              },
            ]}
          />
        );
      })}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: 80,
    height: 80,
    position: "relative",
    justifyContent: "center",
    alignItems: "center",
  },
  line: {
    position: "absolute",
    width: 4,
    height: 16,
    borderRadius: 2,
  },
});
