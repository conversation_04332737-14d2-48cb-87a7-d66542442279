import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { ButtonBasic, Sizes } from "@vs/kit-ui-next";

const meta: Meta<typeof ButtonBasic> = {
  title: "Components/Buttons/ButtonBasic",
  component: ButtonBasic,
  tags: ["autodocs"],
  argTypes: {
    children: {
      control: "text",
      description: "Button content",
    },
    disabled: {
      control: "boolean",
      description: "Button disabled state",
    },
    isLoading: {
      control: "boolean",
      description: "Button loading state",
    },
    size: {
      control: "select",
      description: "Button size",
      options: Object.values(Sizes),
    },
    onClick: { action: "clicked" },
  },
  decorators: [
    Story => (
      <div className="bg-main-700 p-8">
        <Story />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof ButtonBasic>;

export const Default: Story = {
  args: {
    children: "Click me",
  },
};

export const Disabled: Story = {
  args: {
    children: "Disabled But<PERSON>",
    disabled: true,
  },
};

export const WithoutChildren: Story = {
  args: {},
};

export const LongText: Story = {
  args: {
    children: "This is a button with very long text",
  },
};

export const Loading: Story = {
  args: {
    isLoading: true,
  },
};

export const Small: Story = {
  args: {
    size: Sizes.Small,
  },
};

export const Medium: Story = {
  args: {
    size: Sizes.Medium,
  },
};

export const Large: Story = {
  args: {
    size: Sizes.Large,
  },
};
