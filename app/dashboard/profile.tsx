import React from "react";
import { View, StyleSheet } from "react-native";
import { ButtonBasic, Theme, Typography, useTheme } from "@vs/kit-ui-expo";
import { useTranslation } from "react-i18next";
import { useLogout } from "@/hooks/useLogout";

export default function Profile() {
  const { theme } = useTheme();
  const fnStyles = styles(theme);
  const { t } = useTranslation();
  const { handleLogout } = useLogout();

  return (
    <View style={fnStyles.container}>
      <Typography variant="Heading3" color={theme.colors.grey[50]}>
        Profile Settings
      </Typography>

      <ButtonBasic onPress={handleLogout}>Wyloguj</ButtonBasic>
    </View>
  );
}

const styles = (theme: Theme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      padding: 24,
      backgroundColor: theme.colors.grey[900],
    },
  });
