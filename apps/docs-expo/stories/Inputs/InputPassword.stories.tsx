import React, { useState } from "react";
import { View } from "react-native";
import type { Meta, StoryObj } from "@storybook/react";
import { InputPassword, ThemeProvider, Sizes } from "@vs/kit-ui-expo";

// Wrapper component to handle state for the InputPassword
const InputPasswordWrapper = (props: React.ComponentProps<typeof InputPassword>) => {
  const [value, setValue] = useState(props.value || "");
  return <InputPassword {...props} value={value} onChangeText={setValue} />;
};

// Define a dummy onChangeText function for the stories
const dummyOnChangeText = () => {};

const meta = {
  title: "Components/Inputs/InputPassword",
  component: InputPasswordWrapper,
  argTypes: {
    onChangeText: { action: "text changed" },
    size: {
      control: "select",
      options: [Sizes.Small, Sizes.Medium, Sizes.Large],
    },
    disabled: {
      control: "boolean",
    },
    label: {
      control: "text",
    },
    placeholder: {
      control: "text",
    },
  },
  args: {
    placeholder: "Password...",
    size: Sizes.Medium,
    disabled: false,
    value: "",
    onChangeText: dummyOnChangeText,
  },
  decorators: [
    Story => (
      <ThemeProvider>
        <View style={{ padding: 16, width: "100%" }}>
          <Story />
        </View>
      </ThemeProvider>
    ),
  ],
} satisfies Meta<typeof InputPasswordWrapper>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {};

export const WithLabel: Story = {
  args: {
    label: "Hasło",
  },
};

export const Small: Story = {
  args: {
    size: Sizes.Small,
    label: "Small Password Input",
  },
};

export const Medium: Story = {
  args: {
    size: Sizes.Medium,
    label: "Medium Password Input",
  },
};

export const Large: Story = {
  args: {
    size: Sizes.Large,
    label: "Large Password Input",
  },
};

export const Disabled: Story = {
  args: {
    disabled: true,
    value: "password123",
    label: "Disabled Password Input",
  },
};

export const WithPlaceholder: Story = {
  args: {
    placeholder: "Enter your password...",
    label: "Custom Placeholder",
  },
};

export const CustomStyle: Story = {
  args: {
    inputStyle: { backgroundColor: "#2a2a2a", borderColor: "#ff5722", borderWidth: 2, color: "#ffffff" },
    labelStyle: { color: "#ff5722", fontWeight: "bold" },
    label: "Custom Styled Password Input",
  },
};
