{"name": "@vs/kit-ui-assets", "version": "0.2.0", "description": "The boilerplate of creating React SVG icons library for Web and React Native", "main": "./dist/cjs/index.js", "module": "./dist/esm/index.js", "types": "./dist/esm/index.d.ts", "react-native": "./dist/native/index.js", "author": "miku<PERSON><PERSON><PERSON>", "license": "MIT", "keywords": ["react", "react-native", "icon", "svg"], "files": ["dist"], "bugs": {"url": "https://github.com/mikunpham/react-icon-example/issues"}, "repository": {"type": "git", "url": "git+https://github.com/mikunpham/react-icon-example.git"}, "sideEffects": false, "scripts": {"optimize": "rimraf ./optimized && svgo -q -p 8 -f ./raw -o ./optimized -r", "build": "yarn optimize && node scripts/build.js"}, "peerDependencies": {"react": "^16.13.1 || ^17.0.1 || ^18.0.0", "react-dom": "^16.13.1 || ^17.0.1 || ^18.0.0", "react-native-svg": "^12.0.0 || ^13.0.0"}, "peerDependenciesMeta": {"react-dom": {"optional": true}, "react-native-svg": {"optional": true}}, "devDependencies": {"expo-linear-gradient": "^14.0.2", "@babel/core": "^7.15.5", "@babel/preset-react": "^7.14.5", "@svgr/cli": "^5.5.0", "@svgr/core": "^5.5.0", "camelcase": "^6.2.0", "rimraf": "^3.0.2", "svgo": "^2.5.0", "terser": "^5.7.2"}}