import { useTheme } from "@vs/kit-ui-expo";
import { useMemo } from "react";
import {
  NavigationTopBarDashboard,
  NavigationTopBarDashboardWithBack,
} from "@/components/NavigationTopBar";
import { usePathname } from "expo-router";

export const useNavbarScreenOptions = () => {
  const { theme } = useTheme();
  const { colors, borderRadius } = theme;
  const pathname = usePathname();

  const screenOptions = useMemo(() => {
    const isTrainingSubroute = pathname.startsWith("/dashboard/activity/");

    return {
      headerShown: true,
      header: () =>
        isTrainingSubroute ? (
          <NavigationTopBarDashboardWithBack />
        ) : (
          <NavigationTopBarDashboard />
        ),
      tabBarShowLabel: false,
      tabBarActiveTintColor: colors.accent[500],
      tabBarInactiveTintColor: colors.accent[500],
      tabBarStyle: {
        position: "absolute" as const,
        bottom: theme.spacing[4],
        left: theme.spacing[4],
        right: theme.spacing[4],
        borderRadius: borderRadius["2xl"],
        borderColor: "transparent",
        backgroundColor: colors.grey[700],
        height: 60,
        marginHorizontal: theme.spacing[4],
      },
      // ---- Required for Android and IOS to properly center icons ----
      tabBarItemStyle: {
        height: 60,
      },
      tabBarIconStyle: {
        flex: 1,
      },
      cardStyle: {
        backgroundColor: colors.grey[900],
      },
      // ---- Required for Android and IOS to properly center icons ----
    };
  }, [theme, colors, borderRadius, pathname]);

  return {
    screenOptions,
  };
};
