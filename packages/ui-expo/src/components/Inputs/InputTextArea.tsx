import React, { useState } from "react";
import { View, TextInput, Text, StyleSheet, StyleProp, TextStyle } from "react-native";
import { Sizes, buttonSizeStyles } from "../../utils/sizes";
import { useTheme } from "../../theme";

interface Props {
  value: string;
  onChangeText: (text: string) => void;
  placeholder?: string;
  label?: string;
  size?: Sizes;
  disabled?: boolean;
  inputStyle?: StyleProp<TextStyle>;
  labelStyle?: StyleProp<TextStyle>;
  onBlur?: () => void;
  error?: string;
  numberOfLines?: number;
  maxLength?: number;
}

export const InputTextArea = ({
  value,
  onChangeText,
  placeholder = "Sample text...",
  label,
  size = Sizes.Medium,
  disabled,
  inputStyle,
  labelStyle,
  onBlur,
  error,
  numberOfLines = 4,
  maxLength,
}: Props) => {
  const { theme } = useTheme();
  const sizeStyle = buttonSizeStyles[size];
  const [isFocused, setIsFocused] = useState(false);

  return (
    <View style={styles.container}>
      {label && (
        <Text
          style={[
            styles.label,
            {
              color: error ? theme.colors.alert[500] : theme.colors.grey[50],
              fontWeight: theme.typography.BodyS.fontWeight as TextStyle["fontWeight"],
              fontSize: theme.typography.BodyS.fontSize,
              lineHeight: theme.typography.BodyS.lineHeight,
            },
            labelStyle,
          ]}
        >
          {label}
        </Text>
      )}
      <TextInput
        style={[
          styles.input,
          styles.textArea,
          sizeStyle,
          {
            borderColor: error
              ? theme.colors.alert[500]
              : isFocused
                ? theme.colors.accent[400]
                : theme.colors.grey[500],
            color: theme.colors.grey[50],
            borderRadius: theme.borderRadius.lg,
            fontWeight: theme.typography.BodyS.fontWeight as TextStyle["fontWeight"],
            fontSize: theme.typography.BodyS.fontSize,
            lineHeight: theme.typography.BodyS.lineHeight,
            height: Math.max(40, numberOfLines * 20 + 24), // Dynamic height based on numberOfLines
          },
          disabled && { opacity: 0.7 },
          inputStyle,
        ]}
        value={value}
        onChangeText={onChangeText}
        placeholder={placeholder}
        placeholderTextColor={theme.colors.grey[50]}
        onFocus={() => setIsFocused(true)}
        onBlur={() => {
          setIsFocused(false);
          onBlur?.();
        }}
        editable={!disabled}
        underlineColorAndroid="transparent"
        multiline={true}
        numberOfLines={numberOfLines}
        textAlignVertical="top"
        maxLength={maxLength}
      />
      {error && <Text style={[styles.errorLabel, { color: theme.colors.alert[500] }]}>{error}</Text>}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: "100%",
  },
  input: {
    paddingHorizontal: 24,
    fontSize: 16,
    borderWidth: 1,
  },
  textArea: {
    paddingTop: 12,
    paddingBottom: 12,
    minHeight: 80,
  },
  label: {
    marginBottom: 8,
  },
  errorLabel: {
    marginTop: 4,
    fontSize: 12,
    lineHeight: 16,
  },
});
