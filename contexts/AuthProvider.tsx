import React, { createContext, useContext, useEffect } from "react";
import { useAuthStore } from "../stores/useAuthStore";
import { checkUserAuth } from "../utils/checkUserAuth";
import { router } from "expo-router";
import { PathEnum } from "@/enums/path";

type AuthContextType = {
  isAuthenticated: boolean;
  logout: () => Promise<void>;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const { credentials, clearCredentials, isAuthenticated } = useAuthStore();

  useEffect(() => {
    if (!checkUserAuth(credentials)) {
      router.replace("/");
    } else {
      router.replace(PathEnum.DASHBOARD); // Redirect to dashboard after successful login
    }
  }, [credentials]);

  const logout = async () => {
    try {
      clearCredentials();
      router.replace("/");
    } catch (error) {
      console.error("Logout error:", error);
    }
  };

  return (
    <AuthContext.Provider value={{ isAuthenticated, logout }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
