import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConsentDefinitionEntity } from '@app/consents/entities/consent-definition.entity';
import { UserConsentEntity } from '@app/consents/entities/user-consent.entity';
import { ConsentsController } from '@app/consents/controllers/consents.controller';
import { ConsentsService } from '@app/consents/services/consents.service';

@Module({
  imports: [TypeOrmModule.forFeature([ConsentDefinitionEntity, UserConsentEntity])],
  controllers: [ConsentsController],
  providers: [ConsentsService],
  exports: [ConsentsService, TypeOrmModule],
})
export class ConsentsModule {}
