import React from "react";
import { View, TouchableOpacity, StyleSheet } from "react-native";
import DragList, { DragListRenderItemInfo } from "react-native-draglist";
import { DragAndDropListCard } from "../Cards";
import { Theme, Typography, useTheme } from "@vs/kit-ui-expo";

export type DragDropItemProps = {
  id: string;
  title: string;
  tags: string[];
};

interface Props {
  elements: DragDropItemProps[];
  style?: any;
  ListFooterComponent?: React.ReactElement | JSX.Element;
  ListHeaderComponent?: React.ReactElement | JSX.Element;
  onRemoveItemPress: (id: string) => void;
  onReorder: (newOrder: DragDropItemProps[]) => void;
}

export const DragAndDropList: React.FC<Props> = ({
  elements,
  style,
  ListHeaderComponent,
  ListFooterComponent,
  onRemoveItemPress,
  onReorder,
}) => {
  const { theme } = useTheme();
  const fnStyles = styles(theme);

  const keyExtractor = (item: DragDropItemProps, index: number) => {
    return `item-${item.id}-${index}`;
  };

  const renderItem = (info: DragListRenderItemInfo<DragDropItemProps>) => {
    const { item, onDragStart, onDragEnd, isActive } = info;
    const activeIndex = elements.findIndex((i) => i === item) + 1;

    return (
      <TouchableOpacity
        onPressIn={onDragStart}
        onPressOut={onDragEnd}
        activeOpacity={0.8}
        style={[
          fnStyles.itemContainer,
          {
            borderRadius: 18,
            borderColor: isActive ? theme.colors.accent[300] : "",
            borderWidth: isActive ? 2 : 0,
            opacity: isActive ? 0.9 : 1,
            elevation: isActive ? 8 : 2,
            transform: isActive ? [{ scale: 1.02 }] : [{ scale: 1 }],
          },
        ]}
      >
        <Typography variant="Heading5" style={fnStyles.itemText}>
          {activeIndex}.
        </Typography>
        <DragAndDropListCard
          tags={item.tags}
          title={item.title}
          onPress={() => onRemoveItemPress(item.id)}
        />
      </TouchableOpacity>
    );
  };

  const handleReordered = async (fromIndex: number, toIndex: number) => {
    const newItems = [...elements];
    const draggedElement = newItems[fromIndex];

    newItems.splice(fromIndex, 1);
    newItems.splice(toIndex, 0, draggedElement);

    onReorder(newItems);
  };

  return (
    <View style={[fnStyles.container, style]}>
      <View style={fnStyles.listContainer}>
        <DragList
          data={elements}
          keyExtractor={keyExtractor}
          onReordered={handleReordered}
          renderItem={renderItem}
          ListFooterComponent={ListFooterComponent}
          ListHeaderComponent={ListHeaderComponent}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={fnStyles.listContent}
        />
      </View>
    </View>
  );
};

const styles = (theme: Theme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      width: "100%",
      alignSelf: "center",
    },
    title: {
      fontSize: 18,
      fontWeight: "600",
      marginBottom: 16,
      textAlign: "center",
    },
    listContainer: {
      flex: 1,
    },
    listContent: {
      paddingHorizontal: 8,
      paddingVertical: 8,
      gap: 8,
    },
    itemContainer: {
      position: "relative",
    },
    itemContent: {
      flexDirection: "row",
      alignItems: "center",
      gap: 12,
    },
    dragHandle: {
      fontSize: 16,
      fontWeight: "bold",
      paddingHorizontal: 4,
    },
    itemText: {
      color: theme.colors.grey[50],
      fontWeight: "900",
      position: "absolute",
      zIndex: 10,
      top: theme.spacing[1],
      left: theme.spacing[3],
    },
    customItemWrapper: {
      flex: 1,
    },
  });
