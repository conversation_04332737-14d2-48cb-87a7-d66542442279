import React from "react";
import { View, StyleSheet } from "react-native";
import { Theme, useTheme } from "@vs/kit-ui-expo";
import { DietCard, WorkoutCard } from "@/components/Cards";

export default function UserDashboard() {
  const { theme } = useTheme();
  const fnStyles = styles(theme);

  return (
    <View style={fnStyles.container}>
      <DietCard />
      <WorkoutCard />
    </View>
  );
}

const styles = (theme: Theme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      gap: theme.spacing[4],
      padding: 24,
      backgroundColor: theme.colors.grey[900],
    },
  });
