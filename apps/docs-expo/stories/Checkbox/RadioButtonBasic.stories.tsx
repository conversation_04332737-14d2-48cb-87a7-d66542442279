import React, { useState } from "react";
import { View, Text } from "react-native";
import type { Meta, StoryObj } from "@storybook/react";
import { CheckboxBasic, ThemeProvider } from "@vs/kit-ui-expo";

// Define the props type for RadioButtonBasic
interface CheckboxProps {
  label?: string;
  checked?: boolean;
  disabled?: boolean;
  size?: "small" | "medium" | "large";
  onPress?: () => void;
}

const meta = {
  title: "Components/Checkbox/CheckboxBasic",
  component: CheckboxBasic,
  argTypes: {
    onPress: { action: "pressed the radio button" },
    size: {
      control: "select",
      options: ["small", "medium", "large"],
    },
    checked: {
      control: "boolean",
    },
    disabled: {
      control: "boolean",
    },
    label: {
      control: "text",
    },
  },
  args: {
    label: "Radio Button",
    size: "medium",
    checked: false,
    disabled: false,
  },
  decorators: [
    Story => (
      <ThemeProvider>
        <View style={{ padding: 16, alignItems: "flex-start" }}>
          <Story />
        </View>
      </ThemeProvider>
    ),
  ],
} satisfies Meta<typeof CheckboxBasic>;

export default meta;

type Story = StoryObj<typeof meta>;

// Interactive radio button component
const InteractiveCheckboxButton = (props: CheckboxProps) => {
  const [isChecked, setIsChecked] = useState(props.checked || false);

  return (
    <CheckboxBasic
      {...props}
      checked={isChecked}
      onLabelPress={() => {
        console.log("Pressed the label");
      }}
      onPress={() => {
        if (!props.disabled) {
          setIsChecked(!isChecked);
        }
      }}
    />
  );
};

export const Default: Story = {
  render: args => <InteractiveCheckboxButton {...args} />,
};

export const Small: Story = {
  render: args => <InteractiveCheckboxButton {...args} size="small" />,
};

export const Medium: Story = {
  render: args => <InteractiveCheckboxButton {...args} size="medium" />,
};

export const Large: Story = {
  render: args => <InteractiveCheckboxButton {...args} size="large" />,
};

export const Checked: Story = {
  render: args => <InteractiveCheckboxButton {...args} checked={true} />,
};

export const Disabled: Story = {
  render: args => <InteractiveCheckboxButton {...args} disabled={true} />,
};

export const CheckedDisabled: Story = {
  render: args => <InteractiveCheckboxButton {...args} checked={true} disabled={true} />,
};

export const WithoutLabel: Story = {
  render: args => <InteractiveCheckboxButton {...args} label={undefined} />,
};

// All sizes demonstration
export const AllSizes: Story = {
  render: () => {
    const [selectedSize, setSelectedSize] = useState("medium");

    return (
      <View>
        <Text style={{ marginBottom: 16, fontWeight: "bold" }}>Select a size:</Text>
        <View style={{ marginBottom: 12 }}>
          <CheckboxBasic
            label="Small"
            size="small"
            checked={selectedSize === "small"}
            onPress={() => setSelectedSize("small")}
          />
        </View>
        <View style={{ marginBottom: 12 }}>
          <CheckboxBasic
            label="Medium"
            size="medium"
            checked={selectedSize === "medium"}
            onPress={() => setSelectedSize("medium")}
          />
        </View>
        <View style={{ marginBottom: 12 }}>
          <CheckboxBasic
            label="Large"
            size="large"
            checked={selectedSize === "large"}
            onPress={() => setSelectedSize("large")}
          />
        </View>
      </View>
    );
  },
};

// All states demonstration
export const AllStates: Story = {
  render: () => (
    <View>
      <Text style={{ marginBottom: 16, fontWeight: "bold" }}>All possible states:</Text>
      <View style={{ marginBottom: 12 }}>
        <CheckboxBasic label="Unchecked" checked={false} />
      </View>
      <View style={{ marginBottom: 12 }}>
        <CheckboxBasic label="Checked" checked={true} />
      </View>
      <View style={{ marginBottom: 12 }}>
        <CheckboxBasic label="Disabled Unchecked" checked={false} disabled={true} />
      </View>
      <View style={{ marginBottom: 12 }}>
        <CheckboxBasic label="Disabled Checked" checked={true} disabled={true} />
      </View>
      <View style={{ marginBottom: 12, borderColor: "red", borderWidth: 1, width: "50%" }}>
        <CheckboxBasic
          label="Lorem ipsum dolor sit amet, consectetur adipiscing elit. In ornare velit ornare ipsum accumsan bibendum. "
          checked={true}
          disabled={true}
        />
      </View>
    </View>
  ),
};
