import React from "react";
import { View, Text } from "react-native";
import type { Meta, StoryObj } from "@storybook/react";
import { LoaderSpinnerPage, ThemeProvider } from "@vs/kit-ui-expo";

const meta = {
  title: "Components/Loaders/LoaderSpinnerPage",
  component: LoaderSpinnerPage,
  decorators: [
    Story => (
      <ThemeProvider>
        <View style={{ width: 300, height: 200, backgroundColor: "#f5f5f5", position: "relative" }}>
          <Text style={{ padding: 16 }}>Content behind loader</Text>
          <Story />
        </View>
      </ThemeProvider>
    ),
  ],
} satisfies Meta<typeof LoaderSpinnerPage>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {};
