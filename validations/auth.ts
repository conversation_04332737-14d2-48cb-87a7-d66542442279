import { z } from "zod";

export const registerSchema = z
  .object({
    name: z
      .string({ message: "validation_messages_required" })
      .min(2, "validation_messages_name_min_length"),
    email: z
      .string({ message: "validation_messages_required" })
      .email("validation_messages_email_invalid"),
    password: z
      .string({ message: "validation_messages_required" })
      .min(8, "validation_messages_password_min_length"),
    confirmPassword: z.string({ message: "validation_messages_required" }),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "validation_messages_passwords_must_match",
    path: ["confirmPassword"],
  });

export const loginSchema = z.object({
  email: z
    .string({ message: "validation_messages_required" })
    .email("validation_messages_email_invalid"),
  password: z
    .string({ message: "validation_messages_required" })
    .min(8, "validation_messages_password_min_length"),
});

export type RegisterFormData = z.infer<typeof registerSchema>;
export type LoginFormData = z.infer<typeof loginSchema>;
