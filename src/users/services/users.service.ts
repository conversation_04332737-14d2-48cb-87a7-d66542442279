import { BadRequestException, HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { UserEntity } from '../entities/user.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import { authMessages } from '@app/constants/messages/auth-messages';
import { CreateUserDto } from '@app/users/dto/create-user.dto';
import { CONSENTS_CONFIG } from '@app/constants/models/consent-config';
import { CreateSocialUserDto } from '@app/users/dto/create-social-user.dto';
import { UserConsentEntity } from '@app/consents/entities/user-consent.entity';
import { ConsentsService } from '@app/consents/services/consents.service';
import { CommandBus } from '@nestjs/cqrs';
import { SendOtcCommand } from '@app/users/commands/send-otc.command';
import { CreateOtcDto } from '@app/users/dto/create-otc.dto';
import { UserOtcEntity } from '@app/users/entities/user-otc.entity';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(UserEntity)
    private readonly usersRepository: Repository<UserEntity>,
    @InjectRepository(UserConsentEntity)
    private readonly userConsentRepo: Repository<UserConsentEntity>,
    @InjectRepository(UserOtcEntity)
    private readonly userOtcRepository: Repository<UserOtcEntity>,
    private readonly consentsService: ConsentsService,
    private readonly dataSource: DataSource,
    private readonly commandBus: CommandBus,
  ) {}

  /**
   * Get user by ID
   * @param id User ID
   * @returns User entity or null if not found
   * @throws HttpException if user not found
   */
  async getUserById(id: string): Promise<UserEntity | null> {
    try {
      return await this.usersRepository.findOne({ where: { id } });
    } catch {
      throw new HttpException(authMessages.user.notFound, HttpStatus.NOT_FOUND);
    }
  }

  /**
   * Get all users
   * @returns Array of user entities
   */
  async findAll(): Promise<UserEntity[]> {
    return this.usersRepository.find({});
  }

  /**
   * Checks if all required consents are provided
   * @param consents List of user consents
   * @throws HttpException if required consents are missing
   */
  private validateRequiredConsents(consents: string[]): void {
    const requiredConsents = CONSENTS_CONFIG.filter((consent) => consent.required).map((consent) => consent.type);
    for (const requiredConsent of requiredConsents) {
      if (!consents.includes(requiredConsent)) {
        throw new HttpException(`Missing required consent: ${requiredConsent}`, HttpStatus.BAD_REQUEST);
      }
    }
  }

  /**
   * Create a new user
   * @param createUserDto User creation data
   * @returns Newly created user entity
   * @throws HttpException if user already exists or if required consents are missing
   */
  async create(createUserDto: CreateUserDto): Promise<UserEntity> {
    const { email, name, password, consentIds, authorization_code } = createUserDto;

    const validOtc = await this.checkOtcCode(email, authorization_code);
    if (!validOtc) {
      throw new HttpException('Invalid OTC', HttpStatus.UNAUTHORIZED);
    }

    const existingUser = await this.getUserByEmail(email);
    if (existingUser) {
      throw new HttpException(authMessages.user.exists, HttpStatus.CONFLICT);
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const user = this.usersRepository.create({ email, name, password });
      const savedUser = await queryRunner.manager.save(user);

      const latestConsents = await this.consentsService.getLatestConsents();
      const requiredConsentIds = latestConsents.filter((c) => c.isRequired).map((c) => c.id);

      const hasAllRequired = requiredConsentIds.every((requiredId) => consentIds.includes(requiredId));
      if (!hasAllRequired) {
        throw new BadRequestException(authMessages.consents.requiredUserAcceptance);
      }

      const userConsentEntities = consentIds.map((consentId) =>
        this.userConsentRepo.create({
          user: savedUser,
          consentDefinition: { id: consentId },
        }),
      );

      await queryRunner.manager.save(userConsentEntities);

      await queryRunner.commitTransaction();
      return savedUser;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async createSocialUser(dto: CreateSocialUserDto): Promise<UserEntity> {
    const { email, name } = dto;
    const password = '';
    const user = this.usersRepository.create({ email, name, password, refreshTokens: [], consents: [] });
    return this.usersRepository.save(user);
  }

  async getUserByEmail(email: string): Promise<UserEntity | null> {
    return this.usersRepository.findOne({
      where: {
        email,
      },
    });
  }

  async updatePassword(userId: string, newPassword: string): Promise<void> {
    await this.usersRepository.update(userId, { password: newPassword });
  }

  async updateUser(userId: string, data: Partial<UserEntity>) {
    const user = await this.getUserById(userId);
    if (!user) {
      throw new Error(authMessages.user.notFound);
    }
    Object.assign(user, data);
    return this.usersRepository.save(user);
  }

  async getOrCreateUser(data: CreateSocialUserDto) {
    const user = await this.getUserByEmail(data.email);
    if (user) {
      return user;
    }
    return await this.createSocialUser(data);
  }

  async createOtc(createOtcDto: CreateOtcDto): Promise<void> {
    await this.commandBus.execute(new SendOtcCommand(createOtcDto.email));
  }

  async saveOrUpdateOtcCode(email: string, code: string) {
    const entity = this.userOtcRepository.create({
      email,
      code,
    });

    return await this.userOtcRepository.save(entity);
  }

  async checkOtcCode(email: string, code: string): Promise<boolean> {
    const otc = await this.userOtcRepository.findOneBy({ email });
    if (!otc || !code) {
      return false;
    }
    const now = new Date();
    return otc.code === code && otc.expiresAt > now;
  }
}
