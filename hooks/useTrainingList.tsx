import { getAllTrainings } from "@/api/training";
import { useTrainingStore } from "@/stores/useTrainingStore";
import { useQuery } from "@tanstack/react-query";
import { useEffect } from "react";
import { useErrorHandling } from "./useErrorHandling";

export const useTrainingListQuery = () => {
  const { setTraining } = useTrainingStore();
  const { withErrorHandling } = useErrorHandling();

  const {
    data: trainings,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ["trainings"],
    queryFn: async () => {
      const result = await withErrorHandling(async () => {
        return await getAllTrainings();
      });
      return result || [];
    },
    retry: false, // Wyłączamy retry bo błędy obsługuje withErrorHandling
  });

  useEffect(() => {
    if (trainings) {
      setTraining(trainings);
    }
  }, [trainings, setTraining]);

  const handleRefetch = () =>
    withErrorHandling(async () => {
      await refetch();
    });

  return {
    trainingList: trainings || [],
    loading: isLoading,
    refetch: handleRefetch,
  };
};
