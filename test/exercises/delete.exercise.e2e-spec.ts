import 'reflect-metadata';
import { INestApplication } from '@nestjs/common';
import request from 'supertest';

import { loginAndAssertUser, registerAndAssertUser } from '../__helpers/auth';
import { CreateExerciseRequestDto } from '@app/exercises/dto/create-exercise.dto';
import { DifficultyLevel } from '@app/exercises/enums/difficulty-level.enum';
import { buildTestApp } from '../__setup/app.factory';
import { createAndAssertExercise } from '../__helpers/exercise';
import { randomUUID } from 'crypto';
import { generateTestUser } from '../__helpers/user';
import { ConsentDefinitionEntity } from '@app/consents/entities/consent-definition.entity';
import { createAndAssertConsent, generateGDPRConsentPayload } from '../__helpers/consents';

describe('DELETE /v1/exercise/:id', () => {
  let app: INestApplication;
  let accessToken: string;
  let consent: ConsentDefinitionEntity;

  beforeAll(async () => {
    app = await buildTestApp();

    const consentPayload = generateGDPRConsentPayload();
    consent = await createAndAssertConsent(app, consentPayload);

    const testUser = generateTestUser([consent.id]);
    await registerAndAssertUser(app, testUser);
    accessToken = (await loginAndAssertUser(app, testUser)).accessToken;
  });

  afterAll(async () => {
    await app.close();
  });

  it('should soft delete the exercise (status = deleted)', async () => {
    const payload: CreateExerciseRequestDto = {
      name: 'Test Exercise E2E',
      description: 'This is a test exercise',
      tags: 'e2e|test',
      difficultyLevel: DifficultyLevel.MEDIUM,
      instructions: 'Step 1: … Step 2: …',
    };

    const exercise = await createAndAssertExercise(app, payload, accessToken);

    const response = await request(app.getHttpServer())
      .delete(`/v1/exercise/${exercise.id}`)
      .set('Authorization', `Bearer ${accessToken}`);

    expect(response.status).toBe(200);
    expect(response.body.id).toBe(exercise.id);
    expect(response.body.status).toBe('deleted');
  });

  it('should return 403 if user is not the creator', async () => {
    const anotherUser = generateTestUser([consent.id]);

    await registerAndAssertUser(app, anotherUser);
    const anotherUserAccessToken = (await loginAndAssertUser(app, anotherUser)).accessToken;

    const exercisePayload: CreateExerciseRequestDto = {
      name: 'Test Exercise E2E',
      description: 'This is a test exercise',
      tags: 'e2e|test',
      difficultyLevel: DifficultyLevel.MEDIUM,
      instructions: 'Step 1: … Step 2: …',
    };

    const exercise = await createAndAssertExercise(app, exercisePayload, accessToken);

    const response = await request(app.getHttpServer())
      .delete(`/v1/exercise/${exercise.id}`)
      .set('Authorization', `Bearer ${anotherUserAccessToken}`);

    expect(response.status).toBe(403);
    expect(response.body.message).toBe('You do not have permission to access this exercise');
    expect(response.body.error).toBe('Forbidden');
    expect(response.body.statusCode).toBe(403);
  });

  it('should return 404 if exercise does not exist', async () => {
    const uuid = randomUUID();
    const response = await request(app.getHttpServer())
      .delete(`/v1/exercise/${uuid}`)
      .set('Authorization', `Bearer ${accessToken}`);

    expect(response.status).toBe(404);
    expect(response.body.message).toBe(`Exercise with ID ${uuid} not found`);
    expect(response.body.error).toBe('Not Found');
    expect(response.body.statusCode).toBe(404);
  });
});
