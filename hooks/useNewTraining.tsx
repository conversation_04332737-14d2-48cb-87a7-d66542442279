import { useForm } from "react-hook-form";
import { useErrorHandling } from "./useErrorHandling";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "expo-router";
import { ToastSuccess, ToastWarning, useToast } from "@vs/kit-ui-expo";
import { useTranslation } from "react-i18next";
import { useTrainingListQuery } from "./useTrainingList";
import { newTrainingSchema, NewTrainingType } from "@/validations";
import {
  deleteTraining,
  postNewTraining,
  putNewTraining,
} from "@/api/training";

interface Props {
  id?: string;
  isNew?: boolean;
}

export const useNewTraining = ({ id, isNew }: Props) => {
  const { withErrorHandling } = useErrorHandling();
  const { showToast } = useToast();
  const { t } = useTranslation();
  const { refetch } = useTrainingListQuery();
  const router = useRouter();
  const {
    control,
    setValue,
    handleSubmit,
    watch,
    formState: { errors },
    reset,
  } = useForm<NewTrainingType>({
    resolver: zodResolver(newTrainingSchema),
    defaultValues: {
      trainingExercises: [],
    },
  });

  const trainingExercises = watch("trainingExercises");

  const onSubmit = (data: NewTrainingType) =>
    withErrorHandling(async () => {
      const payload = {
        name: data.name,
        description: data.description,
        tags: data.tags,
        exercises: data.trainingExercises,
      };

      console.log("paylaod", payload);

      if (id && !isNew) {
        await putNewTraining(payload, id);
      } else {
        await postNewTraining(payload);
      }

      showToast(
        <ToastSuccess
          title={t("creators_form_toast_training_success_title")}
          message={t("creators_form_toast_training_success_description")}
        />
      );
      await refetch();
      router.back();
    });

  const handleDeleteTraining = (id: string) =>
    withErrorHandling(async () => {
      await deleteTraining(id);
      showToast(
        <ToastWarning
          title={t("creators_form_toast_training_delete_title")}
          message={t("creators_form_toast_training_delete_description")}
        />
      );
      await refetch();
      router.back();
    });

  const setTrainingExercises = (trainingExercises: string[]) => {
    setValue("trainingExercises", trainingExercises);
  };

  return {
    control,
    handleSubmit,
    errors,
    onSubmit,
    reset,
    trainingExercises,
    handleDeleteTraining,
    setTrainingExercises,
  };
};
