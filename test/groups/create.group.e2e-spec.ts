import 'reflect-metadata';
import { INestApplication } from '@nestjs/common';
import request from 'supertest';

import { loginAndAssertUser, registerAndAssertUser } from '../__helpers/auth';
import { buildTestApp } from '../__setup/app.factory';
import { generateTestUser } from '../__helpers/user';
import { ConsentDefinitionEntity } from '@app/consents/entities/consent-definition.entity';
import { createAndAssertConsent, generateGDPRConsentPayload } from '../__helpers/consents';
import { CreateGroupRequestDto } from '@app/groups/dto/create-group.dto';

describe('/POST /v1/group', () => {
  let app: INestApplication;
  let accessToken: string;
  let consent: ConsentDefinitionEntity;

  beforeAll(async () => {
    app = await buildTestApp();

    const consentPayload = generateGDPRConsentPayload();
    consent = await createAndAssertConsent(app, consentPayload);

    const testUser = generateTestUser([consent.id]);
    await registerAndAssertUser(app, testUser);
    accessToken = (await loginAndAssertUser(app, testUser)).accessToken;
  });

  afterAll(async () => {
    await app.close();
  });

  // TODO: only trainer should be able to create a group
  it('should create a new group', async () => {
    const payload: CreateGroupRequestDto = {
      name: 'Test Exercise E2E',
      description: 'This is a test exercise',
      tags: 'e2e|test',
    };

    const response = await request(app.getHttpServer())
      .post('/v1/group')
      .set('Authorization', `Bearer ${accessToken}`)
      .send(payload);

    expect(response.status).toBe(201);
    expect(response.body.id).toBeDefined();
    expect(response.body.name).toBe(payload.name);
    expect(response.body.description).toBe(payload.description);
    expect(response.body.tags).toBe(payload.tags);
    expect(response.body.createdAt).toBeDefined();
    expect(response.body.updatedAt).toBeDefined();
    expect(response.body.ownerId).toBeDefined();
  });

  it('should return 401 when user is not authenticated', async () => {
    const response = await request(app.getHttpServer()).post('/v1/group');

    expect(response.status).toBe(401);
    expect(response.body.message).toBe('Unauthorized');
  });

  it('should return 400 when name is missing', async () => {
    const payload: CreateGroupRequestDto = {
      name: 'Test Exercise E2E',
      description: 'This is a test exercise',
      tags: 'e2e|test',
    };

    delete (payload as any).name;

    const response = await request(app.getHttpServer())
      .post('/v1/group')
      .set('Authorization', `Bearer ${accessToken}`)
      .send(payload);

    expect(response.status).toBe(400);
    expect(response.body.message).toEqual(
      expect.arrayContaining(['name must be a string', 'name should not be empty']),
    );
  });
});
