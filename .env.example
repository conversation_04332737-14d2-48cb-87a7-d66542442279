NODE_ENV=
HTTP_PORT=s

# Database Configuration
POSTGRES_HOST=
POSTGRES_PORT=
POSTGRES_USER=
POSTGRES_PASSWORD=
POSTGRES_DB=

# JWT Configuration
JWT_ACCESS_TOKEN_SECRET=
JWT_ACCESS_TOKEN_EXPIRATION_S=
JWT_REFRESH_TOKEN_SECRET=
JWT_REFRESH_TOKEN_EXPIRATION_S=

# Group Configuration
GROUP_INVITATION_SECRET=
GROUP_INVITATION_BASE_URL=

# Google OAuth
GOOGLE_AUTH_CLIENT_ID=
GOOGLE_AUTH_CLIENT_SECRET=
GOOGLE_AUTH_REDIRECT_URI=

#group invitation
GROUP_INVITATION_SECRET=
GROUP_INVITATION_BASE_URL=
GROUP_INVITATION_URL_EXPIRATION=

#sendgrid
SENDGRID_HOST=
SENDGRID_API_KEY=
SENDGRID_API_USERNAME=
SENDGRID_MAIL_FROM=