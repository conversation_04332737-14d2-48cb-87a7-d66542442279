import React from "react";
import { View } from "react-native";
import type { Meta, StoryObj } from "@storybook/react";
import { ProgressBar, ThemeProvider } from "@vs/kit-ui-expo";

const meta = {
  title: "Components/Progress/ProgressBar",
  component: ProgressBar,
  decorators: [
    Story => (
      <ThemeProvider>
        <View style={{ padding: 16, width: "100%" }}>
          <Story />
        </View>
      </ThemeProvider>
    ),
  ],
  argTypes: {
    label: {
      control: "text",
      description: "Label to display above the progress bar",
    },
    progress: {
      control: { type: "range", min: 0, max: 1, step: 0.01 },
      description: "Value from 0 to 1",
    },
    gradientColors: {
      control: { type: "object" },
      description: "Table of colors for the gradient",
    },
    containerStyle: {
      control: "object",
      description: "Style of the container",
    },
    fillStyle: {
      control: "object",
      description: "Style of the fill",
    },
  },
} satisfies Meta<typeof ProgressBar>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    progress: 50,
  },
};

export const Custom: Story = {
  args: {
    progress: 75,
    label: "Custom",
    gradientColors: ["#f69109", "#beab26", "#5e8633"],
    containerStyle: { height: 24, borderRadius: 12 },
  },
};

export const NoLabel: Story = {
  args: {
    progress: 60,
    label: false,
  },
};

export const LowPercentage: Story = {
  args: {
    progress: 10,
  },
};

export const FullPercentage: Story = {
  args: {
    progress: 100,
  },
};
