import { UserEntity } from '@app/users/entities/user.entity';
import { CreateGroupResponseDto } from './create-group.dto';
import { GroupEntity } from '../entities/group.entity';
import { ApiProperty } from '@nestjs/swagger';

export class GetGroupResponseDto extends CreateGroupResponseDto {}

export class GetGroupUserResponseDto {
  @ApiProperty({
    description: 'Unique group-user relationship identifier',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  group: GroupEntity;

  user: UserEntity;

  @ApiProperty({
    description: 'Relationship creation timestamp',
    example: '2023-01-01T12:00:00Z',
  })
  createdAt: Date;
}
