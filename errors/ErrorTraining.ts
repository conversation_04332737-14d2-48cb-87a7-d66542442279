import { ErrorBase } from "./ErrorBase";
import { ErrorTypes } from "./types";

export class ErrorGetTraining extends ErrorBase {
  constructor(error?: unknown) {
    super(ErrorTypes.GET_TRAINING, error);
  }
}

export class ErrorPostTraining extends ErrorBase {
  constructor(error?: unknown) {
    super(ErrorTypes.POST_TRAINING, error);
  }
}

export class ErrorPutTraining extends ErrorBase {
  constructor(error?: unknown) {
    super(ErrorTypes.PUT_TRAINING, error);
  }
}

export class ErrorDeleteTraining extends ErrorBase {
  constructor(error?: unknown) {
    super(ErrorTypes.DELETE_TRAINING, error);
  }
}
