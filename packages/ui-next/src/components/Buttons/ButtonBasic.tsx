import clsx from "clsx";
import { LoaderSpinnerSmall } from "../Loaders";
import { Sizes, buttonSizeClasses } from "../../utils/sizes";

interface Props extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children?: React.ReactNode;
  size?: Sizes;
  isLoading?: boolean;
}

export const ButtonBasic = ({ children, isLoading, size = Sizes.Medium, ...props }: Props) => {
  const dynamicClasses = clsx(buttonSizeClasses[size]);

  return (
    <button
      className={clsx(
        "px-6 rounded-lg bg-accent-300 text-grey-900",
        "disabled:bg-grey-400 disabled:text-grey-500 disabled:cursor-not-allowed",
        dynamicClasses
      )}
      {...props}
    >
      {isLoading ? <LoaderSpinnerSmall /> : (children ?? "Click")}
    </button>
  );
};
