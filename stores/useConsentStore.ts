import { Consent } from "@/types/consents";
import { create } from "zustand";
import { devtools } from "zustand/middleware";

interface State {
  consents: Consent[];
  setConsents: (consents: Consent[]) => void;
  getConsentsById: (id: string) => Promise<Consent>;
}

export const useConsentStore = create<State>()(
  devtools(
    (set, get) => ({
      consents: [],
      getConsentsById: (id: string) => {
        const consents = get().consents;
        return consents.find((consent) => consent.id === id);
      },
      setConsents: async (consents) => {
        try {
          set({ consents });
        } catch (error) {
          throw error;
        }
      },
    }),
    { name: "ConsentStore" }
  )
);
