import { router } from "expo-router";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { LoginFormData, loginSchema } from "@/validations";
import { useAuthStore } from "@/stores/useAuthStore";
import { useErrorHandling } from "./useErrorHandling";
import { loginRequest } from "@/api/authorization";
import { useUserStore } from "@/stores/useUserStore";
import { useOnboardingStore } from "@/stores/useOnboardingStore";
import { PathEnum } from "@/enums/path";

export const useLogin = () => {
  const { setCredentials } = useAuthStore();
  const { setUser } = useUserStore();
  const { hasUserCompletedOnboarding } = useOnboardingStore();
  const { withErrorHandling } = useErrorHandling();
  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
  });

  const onSubmit = (data: LoginFormData) =>
    withErrorHandling(async () => {
      const res = await loginRequest({
        email: data.email.toLowerCase().trim(),
        password: data.password,
      });
      const { user, ...auth } = res;

      setCredentials(auth);
      setUser(user);

      if (!hasUserCompletedOnboarding(user.id)) {
        router.replace("/onboarding");
      } else {
        router.replace(PathEnum.DASHBOARD);
      }
    });

  const handleScanQR = () => {
    router.push(PathEnum.QR_SCANNER);
  };

  return {
    control,
    handleSubmit,
    errors,
    onSubmit,
    handleScanQR,
  };
};
