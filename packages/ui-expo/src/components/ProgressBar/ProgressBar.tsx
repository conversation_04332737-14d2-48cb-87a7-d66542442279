import React from "react";
import { StyleSheet, View, ViewStyle, StyleProp } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { useTheme } from "../../theme";
import { Typography } from "../Typography";

interface ProgressBarProps {
  progress: number;
  label?: string | false;
  gradientColors?: readonly [string, string, ...string[]];
  containerStyle?: StyleProp<ViewStyle>;
  fillStyle?: StyleProp<ViewStyle>;
}

export const ProgressBar: React.FC<ProgressBarProps> = ({
  progress,
  label = `${Math.round(progress * 100)}%`,
  gradientColors = ["#f69109", "#beab26", "#5e8633"],
  containerStyle,
  fillStyle,
}) => {
  const { theme } = useTheme();

  return (
    <View style={[styles.progressBarContainer, { backgroundColor: theme.colors.grey[50] }, containerStyle]}>
      <LinearGradient
        colors={gradientColors}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={[styles.progressBarFill, { width: `${Math.min(progress * 100, 100)}%` }, fillStyle]}
      />
      {label !== false && (
        <Typography variant="BodyXS" color={theme.colors.grey[500]} style={styles.progressLabel}>
          {label}
        </Typography>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  progressBarContainer: {
    height: 16,
    borderRadius: 8,
    marginTop: 12,
    overflow: "hidden",
    position: "relative",
  },
  progressBarFill: {
    height: "100%",
    borderRadius: 2,
  },
  progressLabel: {
    position: "absolute",
    left: "25%",
    top: 0,
    bottom: 0,
    height: "100%",
    zIndex: 1,
    justifyContent: "center",
  },
});
