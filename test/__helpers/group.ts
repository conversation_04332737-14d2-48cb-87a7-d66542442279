import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { CreateGroupRequestDto } from '@app/groups/dto/create-group.dto';
import { GroupEntity } from '@app/groups/entities/group.entity';

export const createAndAssertGroup = async (
  app: INestApplication,
  payload: CreateGroupRequestDto,
  accessToken: string,
): Promise<GroupEntity> => {
  const response = await request(app.getHttpServer())
    .post('/v1/group')
    .set('Authorization', `Bearer ${accessToken}`)
    .send(payload);

  expect(response.status).toBe(201);
  expect(response.body.id).toBeDefined();
  expect(response.body.name).toBe(payload.name);
  expect(response.body.description).toBe(payload.description);
  expect(response.body.tags).toBe(payload.tags);
  expect(response.body.createdAt).toBeDefined();
  expect(response.body.updatedAt).toBeDefined();
  expect(response.body.ownerId).toBeDefined();

  return response.body as GroupEntity;
};

export const addAndAssertUserToGroup = async (
  app: INestApplication,
  groupId: string,
  userId: string,
  accessToken: string,
): Promise<{ message: string }> => {
  const response = await request(app.getHttpServer())
    .post(`/v1/group/${groupId}/user/${userId}`)
    .set('Authorization', `Bearer ${accessToken}`);

  expect(response.status).toBe(200);
  expect(response.body.message).toBe('User successfully added to group');

  return response.body as { message: string };
};

export const getAndAssertGroupById = async (
  app: INestApplication,
  groupId: string,
  accessToken: string,
): Promise<GroupEntity> => {
  const response = await request(app.getHttpServer())
    .get(`/v1/group/${groupId}`)
    .set('Authorization', `Bearer ${accessToken}`);

  expect(response.status).toBe(200);
  expect(response.body.id).toBe(groupId);

  return response.body as GroupEntity;
};
