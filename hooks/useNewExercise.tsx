import { useForm } from "react-hook-form";
import { useErrorHandling } from "./useErrorHandling";
import { zodResolver } from "@hookform/resolvers/zod";
import { newExerciseSchema, NewExerciseType } from "@/validations/newExercise";
import {
  deleteExercise,
  postNewExercise,
  putNewExercise,
} from "@/api/exercise";
import { useRouter } from "expo-router";
import { ToastSuccess, ToastWarning, useToast } from "@vs/kit-ui-expo";
import { useExerciseListQuery } from "./useExerciseList";
import { useTranslation } from "react-i18next";

interface Props {
  id?: string;
  isNew?: boolean;
}

export const useNewExercise = ({ id, isNew }: Props) => {
  const { withErrorHandling } = useErrorHandling();
  const { showToast } = useToast();
  const { t } = useTranslation();
  const { refetch } = useExerciseListQuery();
  const router = useRouter();
  const {
    control,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<NewExerciseType>({
    resolver: zodResolver(newExerciseSchema),
  });

  const onSubmit = (data: NewExerciseType) =>
    withErrorHandling(async () => {
      const payload = {
        name: data.name,
        description: data.description,
        tags: data.tags,
        difficultyLevel: data.difficultyLevel,
        instructions: data.instructions,
        imageUrl: "https://placebear.com/g/400/400",
        videoUrl:
          "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerFun.mp4",
      };

      if (id && !isNew) {
        await putNewExercise(payload, id);
      } else {
        await postNewExercise(payload);
      }

      showToast(
        <ToastSuccess
          title={t("creators_form_toast_exercise_success_title")}
          message={t("creators_form_toast_exercise_success_description")}
        />
      );
      await refetch();
      router.back();
    });

  const handleDeleteExercise = (id: string) =>
    withErrorHandling(async () => {
      await deleteExercise(id);
      showToast(
        <ToastWarning
          title={t("creators_form_toast_exercise_delete_title")}
          message={t("creators_form_toast_exercise_delete_description")}
        />
      );
      await refetch();
      router.back();
    });

  return {
    control,
    handleSubmit,
    errors,
    onSubmit,
    reset,
    handleDeleteExercise,
  };
};
