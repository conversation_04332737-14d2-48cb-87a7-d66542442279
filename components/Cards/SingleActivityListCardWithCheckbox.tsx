import { ActivityStatus } from "@/enums/activityStatus";
import {
  useTheme,
  BorderBoxWithGradient,
  Typography,
  ButtonBorder,
  Sizes,
  CheckboxBasic,
} from "@vs/kit-ui-expo";
import { useTranslation } from "react-i18next";
import { StyleSheet, View } from "react-native";

interface Props {
  title: string;
  description: string;
  checked: boolean;
  status?: string;
  onChecked: (checked: boolean) => void;
  onPress?: () => void;
}

export const SingleActivityListCardWithCheckbox = ({
  title,
  description,
  status,
  checked,
  onPress,
  onChecked,
}: Props) => {
  const { theme } = useTheme();
  const { t } = useTranslation();

  const renderCheckbox = () => {
    return (
      <View
        style={[
          styles.checkbox,
          {
            gap: theme.spacing[1],
          },
        ]}
      >
        <CheckboxBasic checked={checked} onPress={() => onChecked(!checked)} />
      </View>
    );
  };

  return (
    <BorderBoxWithGradient
      style={[
        styles.container,
        {
          opacity: status === ActivityStatus.DELETED ? 0.5 : 1,
          borderColor: theme.colors.accent[400],
          paddingTop: theme.spacing[10],
          padding: theme.spacing[4],
        },
      ]}
    >
      {renderCheckbox()}
      <Typography
        variant="BodyM"
        style={styles.text}
        color={theme.colors.grey[50]}
      >
        {title}
      </Typography>
      <Typography
        variant="BodyXS"
        style={styles.text}
        color={theme.colors.grey[50]}
      >
        {description}
      </Typography>
      <ButtonBorder
        size={Sizes.Small}
        disabled={status === ActivityStatus.DELETED}
        onPress={onPress}
      >
        {t("activity_single_list_card_show")}
      </ButtonBorder>
    </BorderBoxWithGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    position: "relative",
    gap: 8,
    flex: 1,
    justifyContent: "space-between",
  },
  text: {
    textAlign: "center",
  },
  checkbox: {
    flexDirection: "row",
    justifyContent: "flex-end",
    alignItems: "center",
    padding: 8,
    paddingRight: 2,
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
  },
});
