{"name": "@vs/kit-ui-expo", "version": "1.0.0", "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"build": "tsup", "dev": "tsup --watch", "clean": "rm -rf dist"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./*": {"types": "./src/*.ts", "import": "./dist/*.mjs", "require": "./dist/*.js"}}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-native": "^0.73.0", "@vs/kit-typescript-config": "*", "@vs/kit-ui-assets": "*", "react-native-safe-area-context": "^4.8.2", "tsup": "^8.0.1", "typescript": "5.5.4", "expo-linear-gradient": "^14.0.2"}, "peerDependencies": {"react": "18.2.0", "react-native": "0.74.5", "react-native-safe-area-context": "^4.8.2", "expo-linear-gradient": "^14.0.2", "@vs/kit-ui-assets": "*"}, "publishConfig": {"access": "public"}}