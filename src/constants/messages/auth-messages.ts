export const authMessages = {
  auth: {
    invalidToken: 'Token is not valid',
    invalidRefreshToken: 'Refresh token is not valid',
    headerMissing: 'Authorization header missing',
    invalidCredentials: 'Credentials are not valid',
  },
  consents: {
    requiredUserAcceptance: 'User must accept all required consents',
  },
  email: {
    isRequired: 'Email is required',
    invalid: 'Email is invalid',
    exists: 'Email already exists',
  },
  google: {
    noEmail: 'No email found in Google profile',
  },
  user: {
    notFound: 'User not found',
    exists: 'User already exists',
    createError: 'Error creating users',
    invalidIdFormat: 'Invalid users ID format',
    invalidCredentials: 'Invalid credentials',
    noMatchingRole: 'No matching roles',
    notActiveAccount: 'User account is not active',
  },
};
