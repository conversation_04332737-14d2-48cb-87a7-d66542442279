import { <PERSON><PERSON><PERSON>, PrimaryGeneratedC<PERSON>umn, Column, BeforeInsert, BeforeUpdate, OneToMany, BaseEntity } from 'typeorm';
import { EUserRole } from '@app/constants/models/user-role.enum';
import bcrypt from 'bcryptjs';
import { Exclude } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { RefreshTokenEntity } from '@app/auth/entities/refresh-token.entity';
import { UserConsentEntity } from '@app/consents/entities/user-consent.entity';

@Entity({ name: 'users' })
export class UserEntity extends BaseEntity {
  @ApiProperty({
    description: 'Unique user identifier',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({
    description: 'User email address',
    example: '<EMAIL>',
  })
  @Column({ unique: true })
  email: string;

  @ApiProperty({
    description: 'Unique username',
    example: 'johndoe',
  })
  @Column({ unique: true })
  name: string;

  @ApiProperty({
    description: 'User password (hashed)',
    example: '$2a$10$Jh/Hsk9Jh0SKJ...',
  })
  @Column()
  @Exclude()
  password: string;

  @ApiProperty({
    description: 'User activation status',
    example: false,
  })
  @Column({ default: true })
  isActive: boolean;

  @ApiProperty({
    description: 'List of user consents',
    example: ['GDPR', 'POLICY'],
    isArray: true,
  })
  @OneToMany(() => UserConsentEntity, (uc) => uc.user, { cascade: true })
  consents: UserConsentEntity[];

  @ApiProperty({
    description: 'User roles',
    enum: EUserRole,
    default: [EUserRole.USER],
    isArray: true,
  })
  @Column({ type: 'simple-array', enum: EUserRole, default: EUserRole.USER })
  roles: EUserRole[];

  @ApiProperty({
    description: 'User creation timestamp',
    example: '2023-01-01T12:00:00Z',
  })
  @Column({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
  })
  createdAt: Date;

  @OneToMany(() => RefreshTokenEntity, (token) => token.user, { cascade: true })
  refreshTokens: RefreshTokenEntity[];

  @BeforeInsert()
  @BeforeUpdate()
  async hashPassword() {
    if (!this.password) return;
    try {
      this.password = await bcrypt.hash(this.password, 10);
    } catch (error) {
      console.error('Error while hashing password:', error);
      throw new Error('Could not hash the password');
    }
  }
}
