import { ErrorBase } from "./ErrorBase";
import { ErrorTypes } from "./types";

export class ErrorGetTrainingPlan extends ErrorBase {
  constructor(error?: unknown) {
    super(ErrorTypes.GET_TRAINING_PLAN, error);
  }
}

export class ErrorPostTrainingPlan extends ErrorBase {
  constructor(error?: unknown) {
    super(ErrorTypes.POST_TRAINING_PLAN, error);
  }
}

export class ErrorPutTrainingPlan extends ErrorBase {
  constructor(error?: unknown) {
    super(ErrorTypes.PUT_TRAINING_PLAN, error);
  }
}

export class <PERSON>rrorDeleteTrainingPlan extends ErrorBase {
  constructor(error?: unknown) {
    super(ErrorTypes.DELETE_TRAINING_PLAN, error);
  }
}
