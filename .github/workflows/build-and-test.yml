name: Build and test

on:
  pull_request:
    branches:
      - development

jobs:
  e2e-tests:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        ports:
          - 5432:5432
        env:
          POSTGRES_DB: testdb
          POSTGRES_USER: test
          POSTGRES_PASSWORD: test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    env:
      DB_HOST: localhost
      DB_PORT: 5432
      DB_USERNAME: test
      DB_PASSWORD: test
      DB_DATABASE: testdb
      JWT_ACCESS_TOKEN_SECRET: test-secret
      JWT_REFRESH_TOKEN_SECRET: test-secret
      JWT_ACCESS_TOKEN_EXPIRATION_S: 60
      JWT_REFRESH_TOKEN_EXPIRATION_S: 120
      GROUP_INVITATION_SECRET: test-group-secret
      GROUP_INVITATION_BASE_URL: https://test.app/invite
      GROUP_INVITATION_URL_EXPIRATION: 600s
      GOOGLE_AUTH_CLIENT_ID: test
      GOOGLE_AUTH_CLIENT_SECRET: test
      GOOGLE_AUTH_REDIRECT_URI: test

    steps:
      - uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Install dependencies
        run: npm install

      - name: Build
        run: npm run build

      - name: Run E2E tests
        run: npm run test:e2e
