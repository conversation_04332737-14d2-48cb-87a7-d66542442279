import { Injectable, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { Request } from 'express';
import { AuthService } from '@app/auth/services/auth.service';
import { ITokenPayload } from '@app/constants/interfaces/token-payload.interface';
import { CustomRequest } from '@app/constants/interfaces/custom-request.interface';

@Injectable()
export class JwtRefreshStrategy extends PassportStrategy(Strategy, 'jwt-refresh') {
  constructor(
    configService: ConfigService,
    private readonly authService: AuthService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      secretOrKey: configService.getOrThrow('JWT_REFRESH_TOKEN_SECRET'),
      passReqToCallback: true,
    });
  }

  async validate(request: Request | CustomRequest, payload: ITokenPayload) {
    const authHeader = (request as Request).headers.authorization;
    const refreshToken = authHeader?.split(' ')[1];

    if (!refreshToken) {
      throw new UnauthorizedException('No refresh token provided');
    }

    (request as CustomRequest).refreshToken = refreshToken;

    return this.authService.verifyUserRefreshToken(refreshToken, payload.id);
  }
}
