import React, { useState } from "react";
import { StyleSheet, View } from "react-native";
import { useCameraPermissions } from "expo-camera";
import { ButtonBasic, Typography, useTheme } from "@vs/kit-ui-expo";
import { Scanner } from "@/components/scanner";

export default function QRScannerScreen() {
  const { theme } = useTheme();
  const [permission, requestPermission] = useCameraPermissions();
  const [scanned, setScanned] = useState(false);
  const [groupCode, setGroupCode] = useState<string | null>(null);

  const handleBarCodeScanned = ({
    type,
    data,
  }: {
    type: string;
    data: string;
  }) => {
    if (scanned) return;
    setScanned(true);
    setGroupCode(data);

    // Wyświetlenie alertu z kodem
    alert(`Zeskanowano kod grupy: ${data}`);
  };

  if (!permission) {
    return (
      <View
        style={[
          styles.loadingContainer,
          { backgroundColor: theme.colors.grey[900] },
        ]}
      >
        <Typography
          variant="BodyL"
          color={theme.colors.grey[50]}
          style={styles.loadingText}
        >
          Oczekiwanie na dostęp do kamery...
        </Typography>
      </View>
    );
  }

  if (!permission.granted) {
    return <Scanner.Permission onPress={requestPermission} />;
  }

  return (
    <View
      style={[styles.container, { backgroundColor: theme.colors.grey[900] }]}
    >
      <View style={styles.cameraContainer}>
        <Scanner.CameraView
          scanned={scanned}
          handleBarCodeScanned={handleBarCodeScanned}
        />
      </View>

      <Scanner.Code code={groupCode}>
        {scanned && (
          <ButtonBasic
            onPress={() => setScanned(false)}
            style={{ backgroundColor: theme.colors.accent[300] }}
          >
            Skanuj ponownie
          </ButtonBasic>
        )}
      </Scanner.Code>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    textAlign: "center",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    paddingTop: 40,
  },
  title: {
    flex: 1,
    textAlign: "center",
  },
  backButton: {
    padding: 10,
    borderRadius: 8,
  },
  cameraContainer: {
    flex: 0.7,
    margin: 16,
  },
});
