import { MigrationInterface, QueryRunner } from "typeorm";

export class AddOrderForTrainingExercises1751886521714 implements MigrationInterface {
    name = 'AddOrderForTrainingExercises1751886521714'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "training_exercises" DROP CONSTRAINT "FK_0a7c29d2b153dd18b577c9059d1"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_2c1fcbc16208fe3bff536ddca5"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_0a7c29d2b153dd18b577c9059d"`);
        await queryRunner.query(`ALTER TABLE "training_exercises" ADD "id" uuid NOT NULL DEFAULT uuid_generate_v4()`);
        await queryRunner.query(`ALTER TABLE "training_exercises" DROP CONSTRAINT "PK_31b88086c29615c1c3a82a01374"`);
        await queryRunner.query(`ALTER TABLE "training_exercises" ADD CONSTRAINT "PK_2b1704436a8e673c5eeb813b986" PRIMARY KEY ("training_id", "exercise_id", "id")`);
        await queryRunner.query(`ALTER TABLE "training_exercises" ADD "order_index" integer NOT NULL`);
        await queryRunner.query(`ALTER TABLE "training_exercises" ADD "createdAt" TIMESTAMP NOT NULL DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "training_exercises" DROP CONSTRAINT "FK_2c1fcbc16208fe3bff536ddca57"`);
        await queryRunner.query(`ALTER TABLE "training_exercises" DROP CONSTRAINT "PK_2b1704436a8e673c5eeb813b986"`);
        await queryRunner.query(`ALTER TABLE "training_exercises" ADD CONSTRAINT "PK_a25cbc0a105020f376e30ca7467" PRIMARY KEY ("exercise_id", "id")`);
        await queryRunner.query(`ALTER TABLE "training_exercises" DROP CONSTRAINT "PK_a25cbc0a105020f376e30ca7467"`);
        await queryRunner.query(`ALTER TABLE "training_exercises" ADD CONSTRAINT "PK_3a6acab6b62f3ad827deb3e30d8" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "training_exercises" ADD CONSTRAINT "FK_2c1fcbc16208fe3bff536ddca57" FOREIGN KEY ("training_id") REFERENCES "trainings"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "training_exercises" ADD CONSTRAINT "FK_0a7c29d2b153dd18b577c9059d1" FOREIGN KEY ("exercise_id") REFERENCES "exercises"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "training_exercises" DROP CONSTRAINT "FK_0a7c29d2b153dd18b577c9059d1"`);
        await queryRunner.query(`ALTER TABLE "training_exercises" DROP CONSTRAINT "FK_2c1fcbc16208fe3bff536ddca57"`);
        await queryRunner.query(`ALTER TABLE "training_exercises" DROP CONSTRAINT "PK_3a6acab6b62f3ad827deb3e30d8"`);
        await queryRunner.query(`ALTER TABLE "training_exercises" ADD CONSTRAINT "PK_a25cbc0a105020f376e30ca7467" PRIMARY KEY ("exercise_id", "id")`);
        await queryRunner.query(`ALTER TABLE "training_exercises" DROP CONSTRAINT "PK_a25cbc0a105020f376e30ca7467"`);
        await queryRunner.query(`ALTER TABLE "training_exercises" ADD CONSTRAINT "PK_2b1704436a8e673c5eeb813b986" PRIMARY KEY ("training_id", "exercise_id", "id")`);
        await queryRunner.query(`ALTER TABLE "training_exercises" ADD CONSTRAINT "FK_2c1fcbc16208fe3bff536ddca57" FOREIGN KEY ("training_id") REFERENCES "trainings"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "training_exercises" DROP COLUMN "createdAt"`);
        await queryRunner.query(`ALTER TABLE "training_exercises" DROP COLUMN "order_index"`);
        await queryRunner.query(`ALTER TABLE "training_exercises" DROP CONSTRAINT "PK_2b1704436a8e673c5eeb813b986"`);
        await queryRunner.query(`ALTER TABLE "training_exercises" ADD CONSTRAINT "PK_31b88086c29615c1c3a82a01374" PRIMARY KEY ("training_id", "exercise_id")`);
        await queryRunner.query(`ALTER TABLE "training_exercises" DROP COLUMN "id"`);
        await queryRunner.query(`CREATE INDEX "IDX_0a7c29d2b153dd18b577c9059d" ON "training_exercises" ("exercise_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_2c1fcbc16208fe3bff536ddca5" ON "training_exercises" ("training_id") `);
        await queryRunner.query(`ALTER TABLE "training_exercises" ADD CONSTRAINT "FK_0a7c29d2b153dd18b577c9059d1" FOREIGN KEY ("exercise_id") REFERENCES "exercises"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
    }

}
