import { Injectable, NotFoundException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { TrainingEntity } from '../entities/training.entity';
import { TrainingExerciseEntity } from '../entities/training-exercise.entity';
import { CreateTrainingRequestDto } from '../dto/create-training.dto';
import { UpdateTrainingRequestDto } from '../dto/update-training.dto';
import { UserEntity } from '../../users/entities/user.entity';
import { TrainingStatus } from '../enums/training-status.enum';
import { ExercisesService } from '@app/exercises/services/exercises.service';

@Injectable()
export class TrainingService {
  constructor(
    @InjectRepository(TrainingEntity)
    private trainingRepository: Repository<TrainingEntity>,

    @InjectRepository(TrainingExerciseEntity)
    private trainingExerciseRepository: Repository<TrainingExerciseEntity>,

    private readonly exerciseService: ExercisesService,
  ) {}

  async createTraining(data: CreateTrainingRequestDto, creator: UserEntity): Promise<TrainingEntity> {
    const training = this.trainingRepository.create({
      name: data.name,
      description: data.description,
      tags: data.tags,
      creator,
      creatorId: creator.id,
      status: TrainingStatus.ACTIVE,
    });

    const savedTraining = await this.trainingRepository.save(training);

    await this.createTrainingExercisesWithOrder(savedTraining.id, data.exercises);

    return await this.getTrainingOrThrow(savedTraining.id);
  }

  async findAll(user: UserEntity, status?: TrainingStatus): Promise<TrainingEntity[]> {
    const query = this.trainingRepository
      .createQueryBuilder('training')
      .leftJoinAndSelect('training.trainingExercises', 'trainingExercises')
      .leftJoinAndSelect('trainingExercises.exercise', 'exercise')
      .where('training.creatorId = :creatorId', { creatorId: user.id });

    if (status) {
      query.andWhere('training.status = :status', { status });
    }

    return await query.getMany();
  }

  async findOne(id: string, user: UserEntity): Promise<TrainingEntity> {
    const training = await this.getTrainingOrThrow(id);
    this.assertOwnership(training, user);
    return training;
  }

  async update(id: string, data: UpdateTrainingRequestDto, user: UserEntity): Promise<TrainingEntity> {
    const training = await this.getTrainingOrThrow(id);
    this.assertOwnership(training, user);

    if (data.exercises) {
      await this.exerciseService.getCreatorExercisesOrThrow(data.exercises, user.id);

      await this.trainingExerciseRepository.delete({ trainingId: id });

      await this.createTrainingExercisesWithOrder(id, data.exercises);
    }

    await this.trainingRepository.update(id, {
      name: data.name ?? training.name,
      description: data.description ?? training.description,
      tags: data.tags ?? training.tags,
    });

    return await this.getTrainingOrThrow(id);
  }

  async remove(id: string, user: UserEntity): Promise<{ id: string; status: TrainingStatus }> {
    const training = await this.getTrainingOrThrow(id);
    this.assertOwnership(training, user);

    await this.trainingRepository.update(id, { status: TrainingStatus.DELETED });

    return { id, status: TrainingStatus.DELETED };
  }

  // --------------------------------------------------------------
  //                      Helpers
  // --------------------------------------------------------------
  async getTrainingsByIdsOrThrow(ids: string[], creator: UserEntity): Promise<TrainingEntity[]> {
    const trainings = await this.trainingRepository.find({
      where: { id: In(ids), creatorId: creator.id },
    });

    if (trainings.length !== ids.length) {
      throw new BadRequestException('One or more trainings do not exist or do not belong to you');
    }

    return trainings;
  }

  // --------------------------------------------------------------
  //                      Private helpers
  // --------------------------------------------------------------
  private async getTrainingOrThrow(id: string): Promise<TrainingEntity> {
    if (!id) throw new BadRequestException('Training ID is required');

    const training = await this.trainingRepository.findOne({
      where: { id },
      relations: ['trainingExercises', 'trainingExercises.exercise'],
    });

    if (!training) {
      throw new NotFoundException(`Training with ID ${id} not found`);
    }

    return training;
  }

  private assertOwnership(training: TrainingEntity, creator: UserEntity): void {
    if (training.creatorId !== creator.id) {
      throw new ForbiddenException('You do not have permission to access this training');
    }
  }

  private async createTrainingExercisesWithOrder(trainingId: string, exerciseIds: string[]): Promise<void> {
    await this.exerciseService.getExerciseIdsOrThrow(exerciseIds);

    const trainingExercises = exerciseIds.map((exerciseId, index) =>
      this.trainingExerciseRepository.create({
        trainingId,
        exerciseId,
        orderIndex: index,
      }),
    );

    await this.trainingExerciseRepository.save(trainingExercises);
  }

  /**
   * TODO: Add profanity filter for training name and description
   * For now it's disabled to keep it simple
   */
  // private filterProfanity(text: string): string {
  //   const profanityList = ['brzydkie', 'słowo', 'wulgarny'];
  //   let filteredText = text;

  //   profanityList.forEach((word) => {
  //     const regex = new RegExp(word, 'gi');
  //     filteredText = filteredText.replace(regex, '&**&');
  //   });

  //   return filteredText;
  // }
}
