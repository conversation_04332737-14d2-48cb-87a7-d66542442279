import React from "react";
import { View, Image, StyleSheet } from "react-native";
import { Typography, useTheme, BorderBoxWithGradient } from "@vs/kit-ui-expo";
import { useTranslation } from "react-i18next";

export const DietCard = () => {
  const { theme } = useTheme();
  const { t } = useTranslation();

  const renderHeader = () => (
    <Typography
      variant="Heading5"
      color={theme.colors.grey[50]}
      style={{ marginBottom: theme.spacing[2], marginTop: theme.spacing[4] }}
    >
      {t("user_dashboard_diet")}
    </Typography>
  );

  const renderImage = () => (
    <Image
      source={require("@/assets/images/diet-photo.png")}
      style={styles.image}
      resizeMode="cover"
    />
  );

  const renderContent = () => (
    <View style={styles.textContent}>
      <Typography
        variant="Heading5"
        color={theme.colors.grey[50]}
        style={{ marginBottom: 8 }}
      >
        {t("user_dashboard_diet_title")}
      </Typography>
    </View>
  );

  const renderCardBody = () => (
    <View style={styles.rowLayout}>
      {renderImage()}
      {renderContent()}
    </View>
  );

  const renderCard = () => (
    <BorderBoxWithGradient
      style={{
        borderColor: theme.colors.accent[400],
        paddingTop: 32,
      }}
      gradientColors={["#1b2214", "#28351a", "#354720"]}
    >
      {renderCardBody()}
    </BorderBoxWithGradient>
  );

  return (
    <>
      {renderHeader()}
      {renderCard()}
    </>
  );
};

const styles = StyleSheet.create({
  rowLayout: {
    flexDirection: "row",
    gap: 16,
    marginBottom: 16,
  },
  textContent: {
    flex: 1,
    justifyContent: "center",
  },
  image: {
    width: 110,
    height: 110,
    borderRadius: 8,
  },
});
