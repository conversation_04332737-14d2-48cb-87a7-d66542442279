{"name": "@vs/kit-docs-expo", "version": "0.3.0", "private": true, "main": "index.js", "scripts": {"dev": "expo start --web --port 8090", "android": "expo start --android --port 8090", "ios": "expo start --ios --port 8090", "web": "expo start --web --port 8090", "eject": "expo eject", "storybook-generate": "sb-rn-get-stories"}, "dependencies": {"expo": "~51.0.39", "@vs/kit-ui-expo": "*", "@vs/kit-ui-assets": "*", "@expo/vector-icons": "~14.0.2", "expo-constants": "~16.0.2", "expo-linking": "~6.3.1", "expo-router": "~3.5.24", "expo-status-bar": "~1.12.1", "expo-system-ui": "~3.0.7", "react-dom": "18.2.0", "react": "18.2.0", "react-native": "0.74.5", "react-native-safe-area-context": "4.10.5", "react-native-screens": "3.31.1", "react-native-web": "~0.19.10"}, "devDependencies": {"@babel/core": "^7.23.7", "@expo/webpack-config": "^19.0.0", "@gorhom/bottom-sheet": "^5.1.1", "@react-native-async-storage/async-storage": "^2.1.1", "@react-native-community/datetimepicker": "^8.3.0", "@react-native-community/slider": "^4.5.6", "@storybook/addon-ondevice-actions": "^8.5.5", "@storybook/addon-ondevice-controls": "^8.5.5", "@storybook/react-native": "^8.5.5", "@types/react": "~18.2.14", "@types/react-native": "^0.73.0", "babel-loader": "^8.4.1", "react-native-gesture-handler": "^2.24.0", "react-native-reanimated": "^3.16.7", "react-native-svg": "^15.11.1", "typescript": "~5.3.3"}}