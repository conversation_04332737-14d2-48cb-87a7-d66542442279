import { sliceText } from "@/utils/sliceText";
import { useTheme, BorderBoxWithGradient, Typography } from "@vs/kit-ui-expo";
import { StyleSheet, TouchableOpacity, View } from "react-native";
import { ActionsCloseCircleIcon } from "@vs/kit-ui-assets";

interface Props {
  title: string;
  tags: string[];
  onPress: () => void;
}

export const DragAndDropListCard = ({ title, tags, onPress }: Props) => {
  const { theme } = useTheme();

  const renderTags = () => {
    return (
      <View
        style={[
          styles.tags,
          {
            gap: theme.spacing[1],
          },
        ]}
      >
        {tags.slice(0, 2).map((tag, index) => (
          <Typography
            variant="Link"
            key={`tag-drag-drop-${tag.concat()}-${index}`}
            color={theme.colors.grey[50]}
          >
            {sliceText(tag, 20)}
          </Typography>
        ))}
      </View>
    );
  };

  return (
    <BorderBoxWithGradient
      style={[
        styles.container,
        {
          borderColor: theme.colors.accent[400],
          paddingTop: theme.spacing[4],
          padding: theme.spacing[4],
        },
      ]}
    >
      <TouchableOpacity onPress={onPress} hitSlop={24} style={styles.close}>
        <ActionsCloseCircleIcon
          stroke="white"
          fill="black"
          width={28}
          height={28}
        />
      </TouchableOpacity>
      {renderTags()}
      <Typography
        variant="Heading4"
        style={styles.text}
        color={theme.colors.grey[50]}
      >
        {title}
      </Typography>
    </BorderBoxWithGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    position: "relative",
    gap: 4,
    flex: 1,
  },
  text: {
    textAlign: "center",
    fontWeight: "bold",
  },
  close: {
    position: "absolute",
    top: 4,
    right: 4,
  },
  tags: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
  },
});
