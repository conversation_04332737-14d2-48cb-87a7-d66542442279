import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UsersController } from './controllers/users.controller';
import { UsersService } from './services/users.service';
import { UserEntity } from './entities/user.entity';
import { ConsentsModule } from '@app/consents/consents.module';
import { CqrsModule } from '@nestjs/cqrs';
import { SendOtcHandler } from '@app/users/commands/send-otc.handler';
import { SendOtcEmailHandler } from '@app/users/events/sent-otc-email.handler';
import { MailModule } from '@app/mail/mail.module';
import { UserOtcEntity } from '@app/users/entities/user-otc.entity';

@Module({
  imports: [TypeOrmModule.forFeature([UserEntity, UserOtcEntity]), CqrsModule, ConsentsModule, MailModule],
  controllers: [UsersController],
  providers: [UsersService, SendOtcHandler, SendOtcEmailHandler],
  exports: [UsersService],
})
export class UsersModule {}
