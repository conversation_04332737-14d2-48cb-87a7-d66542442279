import { CredentialsStoreData } from "@/types/authorization";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { create } from "zustand";
import { createJSONStorage, devtools, persist } from "zustand/middleware";

interface State {
  isAuthenticated: boolean;
  credentials: CredentialsStoreData | null;
  setCredentials: (credentials: CredentialsStoreData) => Promise<void>;
  clearCredentials: () => void;
}

export const useAuthStore = create<State>()(
  devtools(
    persist(
      (set) => ({
        isAuthenticated: false,
        credentials: null,
        setCredentials: async (credentials) => {
          try {
            set({ credentials, isAuthenticated: true }, false, "login");
          } catch (error) {
            console.error("Login error:", error);
            throw error;
          }
        },
        clearCredentials: () => {
          set({ credentials: null, isAuthenticated: false }, false, "logout");
        },
      }),
      {
        name: "AuthStore",
        storage: createJSONStorage(() => AsyncStorage),
      }
    )
  )
);
