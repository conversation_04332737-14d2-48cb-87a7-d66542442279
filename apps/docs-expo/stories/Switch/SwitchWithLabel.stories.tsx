import React, { useState } from "react";
import { View, Text } from "react-native";
import type { Meta, StoryObj } from "@storybook/react";
import { SwitchWithLabel } from "@vs/kit-ui-expo";

const meta = {
  title: "Components/Switch/SwitchWithLabel",
  component: SwitchWithLabel,
  argTypes: {
    onValueChange: { action: "switch value changed" },
    size: {
      control: "select",
      options: ["small", "medium", "large"],
    },
  },
} satisfies Meta<typeof SwitchWithLabel>;

export default meta;
type Story = StoryObj<typeof SwitchWithLabel>;

export const Default: Story = {
  render: () => {
    const [value, setValue] = useState(false);

    return (
      <View style={{ padding: 20 }}>
        <View style={{ marginBottom: 30 }}>
          <Text style={{ marginBottom: 10, fontSize: 18, fontWeight: "bold" }}>Small Size</Text>
          <View style={{ flexDirection: "row", justifyContent: "space-around", marginBottom: 20 }}>
            <SwitchWithLabel labelRight="trainer" labelLeft="podopieczny" value={false} size="small" />
            <SwitchWithLabel labelRight="trainer" labelLeft="podopieczny long" value={true} size="small" />
            <SwitchWithLabel labelRight="trainer" labelLeft="podopieczny" value={false} disabled size="small" />
            <SwitchWithLabel labelRight="trainer" labelLeft="podopieczny" value={true} disabled size="small" />
          </View>
        </View>

        <View style={{ marginBottom: 30 }}>
          <Text style={{ marginBottom: 10, fontSize: 18, fontWeight: "bold" }}>Medium Size (Default)</Text>
          <View style={{ flexDirection: "row", justifyContent: "space-around", marginBottom: 20 }}>
            <SwitchWithLabel labelRight="trainer long text" labelLeft="podo" value={false} />
            <SwitchWithLabel labelRight="trainer" labelLeft="podopieczny long" value={true} />
            <SwitchWithLabel labelRight="trainer" labelLeft="podopieczny" value={false} disabled />
            <SwitchWithLabel labelRight="trainer" labelLeft="podopieczny" value={true} disabled />
          </View>
        </View>

        <View style={{ marginBottom: 30 }}>
          <Text style={{ marginBottom: 10, fontSize: 18, fontWeight: "bold" }}>Large Size</Text>
          <View style={{ flexDirection: "row", justifyContent: "space-around", marginBottom: 20 }}>
            <SwitchWithLabel labelRight="trainer" labelLeft="podopieczny" value={false} size="large" />
            <SwitchWithLabel labelRight="trainer" labelLeft="podopieczny long" value={true} size="large" />
            <SwitchWithLabel labelRight="trainer" labelLeft="podopieczny" value={false} disabled size="large" />
            <SwitchWithLabel labelRight="trainer" labelLeft="podopieczny" value={true} disabled size="large" />
          </View>
        </View>

        <View style={{ marginTop: 40 }}>
          <Text style={{ marginBottom: 10, fontSize: 18, fontWeight: "bold" }}>Interactive Example</Text>
          <SwitchWithLabel labelRight="trainer" labelLeft="podopieczny" value={value} onValueChange={setValue} />
        </View>
      </View>
    );
  },
};
