import { Theme, useTheme } from "@vs/kit-ui-expo";
import React from "react";
import { ScrollView, ScrollViewProps, StyleSheet } from "react-native";

interface ScrollViewWithSpacingProps extends ScrollViewProps {
  children: React.ReactNode;
}

const ScrollViewWithSpacing: React.FC<ScrollViewWithSpacingProps> = ({
  style,
  contentContainerStyle,
  children,
  ...props
}) => {
  const { theme } = useTheme();
  const styles = fnStyles(theme);

  return (
    <ScrollView
      {...props}
      style={[styles.container, style]}
      contentContainerStyle={[styles.contentContainer, contentContainerStyle]}
    >
      {children}
    </ScrollView>
  );
};

const fnStyles = (theme: Theme) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    contentContainer: {
      paddingBottom: theme.spacing[24],
    },
  });

export default ScrollViewWithSpacing;
