import React from "react";
import { View } from "react-native";
import type { Meta, StoryObj } from "@storybook/react";
import { LoaderSpinnerSmall, ThemeProvider } from "@vs/kit-ui-expo";

const meta = {
  title: "Components/Loaders/LoaderSpinnerSmall",
  component: LoaderSpinnerSmall,
  decorators: [
    Story => (
      <ThemeProvider>
        <View style={{ padding: 16, alignItems: "center", justifyContent: "center" }}>
          <Story />
        </View>
      </ThemeProvider>
    ),
  ],
} satisfies Meta<typeof LoaderSpinnerSmall>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {};
