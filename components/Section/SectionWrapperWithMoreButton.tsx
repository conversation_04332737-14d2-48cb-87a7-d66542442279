import { Theme, Typography, useTheme } from "@vs/kit-ui-expo";
import { View, StyleSheet } from "react-native";

interface Props {
  children: React.ReactNode;
  title: string;
  bottomText?: string;
}

export const SectionWrapperWithMoreButton = ({
  children,
  title,
  bottomText,
}: Props) => {
  const { theme } = useTheme();
  const fnStyles = styles(theme);

  const renderBottomText = () => {
    if (!bottomText) return null;
    return (
      <View style={fnStyles.footer}>
        <Typography
          variant="BodyXS"
          style={{ textDecorationLine: "underline" }}
          color={theme.colors.grey[50]}
        >
          {bottomText}
        </Typography>
      </View>
    );
  };

  return (
    <View style={fnStyles.container}>
      <View>
        <Typography variant="BodyM" color={theme.colors.grey[50]}>
          {title}
        </Typography>
      </View>
      {children}
      {renderBottomText()}
    </View>
  );
};

const styles = (theme: Theme) =>
  StyleSheet.create({
    container: {
      borderWidth: 1,
      gap: theme.spacing[4],
      borderColor: theme.colors.grey[800],
      width: "100%",
    },
    footer: {
      justifyContent: "center",
      alignItems: "flex-end",
      textAlign: "right",
    },
  });
