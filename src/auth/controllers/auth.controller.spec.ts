import { Test, TestingModule } from '@nestjs/testing';
import { AuthController } from './auth.controller';
import { AuthService } from '@app/auth/services/auth.service';
import { ConsentsService } from '@app/consents/services/consents.service';

describe('AuthController', () => {
  let controller: AuthController;
  let authService: AuthService;
  let consentsService: ConsentsService;

  const mockAuthService = {
    login: jest.fn(),
    refreshToken: jest.fn(),
    logout: jest.fn(),
    logoutAll: jest.fn(),
    getActiveSessions: jest.fn(),
  };

  const mockConsentsService = {
    getLatestConsents: jest.fn(),
    getUserConsents: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AuthController],
      providers: [
        {
          provide: AuthService,
          useValue: mockAuthService,
        },
        {
          provide: ConsentsService,
          useValue: mockConsentsService,
        },
      ],
    }).compile();

    controller = module.get<AuthController>(AuthController);
    authService = module.get<AuthService>(AuthService);
    consentsService = module.get<ConsentsService>(ConsentsService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
