import request from 'supertest';
import { INestApplication } from '@nestjs/common';
import { getRepositoryToken } from '@nestjs/typeorm';
import { UserOtcEntity } from '@app/users/entities/user-otc.entity';

export const createAndGetOtcCode = async (app: INestApplication, email: string): Promise<string> => {
  const response = await request(app.getHttpServer()).post('/v1/user/otc').send({ email, purpose: 'registration' });

  expect(response.status).toBe(201);

  const otcRepo = app.get(getRepositoryToken(UserOtcEntity));
  const otc = await otcRepo.findOneBy({ email });

  expect(otc).toBeDefined();
  return otc.code as string;
};
