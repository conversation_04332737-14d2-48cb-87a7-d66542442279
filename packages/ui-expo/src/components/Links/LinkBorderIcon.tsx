import React, { forwardRef } from "react";
import {
  Text,
  StyleSheet,
  StyleProp,
  ViewStyle,
  TextStyle,
  View,
} from "react-native";
import { LoaderSpinnerSmall } from "../Loaders";
import { Sizes, buttonSizeStyles } from "../../utils/sizes";
import { useTheme } from "../../theme";
import { Link } from "expo-router";

interface Props {
  children?: React.ReactNode;
  size?: Sizes;
  isLoading?: boolean;
  href: string;
  disabled?: boolean;
  style?: StyleProp<ViewStyle>;
  textStyle?: StyleProp<TextStyle>;
  leftIcon?: JSX.Element | React.ReactNode;
  rightIcon?: JSX.Element | React.ReactNode;
}

export const LinkBorderIcon = forwardRef<View, Props>((props, ref) => {
  const {
    children,
    isLoading,
    size = Sizes.Medium,
    href,
    disabled,
    style,
    textStyle,
    leftIcon,
    rightIcon,
  } = props;

  const { theme } = useTheme();
  const sizeStyle = buttonSizeStyles[size];

  const renderContent = () => {
    if (isLoading) {
      return <LoaderSpinnerSmall />;
    }

    return (
      <View style={styles.contentContainer}>
        <View style={styles.iconLeft}>{leftIcon}</View>
        <Text
          adjustsFontSizeToFit
          numberOfLines={1}
          style={[
            styles.text,
            {
              color: disabled ? theme.colors.grey[300] : theme.colors.grey[50],
            },
            textStyle,
          ]}
        >
          {children || "Click"}
        </Text>
        <View style={styles.iconRight}>{rightIcon}</View>
      </View>
    );
  };

  return (
    <View ref={ref} style={{ width: '100%' }}>
      <Link
        style={[
          styles.button,
          sizeStyle,
          {
            backgroundColor: disabled
              ? theme.colors.grey[500]
              : theme.colors.transparent,
            borderColor: disabled
              ? theme.colors.grey[400]
              : theme.colors.accent[300],
            borderRadius: theme.borderRadius.lg,
          },
          style,
        ]}
        href={href}
        disabled={disabled || isLoading}
      >
        {renderContent()}
      </Link>
    </View>
  );
});

const styles = StyleSheet.create({
  button: {
    paddingHorizontal: 24,
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 1,
  },
  contentContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 8,
    width: "100%",
  },
  text: {
    fontSize: 16,
    textAlign: "center",
    flex: 1,
  },
  iconLeft: {
    width: 24,
    marginRight: 4,
    alignItems: "center",
    justifyContent: "center",
  },
  iconRight: {
    width: 24,
    marginLeft: 4,
    alignItems: "center",
    justifyContent: "center",
  },
});
