import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { BurgerIcon } from "@vs/kit-ui-assets";

const meta: Meta<typeof BurgerIcon> = {
  title: "Components/Icons/BurgerIcon",
  component: BurgerIcon,
  tags: ["autodocs"],
  argTypes: {
    color: {
      control: "color",
    },
  },
};

export default meta;
type Story = StoryObj<typeof BurgerIcon>;

export const Default: Story = {
  args: {
    color: "white",
  },
};
