import Link from "next/link";
import { LinkProps } from "next/link";

interface Props
  extends LinkProps,
    Omit<React.AnchorHTMLAttributes<HTMLAnchorElement>, "href"> {
  children?: React.ReactNode;
}

export const LinkGradientSmall = ({ children, href, ...props }: Props) => {
  return (
    <Link
      href={href}
      className="item-gradient disabled:opacity-50 disabled:cursor-not-allowed w-full text-center text-sm flex items-center whitespace-nowrap text-white px-4 rounded-xl"
      {...props}
    >
      {children ?? "Click"}
    </Link>
  );
};
