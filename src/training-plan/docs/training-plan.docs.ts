import { applyDecorators } from '@nestjs/common';
import { ApiT<PERSON>s, ApiBearerAuth, ApiOperation, ApiResponse, ApiParam, ApiQuery, ApiBody } from '@nestjs/swagger';
import { TrainingPlanStatus } from '../enums/training-plan-status.enum';
import { TrainingPlanGroupResponseDto } from '../dto/training-plan-group-response.dto';
import { AssignTrainingPlanToGroupDto } from '../dto/assign-training-plan-to-group.dto';
import { TrainingPlanEntity } from '../entities/training-plan.entity';

export class TrainingPlanSwaggerDocs {
  static apiTags() {
    return applyDecorators(ApiTags('Training Plans'));
  }

  static apiBearerAuth() {
    return applyDecorators(ApiBearerAuth('JWT'));
  }

  static createTrainingPlanDocs() {
    return applyDecorators(
      ApiOperation({
        summary: 'Create a new training plan',
        description:
          'Creates a new training plan with the provided details. Requires trainer role. Endpoint: POST /api/v1/training-plan',
      }),
      ApiResponse({
        status: 201,
        description: 'The training plan has been successfully created.',
        type: TrainingPlanEntity,
      }),
      ApiResponse({
        status: 400,
        description: 'Bad request - validation error or invalid training IDs.',
      }),
      ApiResponse({
        status: 401,
        description: 'Unauthorized - invalid or missing authentication token.',
      }),
      ApiResponse({
        status: 403,
        description: 'Forbidden - user does not have trainer role.',
      }),
    );
  }

  static findAllTrainingPlansDocs() {
    return applyDecorators(
      ApiOperation({
        summary: 'Get all training plans',
        description:
          'Retrieves all training plans for the authenticated trainer. Can be filtered by status. Endpoint: GET /api/v1/training-plan?status=active|deleted',
      }),
      ApiQuery({
        name: 'status',
        required: false,
        enum: TrainingPlanStatus,
        description: 'Filter training plans by status',
      }),
      ApiResponse({
        status: 200,
        description: 'List of training plans',
        type: [TrainingPlanEntity],
      }),
      ApiResponse({
        status: 401,
        description: 'Unauthorized - invalid or missing authentication token.',
      }),
      ApiResponse({
        status: 403,
        description: 'Forbidden - user does not have trainer role.',
      }),
    );
  }

  static findOneTrainingPlanDocs() {
    return applyDecorators(
      ApiOperation({
        summary: 'Get a specific training plan',
        description:
          'Retrieves a specific training plan by ID for the authenticated trainer. Endpoint: GET /api/v1/training-plan/{id}',
      }),
      ApiParam({
        name: 'id',
        required: true,
        description: 'The ID of the training plan to retrieve',
        type: String,
      }),
      ApiResponse({
        status: 200,
        description: 'The training plan details',
        type: TrainingPlanEntity,
      }),
      ApiResponse({
        status: 401,
        description: 'Unauthorized - invalid or missing authentication token.',
      }),
      ApiResponse({
        status: 403,
        description: 'Forbidden - user does not have trainer role or does not own the training plan.',
      }),
      ApiResponse({
        status: 404,
        description: 'Not found - training plan with the given ID does not exist.',
      }),
    );
  }

  static updateTrainingPlanDocs() {
    return applyDecorators(
      ApiOperation({
        summary: 'Update a training plan',
        description:
          'Updates an existing training plan with the provided details. Requires trainer role. Endpoint: PUT /api/v1/training-plan/{id}',
      }),
      ApiParam({
        name: 'id',
        required: true,
        description: 'The ID of the training plan to update',
        type: String,
      }),
      ApiResponse({
        status: 200,
        description: 'The training plan has been successfully updated.',
        type: TrainingPlanEntity,
      }),
      ApiResponse({
        status: 400,
        description: 'Bad request - validation error or invalid training IDs.',
      }),
      ApiResponse({
        status: 401,
        description: 'Unauthorized - invalid or missing authentication token.',
      }),
      ApiResponse({
        status: 403,
        description: 'Forbidden - user does not have trainer role or does not own the training plan.',
      }),
      ApiResponse({
        status: 404,
        description: 'Not found - training plan with the given ID does not exist.',
      }),
    );
  }

  static removeTrainingPlanDocs() {
    return applyDecorators(
      ApiOperation({
        summary: 'Delete a training plan',
        description:
          'Soft deletes a training plan by setting its status to DELETED. Requires trainer role. Endpoint: DELETE /api/v1/training-plan/{id}',
      }),
      ApiParam({
        name: 'id',
        required: true,
        description: 'The ID of the training plan to delete',
        type: String,
      }),
      ApiResponse({
        status: 200,
        description: 'The training plan has been successfully deleted.',
        schema: {
          type: 'object',
          properties: {
            status: {
              type: 'string',
              example: TrainingPlanStatus.DELETED,
            },
          },
        },
      }),
      ApiResponse({
        status: 401,
        description: 'Unauthorized - invalid or missing authentication token.',
      }),
      ApiResponse({
        status: 403,
        description: 'Forbidden - user does not have trainer role or does not own the training plan.',
      }),
      ApiResponse({
        status: 404,
        description: 'Not found - training plan with the given ID does not exist.',
      }),
    );
  }

  static assignTrainingPlanToGroupDocs() {
    return applyDecorators(
      ApiOperation({
        summary: 'Assign a training plan to a group',
        description:
          'Assigns a training plan to a group with a specified start date. Requires trainer role. Endpoint: POST /api/v1/training-plan/{training_plan_id}/group/assign/{group_id}',
      }),
      ApiParam({
        name: 'training_plan_id',
        required: true,
        description: 'The ID of the training plan to assign',
        type: String,
      }),
      ApiParam({
        name: 'group_id',
        required: true,
        description: 'The ID of the group to assign the training plan to',
        type: String,
      }),
      ApiBody({
        type: AssignTrainingPlanToGroupDto,
        description: 'The start date for the training plan in the group',
      }),
      ApiResponse({
        status: 200,
        description: 'The training plan has been successfully assigned to the group.',
        type: TrainingPlanGroupResponseDto,
      }),
      ApiResponse({
        status: 400,
        description: 'Bad request - validation error or invalid date format.',
      }),
      ApiResponse({
        status: 401,
        description: 'Unauthorized - invalid or missing authentication token.',
      }),
      ApiResponse({
        status: 403,
        description: 'Forbidden - user does not have trainer role or does not own the training plan or group.',
      }),
      ApiResponse({
        status: 404,
        description: 'Not found - training plan or group with the given ID does not exist.',
      }),
      ApiResponse({
        status: 409,
        description: 'Conflict - the training plan is already assigned to the group.',
      }),
    );
  }

  static unassignTrainingPlanFromGroupDocs() {
    return applyDecorators(
      ApiOperation({
        summary: 'Unassign a training plan from a group',
        description:
          'Removes the assignment of a training plan from a group. Requires trainer role. Endpoint: DELETE /api/v1/training-plan/{training_plan_id}/group/unassign/{group_id}',
      }),
      ApiParam({
        name: 'training_plan_id',
        required: true,
        description: 'The ID of the training plan to unassign',
        type: String,
      }),
      ApiParam({
        name: 'group_id',
        required: true,
        description: 'The ID of the group to unassign the training plan from',
        type: String,
      }),
      ApiResponse({
        status: 200,
        description: 'The training plan has been successfully unassigned from the group.',
      }),
      ApiResponse({
        status: 401,
        description: 'Unauthorized - invalid or missing authentication token.',
      }),
      ApiResponse({
        status: 403,
        description: 'Forbidden - user does not have trainer role or does not own the training plan or group.',
      }),
      ApiResponse({
        status: 404,
        description:
          'Not found - training plan or group with the given ID does not exist, or the assignment does not exist.',
      }),
    );
  }
}
