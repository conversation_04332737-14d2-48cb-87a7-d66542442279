import React, { useEffect } from "react";
import { StyleSheet, TouchableOpacity } from "react-native";
import { useTheme } from "../../theme";
import { useToast } from "../../contexts";

interface Props {
  children?: React.ReactNode;
}
export const ToastWrapper = ({ children }: Props) => {
  const { theme } = useTheme();
  const { hideToast } = useToast();

  useEffect(() => {
    if (children) {
      const timer = setTimeout(() => {
        hideToast();
      }, 6000);

      return () => clearTimeout(timer);
    }
  }, [children, hideToast]);

  if (!children) return null;
  return (
    <TouchableOpacity onPress={hideToast} style={[styles.container, { padding: theme.spacing[4] }]}>
      {children}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    position: "absolute",
    justifyContent: "flex-start",
    alignItems: "center",
    backgroundColor: "transparent",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 100,
    width: "100%",
    height: "100%",
    flex: 1,
  },
});
