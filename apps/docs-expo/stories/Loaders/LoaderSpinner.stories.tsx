import React from "react";
import { View } from "react-native";
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { LoaderSpinner, ThemeProvider } from "@vs/kit-ui-expo";

const meta = {
  title: "Components/Loaders/LoaderSpinner",
  component: LoaderSpinner,
  decorators: [
    Story => (
      <ThemeProvider>
        <View style={{ padding: 16, alignItems: "center", justifyContent: "center" }}>
          <Story />
        </View>
      </ThemeProvider>
    ),
  ],
} satisfies Meta<typeof LoaderSpinner>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {};
