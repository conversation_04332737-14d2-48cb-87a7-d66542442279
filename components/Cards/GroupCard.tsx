import React from "react";
import { View, Image, StyleSheet, Text, TouchableOpacity } from "react-native";
import {
  Typography,
  useTheme,
  BorderBoxWithGradient,
  ContainerTag,
} from "@vs/kit-ui-expo";
import { useTranslation } from "react-i18next";

interface Props {
  name: string;
  tags: string[];
  onPress?: () => void;
}

export const GroupCard = ({ name, tags, onPress }: Props) => {
  const { theme } = useTheme();
  const { t } = useTranslation();

  const renderTags = () => {
    return (
      <View
        style={[
          styles.tags,
          {
            gap: theme.spacing[4],
          },
        ]}
      >
        {tags.map((tag, index) => (
          <ContainerTag key={`group-tag-${index}`}>{tag}</ContainerTag>
        ))}
      </View>
    );
  };

  return (
    <TouchableOpacity onPress={onPress}>
      <BorderBoxWithGradient
        style={[
          styles.container,
          {
            borderColor: theme.colors.accent[400],
            paddingTop: theme.spacing[10],
            padding: theme.spacing[4],
          },
        ]}
        gradientColors={["#1b2214", "#28351a", "#354720"]}
      >
        {renderTags()}
        <Typography variant="Heading5" color={theme.colors.grey[50]}>
          {name}
        </Typography>
      </BorderBoxWithGradient>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    position: "relative",
  },
  tags: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 16,
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
  },
});
