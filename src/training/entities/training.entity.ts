import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { UserEntity } from '../../users/entities/user.entity';
import { TrainingStatus } from '../enums/training-status.enum';
import { ExerciseEntity } from '../../exercises/entities/exercise.entity';
import { TrainingExerciseEntity } from './training-exercise.entity';
import { Exclude, Expose } from 'class-transformer';
import { BaseEntity } from 'typeorm';

@Entity({ name: 'trainings' })
export class TrainingEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column({ type: 'text' })
  description: string;

  @Column({ nullable: true })
  tags: string;

  @Column({
    type: 'enum',
    enum: TrainingStatus,
    default: TrainingStatus.ACTIVE,
  })
  status: TrainingStatus;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @ManyToOne(() => UserEntity, () => 'trainings', { eager: true })
  @Exclude()
  creator: UserEntity;

  @Column()
  creatorId: string;

  @OneToMany(() => TrainingExerciseEntity, (trainingExercise) => trainingExercise.training)
  @Exclude() // Ukrywamy trainingExercises w API response
  trainingExercises: TrainingExerciseEntity[];

  // Virtual property dla zachowania kompatybilności z API
  @Expose()
  get exercises(): ExerciseEntity[] {
    if (!this.trainingExercises) {
      return [];
    }
    return this.trainingExercises.sort((a, b) => a.orderIndex - b.orderIndex).map((te) => te.exercise);
  }
}
