import { View, Text, StyleSheet, TextStyle } from "react-native";
import { ToastProps } from "./interface";
import React from "react";
import { useTheme } from "../../theme";
import { ActionsWarningIcon as WarningIcon } from "@vs/kit-ui-assets";

export const ToastWarning = ({ title, message, backgroundColor }: ToastProps) => {
  const { theme } = useTheme();

  return (
    <View
      style={[
        styles.container,
        {
          backgroundColor: backgroundColor || theme.colors.grey[600],
        },
      ]}
    >
      <View style={styles.iconWrapper}>
        <WarningIcon style={{ fill: theme.colors.warning[500] }} />
      </View>

      <View style={styles.textWrapper}>
        <Text style={[styles.title, { color: theme.colors.grey[50], ...(theme.typography.BodyM as TextStyle) }]}>
          {title}
        </Text>
        {message && (
          <Text style={[styles.message, { color: theme.colors.grey[50], ...(theme.typography.BodyS as TextStyle) }]}>
            {message}
          </Text>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    width: "100%",
    padding: 12,
    borderRadius: 8,
    gap: 12,
  },
  iconWrapper: {
    width: 24,
    height: 24,
    borderRadius: 12,
    alignItems: "center",
    justifyContent: "center",
  },
  textWrapper: {
    flex: 1,
  },
  title: {
    fontSize: 16,
  },
  message: {
    fontSize: 14,
    marginTop: 2,
  },
});
