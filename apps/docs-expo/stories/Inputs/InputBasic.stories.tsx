import React, { useState } from "react";
import { View } from "react-native";
import type { Meta, StoryObj } from "@storybook/react";
import { InputBasic, ThemeProvider, Sizes } from "@vs/kit-ui-expo";

// Wrapper component to handle state for the Input
const InputWrapper = (props: React.ComponentProps<typeof InputBasic>) => {
  const [value, setValue] = useState(props.value || "");
  return <InputBasic {...props} value={value} onChangeText={setValue} />;
};

// Define a dummy onChangeText function for the stories
const dummyOnChangeText = () => {};

const meta = {
  title: "Components/Inputs/Input",
  component: InputWrapper,
  argTypes: {
    onChangeText: { action: "text changed" },
    size: {
      control: "select",
      options: [Sizes.Small, Sizes.Medium, Sizes.Large],
    },
    disabled: {
      control: "boolean",
    },
    label: {
      control: "text",
    },
    placeholder: {
      control: "text",
    },
  },
  args: {
    placeholder: "Sample text...",
    size: Sizes.Medium,
    disabled: false,
    value: "",
    onChangeText: dummyOnChangeText, // Add default onChangeText
  },
  decorators: [
    Story => (
      <ThemeProvider>
        <View style={{ padding: 16, width: "100%" }}>
          <Story />
        </View>
      </ThemeProvider>
    ),
  ],
} satisfies Meta<typeof InputWrapper>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {};

export const WithLabel: Story = {
  args: {
    label: "Hasło",
  },
};

export const Small: Story = {
  args: {
    size: Sizes.Small,
    label: "Small Input",
  },
};

export const Medium: Story = {
  args: {
    size: Sizes.Medium,
    label: "Medium Input",
  },
};

export const Large: Story = {
  args: {
    size: Sizes.Large,
    label: "Large Input",
  },
};

export const Disabled: Story = {
  args: {
    disabled: true,
    value: "Disabled input text",
    label: "Disabled Input",
  },
};

export const WithPlaceholder: Story = {
  args: {
    placeholder: "Enter your text here...",
    label: "Custom Placeholder",
  },
};

export const CustomStyle: Story = {
  args: {
    style: { backgroundColor: "#2a2a2a", borderColor: "#ff5722", borderWidth: 2 },
    inputStyle: { color: "#ffffff" },
    labelStyle: { color: "#ff5722", fontWeight: "bold" },
    label: "Custom Styled Input",
  },
};
