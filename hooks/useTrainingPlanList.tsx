import { useQuery } from "@tanstack/react-query";
import { useEffect } from "react";
import { useErrorHandling } from "./useErrorHandling";
import { useTrainingPlanStore } from "@/stores/useTrainingPlanStore";
import { getAllTrainingPlan } from "@/api/trainingPlan";

export const useTrainingPlanListQuery = () => {
  const { setTrainingPlans } = useTrainingPlanStore();
  const { withErrorHandling } = useErrorHandling();

  const {
    data: trainingPlans,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ["training-plan"],
    queryFn: async () => {
      const result = await withErrorHandling(async () => {
        return await getAllTrainingPlan();
      });
      return result || [];
    },
    retry: false, // Wyłączamy retry bo błędy obsługuje withErrorHandling
  });

  useEffect(() => {
    if (trainingPlans) {
      setTrainingPlans(trainingPlans);
    }
  }, [trainingPlans, setTrainingPlans]);

  const handleRefetch = () =>
    withErrorHandling(async () => {
      await refetch();
    });

  return {
    trainingPlanList: trainingPlans || [],
    loading: isLoading,
    refetch: handleRefetch,
  };
};
