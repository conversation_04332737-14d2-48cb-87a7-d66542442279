import { <PERSON>tity, PrimaryGeneratedColumn, Column, ManyTo<PERSON>ne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CreateDateColumn } from 'typeorm';
import { TrainingPlanEntity } from './training-plan.entity';
import { TrainingEntity } from '@app/training/entities/training.entity';
import { BaseEntity } from 'typeorm';

@Entity({ name: 'training_plan_trainings' })
export class TrainingPlanTrainingEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'training_plan_id' })
  trainingPlanId: string;

  @Column({ name: 'training_id' })
  trainingId: string;

  @Column({ name: 'order_index' })
  orderIndex: number;

  @CreateDateColumn()
  createdAt: Date;

  @ManyToOne(() => TrainingPlanEntity, (trainingPlan) => trainingPlan.trainingPlanTrainings, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  @JoinColumn({ name: 'training_plan_id' })
  trainingPlan: TrainingPlanEntity;

  @ManyToOne(() => TrainingEntity, { eager: true })
  @JoinColumn({ name: 'training_id' })
  training: TrainingEntity;
}
