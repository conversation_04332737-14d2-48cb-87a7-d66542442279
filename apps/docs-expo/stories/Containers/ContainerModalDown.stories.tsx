import React from "react";
import { View } from "react-native";
import type { Meta, StoryObj } from "@storybook/react";
import { ContainerModalDown, ThemeProvider } from "@vs/kit-ui-expo";

const meta = {
  title: "Components/Containers/ContainerModalDown",
  component: ContainerModalDown,
  argTypes: {
    backgroundColor: {
      control: "color",
    },
    textColor: {
      control: "color",
    },
  },
  args: {
    children: "Container Tab",
  },
  decorators: [
    Story => (
      <ThemeProvider>
        <View style={{ padding: 16 }}>
          <Story />
        </View>
      </ThemeProvider>
    ),
  ],
} satisfies Meta<typeof ContainerModalDown>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {};

export const CustomBackgroundColor: Story = {
  args: {
    backgroundColor: "#FF5722",
  },
};

export const CustomTextColor: Story = {
  args: {
    textColor: "#FFFFFF",
    backgroundColor: "#333333",
  },
};

export const CustomStyle: Story = {
  args: {
    style: {
      width: 200,
      padding: 20,
      borderWidth: 1,
      borderColor: "#ccc",
    },
    textStyle: {
      fontWeight: "bold",
      fontSize: 18,
    },
  },
};

export const LongText: Story = {
  args: {
    children:
      "This is a longer text to demonstrate how the container handles longer content that might wrap to multiple lines.",
    style: { width: 250 },
  },
};

export const MultipleContainers: Story = {
  render: () => (
    <View>
      <ContainerModalDown style={{ marginBottom: 10 }}>First Tab</ContainerModalDown>
      <ContainerModalDown backgroundColor="#4CAF50" textColor="#FFFFFF" style={{ marginBottom: 10 }}>
        Second Tab
      </ContainerModalDown>
      <ContainerModalDown backgroundColor="#2196F3" textColor="#FFFFFF">
        Third Tab
      </ContainerModalDown>
    </View>
  ),
};
