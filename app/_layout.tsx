import "../i18n.config";
import { useFonts } from "expo-font";
import * as SplashScreen from "expo-splash-screen";
import { StatusBar } from "expo-status-bar";
import { useEffect } from "react";
import "react-native-reanimated";
import { Slot } from "expo-router";
import React from "react";
import { theme, ThemeProvider, ToastProvider, useTheme } from "@vs/kit-ui-expo";
import { SafeAreaProvider, SafeAreaView } from "react-native-safe-area-context";
import { LanguageProvider } from "@/contexts/LanguageContext";
import { AuthProvider } from "@/contexts/AuthProvider";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

// Tworzymy instancję QueryClient
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minut
      gcTime: 10 * 60 * 1000, // 10 minut (poprzednio cacheTime)
      refetchOnWindowFocus: false,
      retry: 3,
    },
  },
});

function RootLayoutContent() {
  const { theme } = useTheme();

  return (
    <SafeAreaView
      style={{
        flex: 1,
        backgroundColor: theme.colors.grey[900],
        paddingTop: 0,
      }}
    >
      <Slot />
      <StatusBar style="light" />
    </SafeAreaView>
  );
}

export default function RootLayout() {
  const [loaded] = useFonts({
    SpaceMono: require("../assets/fonts/SpaceMono-Regular.ttf"),
    Outfit: require("../assets/fonts/Outfit-Regular.ttf"),
  });

  useEffect(() => {
    if (loaded) {
      SplashScreen.hideAsync();
    }
  }, [loaded]);

  if (!loaded) {
    return null;
  }

  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider theme={theme}>
        <LanguageProvider>
          <SafeAreaProvider>
            <ToastProvider>
              <AuthProvider>
                <RootLayoutContent />
              </AuthProvider>
            </ToastProvider>
          </SafeAreaProvider>
        </LanguageProvider>
      </ThemeProvider>
    </QueryClientProvider>
  );
}
