import React, { forwardRef } from "react";
import { TouchableOpacity, Text, StyleSheet, StyleProp, ViewStyle, TextStyle, View } from "react-native";
import { LoaderSpinnerSmall } from "../Loaders";
import { Sizes, buttonSizeStyles } from "../../utils/sizes";
import { useTheme } from "../../theme";

interface Props {
  children?: React.ReactNode;
  size?: Sizes;
  isLoading?: boolean;
  onPress?: () => void;
  disabled?: boolean;
  style?: StyleProp<ViewStyle>;
  textStyle?: StyleProp<TextStyle>;
  leftIcon?: JSX.Element | React.ReactNode;
  rightIcon?: JSX.Element | React.ReactNode;
}

export const ButtonBorderIcon = forwardRef<TouchableOpacity, Props>((props, ref) => {
  const { children, isLoading, size = Sizes.Medium, onPress, disabled, style, textStyle, leftIcon, rightIcon } = props;

  const { theme } = useTheme();
  const sizeStyle = buttonSizeStyles[size];

  const renderContent = () => {
    if (isLoading) {
      return <LoaderSpinnerSmall />;
    }

    return (
      <View style={styles.contentContainer}>
        <View style={styles.iconLeft}>{leftIcon}</View>
        <Text
          adjustsFontSizeToFit
          numberOfLines={1}
          style={[
            styles.text,
            {
              color: disabled ? theme.colors.grey[300] : theme.colors.grey[50],
            },
            textStyle,
          ]}
        >
          {children || "Click"}
        </Text>
        <View style={styles.iconRight}>{rightIcon}</View>
      </View>
    );
  };

  return (
    <TouchableOpacity
      ref={ref}
      style={[
        styles.button,
        sizeStyle,
        {
          backgroundColor: disabled ? theme.colors.grey[500] : theme.colors.transparent,
          borderColor: disabled ? theme.colors.grey[400] : theme.colors.accent[300],
          borderRadius: theme.borderRadius.lg,
        },
        style,
      ]}
      onPress={onPress}
      disabled={disabled || isLoading}
    >
      {renderContent()}
    </TouchableOpacity>
  );
});

const styles = StyleSheet.create({
  button: {
    paddingHorizontal: 24,
    width: "100%",
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 1,
  },
  contentContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 8,
  },
  text: {
    fontSize: 16,
    textAlign: "center",
  },
  iconLeft: {
    width: 24,
    marginRight: 4,
    alignItems: "center",
  },
  iconRight: {
    width: 24,
    marginLeft: 4,
    alignItems: "center",
  },
});
