import { Test, TestingModule } from '@nestjs/testing';
import { UsersService } from './users.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { UserEntity } from '../entities/user.entity';
import { UserConsentEntity } from '@app/consents/entities/user-consent.entity';
import { ConsentsService } from '@app/consents/services/consents.service';
import { DataSource, Repository } from 'typeorm';

describe('UserService', () => {
  let service: UsersService;
  let userRepository: Repository<UserEntity>;
  let userConsentRepository: Repository<UserConsentEntity>;

  const mockUserRepository = {
    findOne: jest.fn(),
    find: jest.fn(),
    create: jest.fn(),
    save: jest.fn(),
    update: jest.fn(),
  };

  const mockUserConsentRepository = {
    create: jest.fn(),
    save: jest.fn(),
  };

  const mockConsentsService = {
    getLatestConsents: jest.fn(),
  };

  const mockDataSource = {
    createQueryRunner: jest.fn().mockReturnValue({
      connect: jest.fn(),
      startTransaction: jest.fn(),
      manager: {
        save: jest.fn(),
      },
      commitTransaction: jest.fn(),
      rollbackTransaction: jest.fn(),
      release: jest.fn(),
    }),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UsersService,
        {
          provide: getRepositoryToken(UserEntity),
          useValue: mockUserRepository,
        },
        {
          provide: getRepositoryToken(UserConsentEntity),
          useValue: mockUserConsentRepository,
        },
        {
          provide: ConsentsService,
          useValue: mockConsentsService,
        },
        {
          provide: DataSource,
          useValue: mockDataSource,
        },
      ],
    }).compile();

    service = module.get<UsersService>(UsersService);
    userRepository = module.get<Repository<UserEntity>>(getRepositoryToken(UserEntity));
    userConsentRepository = module.get<Repository<UserConsentEntity>>(getRepositoryToken(UserConsentEntity));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getUserById', () => {
    it('should return a user by id', async () => {
      const mockUser = { id: 'test-id', email: '<EMAIL>', name: 'Test User' };
      mockUserRepository.findOne.mockResolvedValue(mockUser);

      const result = await service.getUserById('test-id');

      expect(mockUserRepository.findOne).toHaveBeenCalledWith({ where: { id: 'test-id' } });
      expect(result).toEqual(mockUser);
    });

    it('should throw an exception if user not found', async () => {
      mockUserRepository.findOne.mockRejectedValue(new Error());

      await expect(service.getUserById('non-existent-id')).rejects.toThrow();
    });
  });

  describe('findAll', () => {
    it('should return an array of users', async () => {
      const mockUsers = [
        { id: 'id1', email: '<EMAIL>', name: 'User 1' },
        { id: 'id2', email: '<EMAIL>', name: 'User 2' },
      ];
      mockUserRepository.find.mockResolvedValue(mockUsers);

      const result = await service.findAll();

      expect(mockUserRepository.find).toHaveBeenCalledWith({});
      expect(result).toEqual(mockUsers);
    });
  });

  describe('getUserByEmail', () => {
    it('should return a user by email', async () => {
      const mockUser = { id: 'test-id', email: '<EMAIL>', name: 'Test User' };
      mockUserRepository.findOne.mockResolvedValue(mockUser);

      const result = await service.getUserByEmail('<EMAIL>');

      expect(mockUserRepository.findOne).toHaveBeenCalledWith({
        where: { email: '<EMAIL>' },
      });
      expect(result).toEqual(mockUser);
    });
  });

  describe('updatePassword', () => {
    it('should update user password', async () => {
      const userId = 'user-id';
      const newPassword = 'new-password';
      mockUserRepository.update.mockResolvedValue({ affected: 1 });

      await service.updatePassword(userId, newPassword);

      expect(mockUserRepository.update).toHaveBeenCalledWith(userId, { password: newPassword });
    });
  });
});
