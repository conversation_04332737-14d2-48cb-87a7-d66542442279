import 'reflect-metadata';
import { INestApplication } from '@nestjs/common';
import request from 'supertest';

import { loginAndAssertUser, registerAndAssertUser } from '../__helpers/auth';
import { buildTestApp } from '../__setup/app.factory';
import { generateTestUser } from '../__helpers/user';
import { ConsentDefinitionEntity } from '@app/consents/entities/consent-definition.entity';
import { createAndAssertConsent, generateGDPRConsentPayload } from '../__helpers/consents';
import { CreateGroupRequestDto } from '@app/groups/dto/create-group.dto';
import { addAndAssertUserToGroup, createAndAssertGroup } from '../__helpers/group';
import { randomUUID } from 'crypto';

describe('/DELETE /v1/group/:groupId', () => {
  let app: INestApplication;
  let accessToken: string;
  let consent: ConsentDefinitionEntity;

  beforeAll(async () => {
    app = await buildTestApp();

    const consentPayload = generateGDPRConsentPayload();
    consent = await createAndAssertConsent(app, consentPayload);

    const testUser = generateTestUser([consent.id]);
    await registerAndAssertUser(app, testUser);
    accessToken = (await loginAndAssertUser(app, testUser)).accessToken;
  });

  afterAll(async () => {
    await app.close();
  });

  // TODO: only trainer should be able to create a group
  it('should delete a group', async () => {
    const payloadToCreateGroup: CreateGroupRequestDto = {
      name: 'Test Exercise E2E',
      description: 'This is a test exercise',
      tags: 'e2e|test',
    };

    const group = await createAndAssertGroup(app, payloadToCreateGroup, accessToken);

    const response = await request(app.getHttpServer())
      .delete(`/v1/group/${group.id}`)
      .set('Authorization', `Bearer ${accessToken}`);

    expect(response.status).toBe(200);
    expect(response.body.message).toBe('Group successfully deleted');

    //=========================================================
    //          Verify if user is added to group
    //=========================================================
    const responseAfterDeleting = await request(app.getHttpServer())
      .get(`/v1/group/${group.id}`)
      .set('Authorization', `Bearer ${accessToken}`);

    expect(responseAfterDeleting.status).toBe(404);
    expect(responseAfterDeleting.body.message).toBe(`Group with id ${group.id} not found`);
  });

  it('should return 401 when user is not authenticated', async () => {
    const payloadToCreateGroup: CreateGroupRequestDto = {
      name: 'Test Exercise E2E',
      description: 'This is a test exercise',
      tags: 'e2e|test',
    };

    const group = await createAndAssertGroup(app, payloadToCreateGroup, accessToken);

    const response = await request(app.getHttpServer()).delete(`/v1/group/${group.id}`);

    expect(response.status).toBe(401);
    expect(response.body.message).toBe('Unauthorized');
  });

  it('should return 404 when group not found', async () => {
    const id = randomUUID();
    const response = await request(app.getHttpServer())
      .delete(`/v1/group/${id}`)
      .set('Authorization', `Bearer ${accessToken}`);

    expect(response.status).toBe(404);
    expect(response.body.message).toBe(`Group with id ${id} not found`);
  });

  it('should return 403 when user is not owner', async () => {
    const payloadToCreateGroup: CreateGroupRequestDto = {
      name: 'Test Exercise E2E',
      description: 'This is a test exercise',
      tags: 'e2e|test',
    };

    const group = await createAndAssertGroup(app, payloadToCreateGroup, accessToken);

    const notOwner = generateTestUser([consent.id]);
    const notOwnerUser = await registerAndAssertUser(app, notOwner);
    await addAndAssertUserToGroup(app, group.id, notOwnerUser.id, accessToken);
    const notOwnerAccessToken = (await loginAndAssertUser(app, notOwner)).accessToken;

    const response = await request(app.getHttpServer())
      .delete(`/v1/group/${group.id}`)
      .set('Authorization', `Bearer ${notOwnerAccessToken}`);

    expect(response.status).toBe(403);
    expect(response.body.message).toBe('Only the group owner can perform this operation');
  });
});
