import { View, TouchableOpacity, StyleSheet } from "react-native";
import { theme } from "@vs/kit-ui-expo";

interface Props {
  currentStep: number;
  totalSteps: number;
  onDotPress: (index: number) => void;
}

export const DotsIndicator = ({
  currentStep,
  totalSteps,
  onDotPress,
}: Props) => (
  <View style={styles.dotsContainer}>
    {Array.from({ length: totalSteps }).map((_, index) => (
      <TouchableOpacity
        key={index}
        onPress={() => onDotPress(index)}
        activeOpacity={0.7}
      >
        <View style={styles.dot}>
          {currentStep === index && <View style={styles.innerDot} />}
        </View>
      </TouchableOpacity>
    ))}
  </View>
);

const styles = StyleSheet.create({
  dotsContainer: {
    position: "absolute",
    bottom: "20%",
    left: 0,
    right: 0,
    flexDirection: "row",
    justifyContent: "center",
  },
  dot: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: "transparent",
    borderWidth: 2,
    borderColor: theme.colors.accent[300],
    marginHorizontal: 6,
    alignItems: "center",
    justifyContent: "center",
  },
  innerDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: theme.colors.accent[300],
  },
});
