import { EUserRole } from "@/enums/roles";
import React, { createContext, useContext } from "react";

type RoleSwitcherContextType = {
  activeRole: EUserRole; // Define the type for active role here
  setActiveRole: (role: EUserRole) => void; // Define the function signature for setting the active role
};

export const RoleSwitcherContext = createContext<
  RoleSwitcherContextType | undefined
>(undefined);

export const RoleSwitcherProvider = ({
  children,
  defaultRole = EUserRole.USER,
}: {
  children: React.ReactNode;
  defaultRole?: EUserRole;
}) => {
  const [activeRole, setActiveRole] = React.useState<EUserRole>(defaultRole);

  const handleSetActiveRole = (role: EUserRole) => {
    setActiveRole(role);
  };

  return (
    <RoleSwitcherContext.Provider
      value={{
        activeRole,
        setActiveRole: handleSetActiveRole,
      }}
    >
      {children}
    </RoleSwitcherContext.Provider>
  );
};

export const useRoleSwitcherProvider = () => {
  const context = useContext(RoleSwitcherContext);
  if (!context) {
    throw new Error(
      "useRoleSwitcher must be used within a RoleSwitcherProvider"
    );
  }
  return context;
};
