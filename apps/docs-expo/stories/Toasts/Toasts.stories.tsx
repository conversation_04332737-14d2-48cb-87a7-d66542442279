import React from "react";
import type { Meta, StoryFn } from "@storybook/react";
import { View } from "react-native";
import { ThemeProvider, ToastError, ToastSuccess, ToastWarning, ToastProps } from "@vs/kit-ui-expo";

type Variant = "error" | "success" | "warning";

interface ToastStoryProps extends ToastProps {
  backgroundColor?: string;
  variant: Variant;
}

const baseArgTypes = {
  title: {
    control: "text",
    defaultValue: "Tytuł",
  },
  message: {
    control: "text",
    defaultValue: "To jest wiadomość toast.",
  },
  backgroundColor: {
    control: "color",
    defaultValue: "#202020",
  },
  variant: {
    control: {
      type: "select",
    },
    options: ["error", "success", "warning"],
    defaultValue: "error",
  },
} as const;

const withDecorator = (Story: any) => (
  <ThemeProvider>
    <View style={{ padding: 16, backgroundColor: "#202020", flex: 1 }}>
      <Story />
    </View>
  </ThemeProvider>
);

// Unified component rendering different variants
const ToastRenderer = ({ variant, ...rest }: ToastStoryProps) => {
  switch (variant) {
    case "success":
      return <ToastSuccess {...rest} />;
    case "warning":
      return <ToastWarning {...rest} />;
    case "error":
    default:
      return <ToastError {...rest} />;
  }
};

const meta: Meta<typeof ToastRenderer> = {
  title: "Components/Toasts/Toast",
  component: ToastRenderer,
  argTypes: baseArgTypes,
  decorators: [withDecorator],
};

export default meta;

export const Default: StoryFn<ToastStoryProps> = args => <ToastRenderer {...args} />;
