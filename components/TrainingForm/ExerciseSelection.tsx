import React from "react";
import { View, FlatList, KeyboardAvoidingView } from "react-native";
import {
  ButtonBasic,
  LoaderSpinnerSmall,
  Typography,
  useTheme,
} from "@vs/kit-ui-expo";
import { useTranslation } from "react-i18next";
import { SingleActivityListCardWithCheckbox } from "@/components/Cards";

interface ExerciseSelectionProps {
  exerciseList: any[];
  loading: boolean;
  trainingExercises: string[];
  onExerciseToggle: (exerciseId: string) => void;
  onClose: () => void;
}

export const ExerciseSelection: React.FC<ExerciseSelectionProps> = ({
  exerciseList,
  loading,
  trainingExercises,
  onExerciseToggle,
  onClose,
}) => {
  const { theme } = useTheme();
  const { t } = useTranslation();

  const renderExerciseList = () => {
    if (loading) return <LoaderSpinnerSmall />;

    return (
      <FlatList
        data={exerciseList}
        numColumns={2}
        contentContainerStyle={{
          gap: theme.spacing[4],
        }}
        columnWrapperStyle={{
          justifyContent: "space-between",
          columnGap: theme.spacing[4],
        }}
        style={{
          flex: 1,
        }}
        keyExtractor={(item, index) => `excercise-item-${item.id}-${index}`}
        ListEmptyComponent={() => (
          <Typography
            variant="BodyM"
            color={theme.colors.grey[50]}
            style={{ textAlign: "center" }}
          >
            {t("activity_exercise_list_empty")}
          </Typography>
        )}
        renderItem={({ item }) => (
          <SingleActivityListCardWithCheckbox
            title={item.name}
            description={item.description}
            status={item.status}
            checked={trainingExercises.includes(item.id)}
            onChecked={() => onExerciseToggle(item.id)}
            onPress={() => {}}
          />
        )}
      />
    );
  };

  return (
    <KeyboardAvoidingView
      style={{
        flex: 1,
        backgroundColor: theme.colors.grey[900],
      }}
      behavior={"padding"}
      keyboardVerticalOffset={50}
    >
      <View
        style={{
          flex: 1,
          padding: theme.spacing[6],
          backgroundColor: theme.colors.grey[900],
        }}
      >
        {renderExerciseList()}
      </View>
      <ButtonBasic onPress={onClose}>Akceptuj wybrane</ButtonBasic>
    </KeyboardAvoidingView>
  );
};
