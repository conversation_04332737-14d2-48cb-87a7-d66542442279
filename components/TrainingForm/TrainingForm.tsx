import React from "react";
import { Controller } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { InputBasic, InputTextArea } from "@vs/kit-ui-expo";

interface TrainingFormProps {
  control: any;
  errors: any;
}

export const TrainingForm: React.FC<TrainingFormProps> = ({
  control,
  errors,
}) => {
  const { t } = useTranslation();

  return (
    <>
      <Controller
        control={control}
        name="name"
        render={({ field: { onChange, onBlur, value } }) => (
          <InputBasic
            onBlur={onBlur}
            onChangeText={onChange}
            value={value}
            label={t("creators_form_new_training_name")}
            placeholder={t("creators_form_placeholder")}
            error={t(errors.name?.message || "")}
          />
        )}
      />
      <Controller
        control={control}
        name="description"
        render={({ field: { onChange, onBlur, value } }) => (
          <InputTextArea
            onBlur={onBlur}
            onChangeText={onChange}
            value={value}
            label={t("creators_form_new_training_description")}
            placeholder={t("creators_form_placeholder")}
            error={t(errors.description?.message || "")}
          />
        )}
      />
      <Controller
        control={control}
        name="tags"
        render={({ field: { onChange, onBlur, value } }) => (
          <InputBasic
            onBlur={onBlur}
            onChangeText={onChange}
            value={value || ""}
            label={t("creators_form_new_training_tags")}
            placeholder={t("creators_form_placeholder")}
            error={t(errors.tags?.message || "")}
          />
        )}
      />
    </>
  );
};
