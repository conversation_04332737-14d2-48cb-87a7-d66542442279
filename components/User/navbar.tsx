import React, { useState } from "react";
import { View, StyleSheet, Image, Modal, TouchableOpacity } from "react-native";
import { theme, Typography } from "@vs/kit-ui-expo";
// import { MenuDotsIcon } from "@vs/kit-ui-assets";

export default function UserNavbar() {
  const [isMenuVisible, setIsMenuVisible] = useState(false);

  const renderHeader = () => (
    <View style={styles.topRow}>
      <Image
        source={require("@/assets/images/logo.png")}
        style={styles.image}
      />
      <TouchableOpacity onPress={() => setIsMenuVisible(true)}>
        {/* <MenuDotsIcon fill={theme.colors.accent[300]} width={32} height={32} /> */}
      </TouchableOpacity>
    </View>
  );

  const renderMenuItems = () => (
    <View style={styles.menuItem}>
      <Typography variant="BodyL" color={theme.colors.grey[50]}>
        Item1
      </Typography>
      <Typography variant="BodyL" color={theme.colors.grey[50]}>
        Item2
      </Typography>
      <Typography variant="BodyL" color={theme.colors.grey[50]}>
        Item3
      </Typography>
    </View>
  );

  const renderMenuModal = () => (
    <Modal
      animationType="fade"
      transparent={true}
      visible={isMenuVisible}
      onRequestClose={() => setIsMenuVisible(false)}
    >
      <TouchableOpacity
        style={styles.modalOverlay}
        activeOpacity={1}
        onPress={() => setIsMenuVisible(false)}
      >
        <View style={styles.sidePanel}>
          <Typography
            variant="Heading3"
            color={theme.colors.grey[50]}
            style={{ marginBottom: 24, marginTop: 40 }}
          >
            Menu
          </Typography>
          {renderMenuItems()}
        </View>
      </TouchableOpacity>
    </Modal>
  );

  return (
    <View>
      {renderHeader()}
      {renderMenuModal()}
    </View>
  );
}

const styles = StyleSheet.create({
  topRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    width: "100%",
    marginBottom: 16,
  },
  image: {
    width: 74,
    height: 48,
    resizeMode: "contain",
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: theme.colors.grey[900] + "90",
  },
  sidePanel: {
    width: "60%",
    height: "100%",
    backgroundColor: theme.colors.grey[900],
    padding: 24,
    borderTopRightRadius: 16,
    borderBottomRightRadius: 16,
  },
  menuItem: {
    marginBottom: 12,
    flexDirection: "column",
    gap: 8,
  },
});
