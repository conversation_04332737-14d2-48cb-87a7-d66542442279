import { ActivityStatus } from "@/enums/activityStatus";
import { sliceText } from "@/utils/sliceText";
import {
  useTheme,
  BorderBoxWithGradient,
  Typography,
  ContainerTag,
  ButtonBorder,
  Sizes,
} from "@vs/kit-ui-expo";
import { useTranslation } from "react-i18next";
import { StyleSheet, View } from "react-native";

interface Props {
  title: string;
  description: string;
  status?: string;
  tags?: string[];
  onPress?: () => void;
}

export const SingleActivityListCard = ({
  title,
  description,
  status,
  tags,
  onPress,
}: Props) => {
  const { theme } = useTheme();
  const { t } = useTranslation();

  const renderTags = () => {
    if (!tags) return null;
    return (
      <View
        style={[
          styles.tags,
          {
            gap: theme.spacing[1],
          },
        ]}
      >
        {tags.slice(0, 2).map((tag, index) => (
          <ContainerTag key={`group-tag-${index}`}>
            {sliceText(tag)}
          </ContainerTag>
        ))}
      </View>
    );
  };

  return (
    <BorderBoxWithGradient
      style={[
        styles.container,
        {
          opacity: status === ActivityStatus.DELETED ? 0.5 : 1,
          borderColor: theme.colors.accent[400],
          paddingTop: theme.spacing[10],
          padding: theme.spacing[4],
        },
      ]}
    >
      {renderTags()}
      <Typography
        variant="BodyM"
        style={styles.text}
        color={theme.colors.grey[50]}
      >
        {title}
      </Typography>
      <Typography
        variant="BodyXS"
        style={styles.text}
        color={theme.colors.grey[50]}
      >
        {description}
      </Typography>
      <ButtonBorder
        size={Sizes.Small}
        disabled={status === ActivityStatus.DELETED}
        onPress={onPress}
      >
        {t("activity_single_list_card_edit")}
      </ButtonBorder>
    </BorderBoxWithGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    position: "relative",
    gap: 8,
    flex: 1,
    justifyContent: "space-between",
  },
  text: {
    textAlign: "center",
  },
  tags: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 16,
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
  },
});
