import { Entity, PrimaryGeneratedColumn, Column, ManyToOne, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { UserEntity } from '@app/users/entities/user.entity';

import { ExerciseStatus } from '../enums/exercise-status.enum';
import { DifficultyLevel } from '../enums/difficulty-level.enum';
import { Exclude } from 'class-transformer';
import { BaseEntity } from 'typeorm';

@Entity({ name: 'exercises' })
export class ExerciseEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column({ type: 'text' })
  description: string;

  @Column({ nullable: true })
  tags: string;

  @Column({
    type: 'enum',
    enum: DifficultyLevel,
    default: DifficultyLevel.MEDIUM,
  })
  difficultyLevel: DifficultyLevel;

  @Column({ nullable: true })
  videoUrl: string;

  @Column({ nullable: true })
  imageUrl: string;

  @Column({
    type: 'enum',
    enum: ExerciseStatus,
    default: ExerciseStatus.PENDING,
  })
  status: ExerciseStatus;

  @Column({ nullable: true })
  statusMessage: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @ManyToOne(() => UserEntity, () => 'exercises', { eager: true })
  @Exclude()
  creator: UserEntity;

  @Column()
  creatorId: string;

  @Column({ nullable: true })
  instructions: string;
}
