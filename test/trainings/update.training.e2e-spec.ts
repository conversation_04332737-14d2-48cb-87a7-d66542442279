import 'reflect-metadata';
import { INestApplication } from '@nestjs/common';
import request from 'supertest';

import { loginAndAssertUser, registerAndAssertUser } from '../__helpers/auth';
import { buildTestApp } from '../__setup/app.factory';
import { generateTestUser } from '../__helpers/user';
import { CreateTrainingRequestDto } from '@app/training/dto/create-training.dto';
import { createAndAssertTraining } from '../__helpers/training';
import { randomUUID } from 'node:crypto';
import { createAndAssertExercise } from '../__helpers/exercise';
import { CreateExerciseRequestDto } from '@app/exercises/dto/create-exercise.dto';
import { DifficultyLevel } from '@app/exercises/enums/difficulty-level.enum';
import { ConsentDefinitionEntity } from '@app/consents/entities/consent-definition.entity';
import { createAndAssertConsent, generateGDPRConsentPayload } from '../__helpers/consents';

describe('/PUT /v1/training/:id', () => {
  let app: INestApplication;
  let accessToken: string;
  let consent: ConsentDefinitionEntity;

  beforeAll(async () => {
    app = await buildTestApp();

    const consentPayload = generateGDPRConsentPayload();
    consent = await createAndAssertConsent(app, consentPayload);

    const testUser = generateTestUser([consent.id]);
    await registerAndAssertUser(app, testUser);
    accessToken = (await loginAndAssertUser(app, testUser)).accessToken;
  });

  afterAll(async () => {
    await app.close();
  });

  it('should update a training name and tags', async () => {
    const payload: CreateTrainingRequestDto = {
      name: 'Test training name',
      description: 'Test training description',
      exercises: [],
      tags: '',
    };

    const training = await createAndAssertTraining(app, payload, accessToken);

    const response = await request(app.getHttpServer())
      .put(`/v1/training/${training.id}`)
      .set('Authorization', `Bearer ${accessToken}`)
      .send({ name: 'Updated training name', tags: 'updated-tag' });

    expect(response.status).toBe(200);
    expect(response.body.id).toBe(training.id);
    expect(response.body.name).toBe('Updated training name');
    expect(response.body.tags).toBe('updated-tag');
  });

  it('should update a training exercises', async () => {
    const exercisePayload: CreateExerciseRequestDto = {
      name: 'Test Exercise E2E',
      description: 'This is a test exercise',
      tags: 'e2e|test',
      difficultyLevel: DifficultyLevel.MEDIUM,
      instructions: 'Step 1: … Step 2: …',
    };
    const exerciseToCreateTraining = await createAndAssertExercise(app, exercisePayload, accessToken);
    const exerciseToUpdateTraining = await createAndAssertExercise(app, exercisePayload, accessToken);

    const trainingPayload: CreateTrainingRequestDto = {
      name: 'Test training name',
      description: 'Test training description',
      exercises: [exerciseToCreateTraining.id],
      tags: '',
    };
    const training = await createAndAssertTraining(app, trainingPayload, accessToken);

    const response = await request(app.getHttpServer())
      .put(`/v1/training/${training.id}`)
      .set('Authorization', `Bearer ${accessToken}`)
      .send({ exercises: [exerciseToUpdateTraining.id] });

    expect(response.status).toBe(200);
    expect(response.body.id).toBe(training.id);
    expect(response.body.exercises.length).toBe(1);
    expect(response.body.exercises[0].id).toBe(exerciseToUpdateTraining.id);
  });

  it('should update a training exercises order', async () => {
    const exercisePayload: CreateExerciseRequestDto = {
      name: 'Test Exercise E2E',
      description: 'This is a test exercise',
      tags: 'e2e|test',
      difficultyLevel: DifficultyLevel.MEDIUM,
      instructions: 'Step 1: … Step 2: …',
    };

    const exerciseToUpdateTraining1 = await createAndAssertExercise(app, exercisePayload, accessToken);
    const exerciseToUpdateTraining2 = await createAndAssertExercise(app, exercisePayload, accessToken);

    const trainingPayload: CreateTrainingRequestDto = {
      name: 'Test training name',
      description: 'Test training description',
      exercises: [exerciseToUpdateTraining1.id, exerciseToUpdateTraining2.id],
      tags: '',
    };
    const training = await createAndAssertTraining(app, trainingPayload, accessToken);

    const response = await request(app.getHttpServer())
      .put(`/v1/training/${training.id}`)
      .set('Authorization', `Bearer ${accessToken}`)
      .send({ exercises: [exerciseToUpdateTraining2.id, exerciseToUpdateTraining1.id] });

    expect(response.status).toBe(200);
    expect(response.body.id).toBe(training.id);
    expect(response.body.exercises.length).toBe(2);
    expect(response.body.exercises[0].id).toBe(exerciseToUpdateTraining2.id);
  });

  it('should fail to update a training exercises with invalid exercise id', async () => {
    const exercisePayload: CreateExerciseRequestDto = {
      name: 'Test Exercise E2E',
      description: 'This is a test exercise',
      tags: 'e2e|test',
      difficultyLevel: DifficultyLevel.MEDIUM,
      instructions: 'Step 1: … Step 2: …',
    };
    const exercise = await createAndAssertExercise(app, exercisePayload, accessToken);

    const trainingPayload: CreateTrainingRequestDto = {
      name: 'Test training name',
      description: 'Test training description',
      exercises: [exercise.id],
      tags: '',
    };
    const training = await createAndAssertTraining(app, trainingPayload, accessToken);

    const response = await request(app.getHttpServer())
      .put(`/v1/training/${training.id}`)
      .set('Authorization', `Bearer ${accessToken}`)
      .send({ exercises: [randomUUID()] });

    expect(response.status).toBe(400);
    expect(response.body.error).toBe('Bad Request');
    expect(response.body.message).toBe('One or more exercises do not exist or do not belong to you');
  });

  it('should return 401 if user is not authenticated', async () => {
    const payload: CreateTrainingRequestDto = {
      name: 'Test training name',
      description: 'Test training description',
      exercises: [],
      tags: '',
    };

    const training = await createAndAssertTraining(app, payload, accessToken);

    const response = await request(app.getHttpServer()).put(`/v1/training/${training.id}`).send({
      name: 'Updated training name',
      tags: 'updated-tag',
    });

    expect(response.status).toBe(401);
    expect(response.body.message).toBe('Unauthorized');
  });

  it('should return 404 if training is not found', async () => {
    const uuid = randomUUID();

    const response = await request(app.getHttpServer())
      .put(`/v1/training/${uuid}`)
      .set('Authorization', `Bearer ${accessToken}`)
      .send({
        name: 'Updated training name',
        tags: 'updated-tag',
      });

    expect(response.status).toBe(404);
    expect(response.body.message).toBe('Training with ID ' + uuid + ' not found');
  });
});
