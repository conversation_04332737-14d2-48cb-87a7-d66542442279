# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added

- Group Management feature:

  - Added new entities `GroupEntity` and `GroupUserEntity` for storing groups and user-group relationships
  - Implemented CRUD operations for groups
  - Added endpoints for managing groups:
    - `POST /api/v1/group` - Create a new group
    - `GET /api/v1/group` - Get all groups for the current user
    - `GET /api/v1/group/{group_id}` - Get details of a specific group
    - `PUT /api/v1/group/{group_id}` - Update a group's details
    - `POST /api/v1/group/{group_id}/user/{user_id}` - Add a user to a group
    - `DELETE /api/v1/group/{group_id}/user/{user_id}` - Remove a user from a group
  - Implemented secure invitation URLs using JWT for adding users to groups
  - Added database migration for the new entities
  - Updated documentation with information about the new feature

- Training Plan feature:

  - Added new entity `TrainingPlanEntity` for storing training plans
  - Implemented CRUD operations for training plans
  - Added endpoints for managing training plans:
    - `POST /api/v1/training-plan` - Create a new training plan
    - `GET /api/v1/training-plan?status=active|deleted` - Get all training plans
    - `GET /api/v1/training-plan/{training_plan_id}` - Get a specific training plan
    - `PUT /api/v1/training-plan/{training_plan_id}` - Update a training plan
    - `DELETE /api/v1/training-plan/{training_plan_id}` - Delete a training plan (soft delete)
  - Added database migration for the new entity
  - Updated documentation with information about the new feature

- Training Plan Group Assignment feature:
  - Added new entity `TrainingPlanGroupEntity` for storing relationships between training plans and groups
  - Implemented functionality to assign and unassign training plans to/from groups
  - Added endpoints for managing training plan assignments:
    - `POST /api/v1/training-plan/{training_plan_id}/group/assign/{group_id}` - Assign a training plan to a group with a start date
    - `DELETE /api/v1/training-plan/{training_plan_id}/group/unassign/{group_id}` - Unassign a training plan from a group
  - Added database migration for the new entity
  - Updated documentation with information about the new feature

### Changed

- Training Module API:
  - Updated API routes from `/api/v1/training` to `/v1/training` for consistency
  - Implemented route constants for better maintainability
  - Updated controller to use `CurrentBaseUser` decorator instead of `@Request()`
  - Changed response types from `TrainingResponseDto` to `TrainingEntity`
  - Updated Swagger documentation to reflect these changes

### Updated

- E2E Tests:
  - Updated training module E2E tests to reflect API changes
  - Enhanced test assertions to verify exercise objects in responses
  - Added tests for additional fields in responses (createdAt, updatedAt, creatorId)
  - Updated API route handling to use constants from `TRAINING_ROUTES`
  - Improved test structure and organization
  - Updated documentation in TESTING.md with details about training module tests
  - Enhanced training-plan module E2E tests:
    - Added tests for profanity filtering in training plan name and description
    - Added tests for updating trainings in a training plan
    - Added tests for handling duplicate assignments of training plans to groups
    - Added tests for invalid date formats in group assignments
    - Added tests for non-existent assignments when unassigning
    - Added tests for role-based access control
    - Improved test coverage for edge cases and error handling
    - Updated assertions to verify response fields (createdAt, updatedAt)
    - Updated documentation in TESTING.md with comprehensive details about training plan module tests
