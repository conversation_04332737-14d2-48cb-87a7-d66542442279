import { Stack } from "expo-router";
import { View, StyleSheet } from "react-native";
import { Typography, LinkBasic, useTheme } from "@vs/kit-ui-expo";

export default function NotFoundScreen() {
  const { theme } = useTheme();

  return (
    <>
      <Stack.Screen name="not-found" options={{ title: "Oops!" }} />
      <View
        style={[styles.container, { backgroundColor: theme.colors.grey[900] }]}
      >
        <Typography
          variant="Heading2"
          color={theme.colors.grey[50]}
          style={styles.text}
        >
          This screen doesn't exist.
        </Typography>
        <LinkBasic href="/" style={styles.link}>
          Go to home screen!
        </LinkBasic>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    padding: 20,
    textAlign: "center",
  },
  link: {
    marginTop: 15,
    width: "auto",
  },
  text: {
    textAlign: "center",
  },
});
