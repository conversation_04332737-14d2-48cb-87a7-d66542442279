import type { Config } from "tailwindcss";

export default {
  content: ["./src/**/*.{js,ts,jsx,tsx,mdx}", "./dist/**/*.{js,ts,jsx,tsx,mdx}", "./stories/**/*.{js,ts,jsx,tsx,mdx}"],
  theme: {
    extend: {
      colors: {
        grey: {
          50: "#eeeeee",
          100: "#cacaca",
          200: "#b0b0b0",
          300: "#8c8c8c",
          400: "#767676",
          500: "#545454",
          600: "#3c3c3c",
          700: "#1f1f1f",
          800: "#181818",
          900: "#121212",
        },
        accent: {
          50: "#f3f9ec",
          100: "#edf6e3",
          200: "#daedc5",
          300: "#87c543",
          400: "#7ab13c",
          500: "#6c9e36",
          600: "#659432",
          700: "#517628",
          800: "#3d591e",
          900: "#2f4517",
        },
        alert: {
          50: "#f8e9e8",
          100: "#e8bbb7",
          200: "#dd9a95",
          300: "#cd6c64",
          400: "#c34f46",
          500: "#b42318",
          600: "#a42016",
          700: "#801911",
          800: "#63130d",
          900: "#4c0f0a",
        },
        warning: {
          50: "#fef4e6",
          100: "#fdddb3",
          200: "#fbcc8e",
          300: "#fab55a",
          400: "#f9a63a",
          500: "#f79009",
          600: "#e18308",
          700: "#af6606",
          800: "#884f05",
          900: "#683c04",
        },
        success: {
          50: "#e6f6ec",
          100: "#b0e2c3",
          200: "#8ad5a7",
          300: "#54c17e",
          400: "#33b565",
          500: "#00a33f",
          600: "#009439",
          700: "#00742d",
          800: "#005a23",
          900: "#00441a",
        },
      },
      fontFamily: {
        outfit: ["Outfit", "sans-serif"],
      },
      fontSize: {
        Display1: ["64px", { lineHeight: "82px", fontWeight: "600" }],
        Heading1: ["48px", { lineHeight: "62px", fontWeight: "600" }],
        Heading2: ["40px", { lineHeight: "48px", fontWeight: "600" }],
        Heading3: ["32px", { lineHeight: "38px", fontWeight: "600" }],
        Heading4: ["24px", { lineHeight: "31px", fontWeight: "600" }],
        Heading5: ["20px", { lineHeight: "26px", fontWeight: "600" }],
        BodyL: ["18px", { lineHeight: "22px", fontWeight: "300" }],
        BodyM: ["16px", { lineHeight: "20px", fontWeight: "300" }],
        BodyS: ["14px", { lineHeight: "18px", fontWeight: "300" }],
        BodyXS: ["12px", { lineHeight: "16px", fontWeight: "300" }],
        SubtitleL: ["18px", { lineHeight: "24px", fontWeight: "500" }],
        SubtitleM: ["16px", { lineHeight: "20px", fontWeight: "500" }],
        SubtitleS: ["14px", { lineHeight: "18px", fontWeight: "500" }],
        SubtitleXS: ["12px", { lineHeight: "16px", fontWeight: "500" }],
        Link: ["14px", { lineHeight: "16px", fontWeight: "300" }],
      },
    },
  },
  plugins: [],
} satisfies Config;
