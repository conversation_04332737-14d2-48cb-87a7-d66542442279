import { useRoleSwitcherProvider } from "@/contexts/RoleSwitcherProvider";
import { EUserRole } from "@/enums/roles";

interface Props {
  extRole: EUserRole;
}
export const useRoleSwitcher = ({ extRole }: Props) => {
  const { activeRole, setActiveRole } = useRoleSwitcherProvider();
  const baseUserOn = activeRole !== extRole;

  const handleChangeRole = (e: boolean) => {
    setActiveRole(e ? EUserRole.USER : extRole);
  };

  return {
    handleChangeRole,
    baseUserOn,
  };
};
