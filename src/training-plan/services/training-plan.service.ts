import { Injectable, NotFoundException, ForbiddenException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TrainingPlanEntity } from '../entities/training-plan.entity';
import { TrainingPlanTrainingEntity } from '../entities/training-plan-training.entity';
import { CreateTrainingPlanRequestDto } from '../dto/create-training-plan.dto';
import { UpdateTrainingPlanRequestDto } from '../dto/update-training-plan.dto';
import { UserEntity } from '@app/users/entities/user.entity';
import { TrainingPlanStatus } from '../enums/training-plan-status.enum';

import { TrainingPlanGroupEntity } from '../entities/training-plan-group.entity';
import { AssignTrainingPlanToGroupDto } from '../dto/assign-training-plan-to-group.dto';
import { TrainingPlanGroupResponseDto } from '../dto/training-plan-group-response.dto';
import { GroupsService } from '@app/groups/services/groups.service';
import { TrainingService } from '@app/training/services/training.service';

@Injectable()
export class TrainingPlanService {
  constructor(
    @InjectRepository(TrainingPlanEntity)
    private trainingPlanRepository: Repository<TrainingPlanEntity>,

    @InjectRepository(TrainingPlanTrainingEntity)
    private trainingPlanTrainingRepository: Repository<TrainingPlanTrainingEntity>,

    @InjectRepository(TrainingPlanGroupEntity)
    private trainingPlanGroupRepository: Repository<TrainingPlanGroupEntity>,

    private readonly groupService: GroupsService,
    private readonly trainingService: TrainingService,
  ) {}

  async createTrainingPlan(data: CreateTrainingPlanRequestDto, creator: UserEntity): Promise<TrainingPlanEntity> {
    const trainingPlan = this.trainingPlanRepository.create({
      name: data.name,
      description: data.description,
      creator,
      creatorId: creator.id,
      status: TrainingPlanStatus.ACTIVE,
    });

    const savedTrainingPlan = await this.trainingPlanRepository.save(trainingPlan);

    await this.createTrainingPlanTrainingsWithOrder(savedTrainingPlan.id, data.trainings, creator);

    return await this.getTrainingPlanOrThrow(savedTrainingPlan.id);
  }

  async findAll(creator: UserEntity, status?: TrainingPlanStatus): Promise<TrainingPlanEntity[]> {
    const query = this.trainingPlanRepository
      .createQueryBuilder('trainingPlan')
      .leftJoinAndSelect('trainingPlan.trainingPlanTrainings', 'trainingPlanTrainings')
      .leftJoinAndSelect('trainingPlanTrainings.training', 'training')
      .where('trainingPlan.creatorId = :creatorId', { creatorId: creator.id });

    if (status) {
      query.andWhere('trainingPlan.status = :status', { status });
    }

    return query.getMany();
  }

  async findOne(id: string, creator: UserEntity): Promise<TrainingPlanEntity> {
    const trainingPlan = await this.getTrainingPlanOrThrow(id);
    this.assertOwnership(trainingPlan, creator);
    return trainingPlan;
  }

  async update(id: string, data: UpdateTrainingPlanRequestDto, creator: UserEntity): Promise<TrainingPlanEntity> {
    const trainingPlan = await this.getTrainingPlanOrThrow(id);
    this.assertOwnership(trainingPlan, creator);

    if (data.trainings) {
      await this.trainingService.getTrainingsByIdsOrThrow(data.trainings, creator);

      await this.trainingPlanTrainingRepository.delete({ trainingPlanId: id });

      await this.createTrainingPlanTrainingsWithOrder(id, data.trainings, creator);
    }

    await this.trainingPlanRepository.update(id, {
      name: data.name ?? trainingPlan.name,
      description: data.description ?? trainingPlan.description,
    });

    return await this.getTrainingPlanOrThrow(id);
  }

  async remove(id: string, creator: UserEntity): Promise<{ id: string; status: TrainingPlanStatus }> {
    const trainingPlan = await this.getTrainingPlanOrThrow(id);
    this.assertOwnership(trainingPlan, creator);

    await this.trainingPlanRepository.update(id, { status: TrainingPlanStatus.DELETED });

    return { id, status: TrainingPlanStatus.DELETED };
  }

  async assignToGroup(
    trainingPlanId: string,
    groupId: string,
    assignDto: AssignTrainingPlanToGroupDto,
    creator: UserEntity,
  ): Promise<TrainingPlanGroupResponseDto> {
    const trainingPlan = await this.getTrainingPlanOrThrow(trainingPlanId);
    this.assertOwnership(trainingPlan, creator);

    await this.groupService.getGroupOrThrowAndAssertOwnership(groupId, creator);

    const existingAssignment = await this.trainingPlanGroupRepository.findOne({
      where: { trainingPlanId, groupId },
    });

    if (existingAssignment) {
      throw new ConflictException('This training plan is already assigned to the group');
    }

    const trainingPlanGroup = this.trainingPlanGroupRepository.create({
      trainingPlanId,
      groupId,
      startDate: new Date(assignDto.startDate),
    });

    const saved = await this.trainingPlanGroupRepository.save(trainingPlanGroup);

    return {
      id: saved.id,
      trainingPlanId: saved.trainingPlanId,
      groupId: saved.groupId,
      startDate: saved.startDate.toISOString().split('T')[0],
      createdAt: saved.createdAt,
    };
  }

  async unassignFromGroup(trainingPlanId: string, groupId: string, creator: UserEntity): Promise<void> {
    await this.getTrainingPlanOrThrow(trainingPlanId);
    await this.groupService.getGroupOrThrowAndAssertOwnership(groupId, creator);
    const assignment = await this.getAssignmentOrThrow(trainingPlanId, groupId);

    await this.trainingPlanGroupRepository.remove(assignment);
  }

  // --------------------------------------------------------------
  //                      Private methods
  // --------------------------------------------------------------

  private async getTrainingPlanOrThrow(id: string): Promise<TrainingPlanEntity> {
    const plan = await this.trainingPlanRepository.findOne({
      where: { id },
      relations: ['trainingPlanTrainings', 'trainingPlanTrainings.training'],
    });
    if (!plan) throw new NotFoundException(`Training plan with ID ${id} not found`);
    return plan;
  }

  private async getAssignmentOrThrow(trainingPlanId: string, groupId: string): Promise<TrainingPlanGroupEntity> {
    const assignment = await this.trainingPlanGroupRepository.findOne({
      where: { trainingPlanId, groupId },
    });
    if (!assignment) {
      throw new NotFoundException('This training plan is not assigned to the specified group');
    }
    return assignment;
  }

  private assertOwnership(plan: TrainingPlanEntity, creator: UserEntity): void {
    if (plan.creatorId !== creator.id) {
      throw new ForbiddenException('You do not have permission to access this training plan');
    }
  }

  private async createTrainingPlanTrainingsWithOrder(
    trainingPlanId: string,
    trainingIds: string[],
    creator: UserEntity,
  ): Promise<void> {
    await this.trainingService.getTrainingsByIdsOrThrow(trainingIds, creator);

    const trainingPlanTrainings = trainingIds.map((trainingId, index) =>
      this.trainingPlanTrainingRepository.create({
        trainingPlanId,
        trainingId,
        orderIndex: index,
      }),
    );

    await this.trainingPlanTrainingRepository.save(trainingPlanTrainings);
  }

  /**
   * TODO: Add profanity filter for training name and description
   * For now it's disabled to keep it simple
   */
  // private filterProfanity(text: string): string {
  //   // Simple profanity filter - in a real app, you'd use a more comprehensive library
  //   const profanityList = ['brzydkie', 'słowo', 'wulgarny'];
  //   let filteredText = text;

  //   profanityList.forEach((word) => {
  //     const regex = new RegExp(word, 'gi');
  //     filteredText = filteredText.replace(regex, '&**&');
  //   });

  //   return filteredText;
  // }
}
