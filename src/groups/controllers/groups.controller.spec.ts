import { Test, TestingModule } from '@nestjs/testing';
import { GroupsController } from './groups.controller';
import { GroupsService } from '../services/groups.service';
import { CreateGroupRequestDto } from '../dto/create-group.dto';
import { UpdateGroupRequestDto } from '../dto/update-group.dto';
import { UserEntity } from '@app/users/entities/user.entity';
import { EUserRole } from '@app/constants/models/user-role.enum';
import { GroupEntity } from '../entities/group.entity';

describe('GroupsController', () => {
  let controller: GroupsController;

  const mockGroupsService = {
    createGroup: jest.fn(),
    getGroups: jest.fn(),
    getGroupById: jest.fn(),
    updateGroup: jest.fn(),
    addUserToGroup: jest.fn(),
    removeUserFromGroup: jest.fn(),
  };

  const mockUser: UserEntity = {
    id: 'user-id',
    email: '<EMAIL>',
    name: 'Test User',
    password: 'hashed-password',
    roles: [EUserRole.TRAINER],
    createdAt: new Date(),
    refreshTokens: [],
    consents: [],
    hashPassword: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [GroupsController],
      providers: [
        {
          provide: GroupsService,
          useValue: mockGroupsService,
        },
      ],
    }).compile();

    controller = module.get<GroupsController>(GroupsController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('createGroup', () => {
    it('should create a new group', async () => {
      const CreateGroupRequestDto: CreateGroupRequestDto = {
        name: 'Test Group',
        description: 'Test Description',
        tags: 'tag1|tag2',
      };

      const expectedResult: GroupEntity = {
        id: 'group-id',
        name: 'Test Group',
        description: 'Test Description',
        tags: 'tag1|tag2',
        invitationUrl: 'https://invite-to-group.pl/token',
        ownerId: mockUser.id,
        createdAt: new Date(),
        updatedAt: new Date(),
        groupUsers: [],
        trainingPlanGroups: [],
      };

      mockGroupsService.createGroup.mockResolvedValue(expectedResult);

      const result = await controller.createGroup(CreateGroupRequestDto, mockUser);

      expect(mockGroupsService.createGroup).toHaveBeenCalledWith(CreateGroupRequestDto, mockUser);
      expect(result).toEqual(expectedResult);
    });
  });

  describe('getGroups', () => {
    it('should return all groups for a user', async () => {
      const mockGroupUserEntities = [
        {
          id: 'group-user-id-1',
          group: {
            id: 'group-id-1',
            name: 'Group 1',
            description: 'Description 1',
            tags: 'tag1|tag2',
            invitationUrl: 'https://invite-to-group.pl/token1',
            ownerId: mockUser.id,
            createdAt: new Date(),
            updatedAt: new Date(),
            groupUsers: [],
            trainingPlanGroups: [],
          },
          user: mockUser,
          createdAt: new Date(),
        },
        {
          id: 'group-user-id-2',
          group: {
            id: 'group-id-2',
            name: 'Group 2',
            description: 'Description 2',
            tags: 'tag3|tag4',
            invitationUrl: 'https://invite-to-group.pl/token2',
            ownerId: mockUser.id,
            createdAt: new Date(),
            updatedAt: new Date(),
            groupUsers: [],
            trainingPlanGroups: [],
          },
          user: mockUser,
          createdAt: new Date(),
        },
      ];

      mockGroupsService.getGroups.mockResolvedValue(mockGroupUserEntities);

      const result = await controller.getGroups(mockUser);

      expect(mockGroupsService.getGroups).toHaveBeenCalledWith(mockUser);
      expect(result).toEqual(mockGroupUserEntities);
    });
  });

  describe('getGroupById', () => {
    it('should return a group by id', async () => {
      const groupId = 'group-id';
      const expectedResult: GroupEntity = {
        id: groupId,
        name: 'Test Group',
        description: 'Test Description',
        tags: 'tag1|tag2',
        invitationUrl: 'https://invite-to-group.pl/token',
        ownerId: 'owner-id',
        createdAt: new Date(),
        updatedAt: new Date(),
        groupUsers: [],
        trainingPlanGroups: [],
      };

      mockGroupsService.getGroupById.mockResolvedValue(expectedResult);

      const result = await controller.getGroupById(groupId, mockUser);

      expect(mockGroupsService.getGroupById).toHaveBeenCalledWith(groupId, mockUser);
      expect(result).toEqual(expectedResult);
    });
  });

  describe('updateGroup', () => {
    it('should update a group', async () => {
      const groupId = 'group-id';
      const UpdateGroupRequestDto: UpdateGroupRequestDto = {
        name: 'Updated Group Name',
        description: 'Updated Description',
        tags: 'updated|tags',
      };

      const expectedResult: GroupEntity = {
        id: groupId,
        name: UpdateGroupRequestDto.name!,
        description: UpdateGroupRequestDto.description,
        tags: UpdateGroupRequestDto.tags,
        invitationUrl: 'https://invite-to-group.pl/token',
        ownerId: mockUser.id,
        createdAt: new Date(),
        updatedAt: new Date(),
        groupUsers: [],
        trainingPlanGroups: [],
      };

      mockGroupsService.updateGroup.mockResolvedValue(expectedResult);

      const result = await controller.updateGroup(groupId, UpdateGroupRequestDto, mockUser);

      expect(mockGroupsService.updateGroup).toHaveBeenCalledWith(groupId, UpdateGroupRequestDto, mockUser);
      expect(result).toEqual(expectedResult);
    });
  });

  describe('addUserToGroup', () => {
    it('should add a user to a group', async () => {
      const groupId = 'group-id';
      const userId = 'user-to-add-id';

      mockGroupsService.addUserToGroup.mockResolvedValue(undefined);

      await controller.addUserToGroup(groupId, userId, mockUser);

      expect(mockGroupsService.addUserToGroup).toHaveBeenCalledWith(groupId, userId, mockUser);
    });
  });

  describe('removeUserFromGroup', () => {
    it('should remove a user from a group', async () => {
      const groupId = 'group-id';
      const userId = 'user-to-remove-id';

      mockGroupsService.removeUserFromGroup.mockResolvedValue(undefined);

      await controller.removeUserFromGroup(groupId, userId, mockUser);

      expect(mockGroupsService.removeUserFromGroup).toHaveBeenCalledWith(groupId, userId, mockUser);
    });
  });
});
