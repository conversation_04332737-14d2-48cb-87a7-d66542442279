import { BadRequestException, ForbiddenException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { ExerciseEntity } from '../entities/exercise.entity';
import { CreateExerciseRequestDto } from '../dto/create-exercise.dto';
import { UpdateExerciseRequestDto } from '../dto/update-exercise.dto';
import { UserEntity } from '../../users/entities/user.entity';
import { ExerciseStatus } from '../enums/exercise-status.enum';

@Injectable()
export class ExercisesService {
  constructor(
    @InjectRepository(ExerciseEntity)
    private readonly exercisesRepository: Repository<ExerciseEntity>,
  ) {}

  async create(data: CreateExerciseRequestDto, creator: UserEntity): Promise<ExerciseEntity> {
    const exercise = this.exercisesRepository.create({
      ...data,
      creator,
      creatorId: creator.id,
      status: ExerciseStatus.PENDING,
    });

    return await this.exercisesRepository.save(exercise);
  }

  async findAll(creator: UserEntity, status?: ExerciseStatus): Promise<ExerciseEntity[]> {
    const query = this.exercisesRepository
      .createQueryBuilder('exercise')
      .where('exercise.creatorId = :creatorId', { creatorId: creator.id });

    if (status) {
      query.andWhere('exercise.status = :status', { status });
    }

    return await query.getMany();
  }

  async findOne(id: string, creator: UserEntity): Promise<ExerciseEntity> {
    const exercise = await this.getExerciseOrThrow(id);

    this.assertOwnership(exercise, creator);

    return exercise;
  }

  async update(id: string, data: UpdateExerciseRequestDto, creator: UserEntity): Promise<ExerciseEntity> {
    const exercise = await this.getExerciseOrThrow(id);
    this.assertOwnership(exercise, creator);

    const updated = {
      ...exercise,
      ...data,
      status: ExerciseStatus.PENDING,
    };

    return await this.exercisesRepository.save(updated);
  }

  async remove(id: string, creator: UserEntity): Promise<{ id: string; status: ExerciseStatus }> {
    const exercise = await this.getExerciseOrThrow(id);
    this.assertOwnership(exercise, creator);

    await this.exercisesRepository.update(id, { status: ExerciseStatus.DELETED });
    return { id, status: ExerciseStatus.DELETED };
  }

  // --------------------------------------------------------------
  //                      Helpers
  // --------------------------------------------------------------
  async getCreatorExercisesOrThrow(exerciseIds: string[], creatorId: string): Promise<ExerciseEntity[]> {
    const exercises = await this.exercisesRepository.find({
      where: { id: In(exerciseIds), creatorId },
    });

    if (exercises.length !== exerciseIds.length) {
      throw new BadRequestException('One or more exercises do not exist or do not belong to you');
    }

    return exercises;
  }

  async getExerciseIdsOrThrow(exerciseIds: string[]): Promise<ExerciseEntity[]> {
    const exercises = await this.exercisesRepository.find({
      where: { id: In(exerciseIds) },
    });

    if (exercises.length !== exerciseIds.length) {
      throw new BadRequestException(`One or more exercises do not exist ${exerciseIds}`);
    }

    return exercises;
  }

  // --------------------------------------------------------------
  //                      Private methods
  // --------------------------------------------------------------
  private async getExerciseOrThrow(id: string): Promise<ExerciseEntity> {
    if (!id) {
      throw new BadRequestException('Exercise ID is required');
    }

    const exercise = await this.exercisesRepository.findOne({ where: { id } });

    if (!exercise) {
      throw new NotFoundException(`Exercise with ID ${id} not found`);
    }

    return exercise;
  }

  private assertOwnership(exercise: ExerciseEntity, user: UserEntity): void {
    if (exercise.creatorId !== user.id) {
      throw new ForbiddenException('You do not have permission to access this exercise');
    }
  }
}
