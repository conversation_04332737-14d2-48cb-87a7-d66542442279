import 'reflect-metadata';
import { INestApplication } from '@nestjs/common';
import request from 'supertest';

import { loginAndAssertUser, registerAndAssertUser } from '../__helpers/auth';
import { CreateExerciseRequestDto } from '@app/exercises/dto/create-exercise.dto';
import { DifficultyLevel } from '@app/exercises/enums/difficulty-level.enum';
import { buildTestApp } from '../__setup/app.factory';
import { generateTestUser } from '../__helpers/user';
import { ConsentDefinitionEntity } from '@app/consents/entities/consent-definition.entity';
import { createAndAssertConsent, generateGDPRConsentPayload } from '../__helpers/consents';

describe('/POST /v1/exercise', () => {
  let app: INestApplication;
  let accessToken: string;
  let consent: ConsentDefinitionEntity;

  beforeAll(async () => {
    app = await buildTestApp();

    const consentPayload = generateGDPRConsentPayload();
    consent = await createAndAssertConsent(app, consentPayload);

    const testUser = generateTestUser([consent.id]);
    await registerAndAssertUser(app, testUser);
    accessToken = (await loginAndAssertUser(app, testUser)).accessToken;
  });

  afterAll(async () => {
    await app.close();
  });

  it('should create a new exercise', async () => {
    const payload: CreateExerciseRequestDto = {
      name: 'Test Exercise E2E',
      description: 'This is a test exercise',
      tags: 'e2e|test',
      difficultyLevel: DifficultyLevel.MEDIUM,
      instructions: 'Step 1: … Step 2: …',
    };

    const response = await request(app.getHttpServer())
      .post('/v1/exercise')
      .set('Authorization', `Bearer ${accessToken}`)
      .send(payload);

    expect(response.status).toBe(201);
    expect(response.body.id).toBeDefined();
    expect(response.body.name).toBe(payload.name);
    expect(response.body.description).toBe(payload.description);
    expect(response.body.tags).toBe(payload.tags);
    expect(response.body.difficultyLevel).toBe(payload.difficultyLevel);
    expect(response.body.videoUrl).toBeNull();
    expect(response.body.imageUrl).toBeNull();
    expect(response.body.instructions).toBeDefined();
    expect(response.body.status).toBe('pending');
    expect(response.body.statusMessage).toBeNull();
    expect(response.body.createdAt).toBeDefined();
    expect(response.body.updatedAt).toBeDefined();
    expect(response.body.creatorId).toBeDefined();
  });

  it('should return 400 when "name" is missing', async () => {
    const payload: CreateExerciseRequestDto = {
      name: 'Test Exercise E2E',
      description: 'This is a test exercise',
      tags: 'e2e|test',
      difficultyLevel: DifficultyLevel.MEDIUM,
      instructions: 'Step 1: … Step 2: …',
    };

    const { description, ...rest } = payload;
    const response = await request(app.getHttpServer())
      .post('/v1/exercise')
      .set('Authorization', `Bearer ${accessToken}`)
      .send({ description }); // name missing

    expect(response.status).toBe(400);
    expect(response.body.message).toEqual(
      expect.arrayContaining(['name must be a string', 'name should not be empty']),
    );
  });

  it('should return 400 when "description" is missing', async () => {
    const payload: CreateExerciseRequestDto = {
      name: 'Test Exercise E2E',
      description: 'This is a test exercise',
      tags: 'e2e|test',
      difficultyLevel: DifficultyLevel.MEDIUM,
      instructions: 'Step 1: … Step 2: …',
    };

    const { name, ...rest } = payload;
    const response = await request(app.getHttpServer())
      .post('/v1/exercise')
      .set('Authorization', `Bearer ${accessToken}`)
      .send({ name }); // description missing

    expect(response.status).toBe(400);
    expect(response.body.message).toEqual(
      expect.arrayContaining(['description must be a string', 'description should not be empty']),
    );
  });

  it('should return 401 when user is not authenticated', async () => {
    const payload: CreateExerciseRequestDto = {
      name: 'Test Exercise E2E',
      description: 'This is a test exercise',
      tags: 'e2e|test',
      difficultyLevel: DifficultyLevel.MEDIUM,
      instructions: 'Step 1: … Step 2: …',
    };

    const response = await request(app.getHttpServer()).post('/v1/exercise').send(payload);

    expect(response.status).toBe(401);
    expect(response.body.message).toBe('Unauthorized');
  });

  // TODO: Add this case when ROLES are implemented
  it.todo('should return 403 when user is not trainer');
});
