import React from "react";
import { View, StyleSheet } from "react-native";
import {
  ButtonBorder,
  ButtonBasicIcon,
  Typography,
  Sizes,
  Theme,
  useTheme,
} from "@vs/kit-ui-expo";
import { ActionsAddCircleIcon } from "@vs/kit-ui-assets";
import { useTranslation } from "react-i18next";

interface TrainingHeaderProps {
  id: string | string[] | undefined;
  isNewInstance: boolean;
  onDeleteTraining: (id: string) => void;
  onAddExercise: () => void;
  errors: any;
}

export const TrainingHeader: React.FC<TrainingHeaderProps> = ({
  id,
  isNewInstance,
  onDeleteTraining,
  onAddExercise,
  errors,
}) => {
  const { theme } = useTheme();
  const { t } = useTranslation();
  const fnStyles = styles(theme);

  const renderActionButtons = () => {
    if (id && typeof id === "string" && !isNewInstance) {
      return (
        <View style={fnStyles.actionButtons}>
          <ButtonBorder
            onPress={() => onDeleteTraining(id)}
            size={Sizes.Small}
            style={{ width: 200 }}
            textStyle={{ fontSize: 16 }}
          >
            {t("edit_training_delete_button")}
          </ButtonBorder>
        </View>
      );
    }
    return null;
  };

  const renderHeader = () => {
    return (
      <View style={fnStyles.header}>
        <Typography variant="Heading4" color={theme.colors.grey[50]}>
          {t("activity_training_list_title")}
        </Typography>
      </View>
    );
  };

  const renderAddExerciseButton = () => {
    return (
      <ButtonBasicIcon
        size={Sizes.Small}
        rightIcon={
          <ActionsAddCircleIcon
            stroke={theme.colors.grey[900]}
            fill="transparent"
          />
        }
        onPress={onAddExercise}
        style={{
          width: 200,
        }}
      >
        {t("creators_form_new_training_add_excercise")}
      </ButtonBasicIcon>
    );
  };

  return (
    <>
      {renderActionButtons()}
      {renderHeader()}
      {renderAddExerciseButton()}
      {errors.trainingExercises && (
        <Typography
          variant="BodyS"
          color={theme.colors.alert[400]}
          style={{ marginTop: theme.spacing[2], textAlign: "center" }}
        >
          {t(errors.trainingExercises.message || "")}
        </Typography>
      )}
      <Typography variant="Link" style={fnStyles.instructions}>
        Naciśnij i przeciągnij elementy aby zmienić ich kolejność
      </Typography>
    </>
  );
};

const styles = (theme: Theme) =>
  StyleSheet.create({
    actionButtons: {
      flexDirection: "row",
      justifyContent: "flex-end",
    },
    header: {
      gap: theme.spacing[4],
      borderColor: "white",
      flexDirection: "column",
      alignItems: "center",
      justifyContent: "space-between",
    },
    instructions: {
      fontSize: 12,
      color: theme.colors.grey[50],
      textAlign: "center",
      marginTop: 16,
      paddingBottom: 8,
    },
  });
