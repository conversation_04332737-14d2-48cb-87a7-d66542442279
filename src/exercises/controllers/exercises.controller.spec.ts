import { Test, TestingModule } from '@nestjs/testing';
import { ExercisesController } from './exercises.controller';
import { ExercisesService } from '../services/exercises.service';
import { CreateExerciseRequestDto } from '../dto/create-exercise.dto';
import { UpdateExerciseRequestDto } from '../dto/update-exercise.dto';
import { ExerciseStatus } from '../enums/exercise-status.enum';
import { UserEntity } from '@app/users/entities/user.entity';
import { ExerciseEntity } from '../entities/exercise.entity';
import { EUserRole } from '@app/constants/models/user-role.enum';
import { DifficultyLevel } from '../enums/difficulty-level.enum';

describe('ExercisesController', () => {
  let controller: ExercisesController;
  let service: ExercisesService;

  const mockUser: UserEntity = {
    id: 'user-id',
    email: '<EMAIL>',
    name: 'Test User',
    password: 'hashedPassword',
    consents: ['RODO'] as any,
    roles: [EUserRole.TRAINER],
    createdAt: new Date(),
    refreshTokens: [],
    hashPassword: jest.fn(),
  };

  const mockExercise: ExerciseEntity = {
    id: 'exercise_id_1',
    name: 'Pompki',
    description: 'Ćwiczenie na klatkę piersiową',
    tags: 'klatka|triceps|ramiona',
    difficultyLevel: DifficultyLevel.MEDIUM,
    videoUrl: 'https://example.com/video.mp4',
    imageUrl: 'https://example.com/image.jpg',
    status: ExerciseStatus.PENDING,
    statusMessage: '',
    creator: mockUser,
    creatorId: 'user-id',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockExercisesService = {
    create: jest.fn(),
    findAll: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ExercisesController],
      providers: [
        {
          provide: ExercisesService,
          useValue: mockExercisesService,
        },
      ],
    }).compile();

    controller = module.get<ExercisesController>(ExercisesController);
    service = module.get<ExercisesService>(ExercisesService);

    // Reset all mocks before each test
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create', () => {
    it('should create an exercise', async () => {
      const CreateExerciseRequestDto: CreateExerciseRequestDto = {
        name: 'Pompki',
        description: 'Ćwiczenie na klatkę piersiową',
        tags: 'klatka|triceps|ramiona',
        difficultyLevel: DifficultyLevel.MEDIUM,
        videoUrl: 'https://example.com/video.mp4',
        imageUrl: 'https://example.com/image.jpg',
        instructions: 'Wykonaj 3 serie po 10 powtórzeń',
      };

      mockExercisesService.create.mockResolvedValue(mockExercise);

      const result = await controller.create(CreateExerciseRequestDto, mockUser);

      expect(service.create).toHaveBeenCalledWith(CreateExerciseRequestDto, mockUser);
      expect(result).toEqual(mockExercise);
    });
  });

  describe('findAll', () => {
    it('should return all exercises with status filter', async () => {
      mockExercisesService.findAll.mockResolvedValue([mockExercise]);

      const result = await controller.findAll(ExerciseStatus.PENDING, mockUser);

      expect(service.findAll).toHaveBeenCalledWith(mockUser, ExerciseStatus.PENDING);
      expect(result).toEqual([mockExercise]);
    });

    it('should return all exercises without status filter', async () => {
      mockExercisesService.findAll.mockResolvedValue([mockExercise]);

      const result = await controller.findAll(undefined, mockUser);

      expect(service.findAll).toHaveBeenCalledWith(mockUser, undefined);
      expect(result).toEqual([mockExercise]);
    });
  });

  describe('findOne', () => {
    it('should return an exercise by id', async () => {
      mockExercisesService.findOne.mockResolvedValue(mockExercise);

      const result = await controller.findOne('exercise_id_1', mockUser);

      expect(service.findOne).toHaveBeenCalledWith('exercise_id_1', mockUser);
      expect(result).toEqual(mockExercise);
    });
  });

  describe('update', () => {
    it('should update an exercise with partial data', async () => {
      const updateExerciseDto: UpdateExerciseRequestDto = {
        name: 'Updated Exercise Name',
      };

      const updatedExercise = {
        ...mockExercise,
        name: 'Updated Exercise Name',
      };

      mockExercisesService.update.mockResolvedValue(updatedExercise);

      const result = await controller.update('exercise_id_1', updateExerciseDto, mockUser);

      expect(service.update).toHaveBeenCalledWith('exercise_id_1', updateExerciseDto, mockUser);
      expect(result.name).toBe('Updated Exercise Name');
    });

    it('should update an exercise with multiple fields', async () => {
      const updateExerciseDto: UpdateExerciseRequestDto = {
        name: 'Updated Exercise Name',
        description: 'Updated description',
        tags: 'updated|tags',
        difficultyLevel: DifficultyLevel.HARD,
      };

      const updatedExercise = {
        ...mockExercise,
        name: 'Updated Exercise Name',
        description: 'Updated description',
        tags: 'updated|tags',
        difficultyLevel: DifficultyLevel.HARD,
      };

      mockExercisesService.update.mockResolvedValue(updatedExercise);

      const result = await controller.update('exercise_id_1', updateExerciseDto, mockUser);

      expect(service.update).toHaveBeenCalledWith('exercise_id_1', updateExerciseDto, mockUser);
      expect(result).toEqual(updatedExercise);
    });
  });

  describe('remove', () => {
    it('should remove an exercise', async () => {
      mockExercisesService.remove.mockResolvedValue({ id: 'exercise_id_1', status: ExerciseStatus.DELETED });

      const result = await controller.remove('exercise_id_1', mockUser);

      expect(service.remove).toHaveBeenCalledWith('exercise_id_1', mockUser);
      expect(result).toEqual({ id: 'exercise_id_1', status: ExerciseStatus.DELETED });
    });
  });
});
