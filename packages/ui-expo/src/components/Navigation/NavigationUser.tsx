import React from "react";
import { TouchableOpacity, StyleSheet, View } from "react-native";
import { NavigationContainer } from "./NavigationContainer";
import { NavigationMainDot } from "./NavigationMainDot";
import { useTheme } from "../../theme";
import { NavigationAssets } from "./NavigationIcons";

interface NavigationItemProps {
  icon: React.ReactNode;
  href: string;
  isActive: boolean;
}

export const NavigationItem = ({ icon, href, isActive }: NavigationItemProps) => {
  const { theme } = useTheme();
  const { colors } = theme;

  const styles = StyleSheet.create({
    item: {
      alignItems: "center",
      justifyContent: "center",
      padding: 8,
    },
    active: {
      backgroundColor: colors.grey[600],
      borderRadius: 8,
    },
  });

  return (
    <TouchableOpacity style={[styles.item, isActive && styles.active]} onPress={() => {}}>
      <View>{icon}</View>
    </TouchableOpacity>
  );
};

interface NavigationUserProps {
  homeRoute?: string;
  profileRoute?: string;
  settingsRoute?: string;
  mainRoute?: string;
  peopleRoute?: string;
  mainIcon?: React.ReactNode;
  onMainPress?: () => void;
}

export const NavigationUser = ({
  homeRoute = "/home",
  profileRoute = "/profile",
  settingsRoute = "/settings",
  mainRoute = "/main",
  peopleRoute = "/people",
  mainIcon = <NavigationAssets.NavigationPeopleNearbyIcon />,
  onMainPress,
}: NavigationUserProps) => {
  const pathname = mainRoute;

  const isActive = (route: string) => {
    return pathname === route;
  };

  const handleMainPress = () => {
    if (onMainPress) {
      onMainPress();
    }
  };

  return (
    <NavigationContainer>
      <NavigationItem
        href={homeRoute}
        isActive={isActive(homeRoute)}
        icon={
          isActive(homeRoute) ? (
            <NavigationAssets.NavigationHomeAngleIcon />
          ) : (
            <NavigationAssets.NavigationHomeAngleOutlineIcon />
          )
        }
      />
      <NavigationItem
        href={profileRoute}
        isActive={isActive(profileRoute)}
        icon={
          isActive(profileRoute) ? (
            <NavigationAssets.NavigationUserIcon />
          ) : (
            <NavigationAssets.NavigationUserOutlineIcon />
          )
        }
      />
      <TouchableOpacity onPress={handleMainPress}>
        <NavigationMainDot>{mainIcon}</NavigationMainDot>
      </TouchableOpacity>
      <NavigationItem
        href={peopleRoute}
        isActive={isActive(peopleRoute)}
        icon={
          isActive(peopleRoute) ? (
            <NavigationAssets.NavigationPeopleNearbyIcon />
          ) : (
            <NavigationAssets.NavigationPeopleNearbyIcon style={{ opacity: 0.6 }} />
          )
        }
      />
      <NavigationItem
        href={settingsRoute}
        isActive={isActive(settingsRoute)}
        icon={
          isActive(settingsRoute) ? (
            <NavigationAssets.NavigationSettingsIcon />
          ) : (
            <NavigationAssets.NavigationSettingsOutlineIcon />
          )
        }
      />
    </NavigationContainer>
  );
};
