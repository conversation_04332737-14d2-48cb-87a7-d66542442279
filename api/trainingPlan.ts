import {
  ErrorDeleteTrainingPlan,
  ErrorGetTrainingPlan,
  ErrorPostTrainingPlan,
  ErrorPutTrainingPlan,
} from "@/errors";
import axiosInstance from "@/middleware/axiosInstance";
import {
  DeleteTrainingPlanResponse,
  NewTrainingPlanRequestPayload,
  NewTrainingPlanResponse,
} from "@/types/trainingPlan";

export const getAllTrainingPlan = async (): Promise<
  NewTrainingPlanResponse[]
> => {
  try {
    const response = await axiosInstance.get(`/training-plan`);
    return response.data;
  } catch (error) {
    throw new ErrorGetTrainingPlan(error);
  }
};

export const postNewTrainingPlan = async (
  payload: NewTrainingPlanRequestPayload
): Promise<NewTrainingPlanResponse> => {
  try {
    const response = await axiosInstance.post("/training-plan", payload);
    return response.data;
  } catch (error) {
    throw new ErrorPostTrainingPlan(error);
  }
};

export const putNewTrainingPlan = async (
  payload: NewTrainingPlanRequestPayload,
  id: string
): Promise<NewTrainingPlanResponse> => {
  try {
    const response = await axiosInstance.put(`/training-plan/${id}`, payload);
    return response.data;
  } catch (error) {
    throw new ErrorPutTrainingPlan(error);
  }
};

export const deleteTrainingPlan = async (
  id: string
): Promise<DeleteTrainingPlanResponse> => {
  try {
    const response = await axiosInstance.delete(`/training-plan/${id}`);
    return response.data;
  } catch (error) {
    throw new ErrorDeleteTrainingPlan(error);
  }
};
