import Link from "next/link";
import { LinkProps } from "next/link";

interface Props
  extends LinkProps,
    Omit<React.AnchorHTMLAttributes<HTMLAnchorElement>, "href"> {
  children?: React.ReactNode;
}

export const LinkGradient = ({ children, href, ...props }: Props) => {
  return (
    <Link
      href={href}
      className="item-gradient disabled:opacity-50 w-full whitespace-nowrap disabled:cursor-not-allowed text-white p-4 rounded-md text-center"
      {...props}
    >
      {children ?? "Click"}
    </Link>
  );
};
