import clsx from "clsx";
import { LoaderSpinnerSmall } from "../Loaders";
import { Sizes, buttonSizeClasses } from "../../utils/sizes";

interface Props extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children?: React.ReactNode;
  size?: Sizes;
  isLoading?: boolean;
}

export const ButtonBorder = ({ children, isLoading, size = Sizes.Medium, ...props }: Props) => {
  const dynamicClasses = clsx(buttonSizeClasses[size]);

  return (
    <button
      className={clsx(
        "px-6 rounded-lg bg-transparent border border-accent-300 text-grey-50",
        "disabled:bg-grey-500 disabled:border-grey-400 disabled:text-grey-300 disabled:cursor-not-allowed",
        dynamicClasses
      )}
      {...props}
    >
      {isLoading ? <LoaderSpinnerSmall /> : (children ?? "Click")}
    </button>
  );
};
