import React from "react";
import { ContainerModalDown, Theme, useTheme } from "@vs/kit-ui-expo";
import { StyleSheet, View } from "react-native";

interface Props {
  children: any;
  modalChildren: any;
}

export const ContainerBottomModalWrapper = ({
  children,
  modalChildren,
}: Props) => {
  const { theme } = useTheme();
  const styles = stylesConfig(theme);

  return (
    <>
      <View style={styles.view}>{children}</View>
      <ContainerModalDown style={styles.container}>
        {modalChildren}
      </ContainerModalDown>
    </>
  );
};

const stylesConfig = (theme: Theme) =>
  StyleSheet.create({
    view: {
      flex: 1,
      paddingBottom: 156,
    },
    container: {
      position: "absolute",
      bottom: 0,
      left: 0,
      right: 0,
      paddingTop: theme.spacing[6],
      paddingHorizontal: theme.spacing[4],
      paddingBottom: theme.spacing[24],
    },
  });
