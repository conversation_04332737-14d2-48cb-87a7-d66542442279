import request from 'supertest';
import { INestApplication } from '@nestjs/common';
import { CreateUserDto } from '@app/users/dto/create-user.dto';
import { LoginDto } from '@app/auth/dto/login.dto';
import { LoginResponseDto } from '@app/auth/dto/login-response.dto';
import { UserEntity } from '@app/users/entities/user.entity';
import { createAndGetOtcCode } from './otc';

export const registerAndAssertUser = async (app: INestApplication, payload: CreateUserDto): Promise<UserEntity> => {
  const code = await createAndGetOtcCode(app, payload.email);

  const response = await request(app.getHttpServer())
    .post('/v1/user/create')
    .send({ ...payload, authorization_code: code });

  expect(response.status).toBe(201);
  expect(response.body.id).toBeDefined();
  expect(response.body.email).toBe(payload.email);
  expect(response.body.name).toBe(payload.name);
  expect(response.body.roles).toEqual(['USER']);
  expect(response.body.createdAt).toBeDefined();

  return response.body as UserEntity;
};

export const loginAndAssertUser = async (app: INestApplication, payload: LoginDto): Promise<LoginResponseDto> => {
  const response = await request(app.getHttpServer()).post('/v1/user/login').send(payload);

  expect(response.status).toBe(200);
  expect(response.body.accessToken).toBeDefined();
  expect(response.body.refreshToken).toBeDefined();
  expect(response.body.expiresIn.accessToken).toBeDefined();
  expect(response.body.expiresIn.refreshToken).toBeDefined();
  expect(response.body.user).toBeDefined();

  return response.body as LoginResponseDto;
};

export const createExpiredSession = async (app: INestApplication, payload: LoginDto): Promise<LoginResponseDto> => {
  const response = await request(app.getHttpServer()).post('/v1/user/login').send(payload);

  expect(response.status).toBe(200);
  expect(response.body.accessToken).toBeDefined();
  expect(response.body.refreshToken).toBeDefined();
  expect(response.body.expiresIn.accessToken).toBeDefined();
  expect(response.body.expiresIn.refreshToken).toBeDefined();
  expect(response.body.user).toBeDefined();

  return response.body as LoginResponseDto;
};
