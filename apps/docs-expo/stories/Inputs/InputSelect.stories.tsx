import React, { useState } from "react";
import { View } from "react-native";
import type { Meta, StoryObj } from "@storybook/react";
import { InputSelect, SelectOption, ThemeProvider, Sizes } from "@vs/kit-ui-expo";

// Wrapper component to handle state for the InputSelect
const InputSelectWrapper = (props: React.ComponentProps<typeof InputSelect>) => {
  const [value, setValue] = useState(props.value || "");
  return <InputSelect {...props} value={value} onChangeText={setValue} />;
};

// Define sample options for the stories
const sampleOptions: SelectOption[] = [
  { label: "Łatwy", value: "easy" },
  { label: "Średni", value: "medium" },
  { label: "Trudny", value: "hard" },
  { label: "Bardzo trudny", value: "very-hard" },
];

const colorOptions: SelectOption[] = [
  { label: "Czerwony", value: "red" },
  { label: "<PERSON><PERSON><PERSON><PERSON>", value: "blue" },
  { label: "<PERSON><PERSON><PERSON>", value: "green" },
  { label: "<PERSON><PERSON><PERSON><PERSON>", value: "yellow" },
  { label: "<PERSON>oletowy", value: "purple" },
];

const longOptions: SelectOption[] = [
  { label: "Opcja pierwsza z bardzo długim tekstem", value: "option1" },
  { label: "Opcja druga", value: "option2" },
  { label: "Opcja trzecia z jeszcze dłuższym tekstem niż pierwsza", value: "option3" },
  { label: "Opcja czwarta", value: "option4" },
  { label: "Opcja piąta", value: "option5" },
  { label: "Opcja szósta", value: "option6" },
  { label: "Opcja siódma", value: "option7" },
  { label: "Opcja ósma", value: "option8" },
];

// Define a dummy onChangeText function for the stories
const dummyOnChangeText = () => {};

const meta = {
  title: "Components/Inputs/InputSelect",
  component: InputSelectWrapper,
  argTypes: {
    onChangeText: { action: "option selected" },
    size: {
      control: "select",
      options: [Sizes.Small, Sizes.Medium, Sizes.Large],
    },
    disabled: {
      control: "boolean",
    },
    label: {
      control: "text",
    },
    placeholder: {
      control: "text",
    },
    maxHeight: {
      control: "number",
    },
  },
  args: {
    placeholder: "Wybierz opcję...",
    size: Sizes.Medium,
    disabled: false,
    value: "",
    options: sampleOptions,
    maxHeight: 200,
    onChangeText: dummyOnChangeText,
  },
  decorators: [
    Story => (
      <ThemeProvider>
        <View style={{ padding: 16, width: "100%" }}>
          <Story />
        </View>
      </ThemeProvider>
    ),
  ],
} satisfies Meta<typeof InputSelectWrapper>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {};

export const WithLabel: Story = {
  args: {
    label: "Poziom trudności",
  },
};

export const Small: Story = {
  args: {
    size: Sizes.Small,
    label: "Small Select",
    options: colorOptions,
  },
};

export const Medium: Story = {
  args: {
    size: Sizes.Medium,
    label: "Medium Select",
    options: colorOptions,
  },
};

export const Large: Story = {
  args: {
    size: Sizes.Large,
    label: "Large Select",
    options: colorOptions,
  },
};

export const Disabled: Story = {
  args: {
    disabled: true,
    value: "medium",
    label: "Disabled Select",
  },
};

export const WithError: Story = {
  args: {
    error: "To pole jest wymagane",
    label: "Select z błędem",
  },
};

export const PreSelected: Story = {
  args: {
    value: "hard",
    label: "Wybrana opcja",
    placeholder: "Domyślnie wybrane...",
  },
};

export const LongOptions: Story = {
  args: {
    options: longOptions,
    label: "Długie opcje",
    placeholder: "Wybierz długą opcję...",
    maxHeight: 300,
  },
};

export const CustomStyle: Story = {
  args: {
    inputStyle: { backgroundColor: "#2a2a2a", borderColor: "#ff5722", borderWidth: 2 },
    labelStyle: { color: "#ff5722", fontWeight: "bold" },
    label: "Custom Styled Select",
  },
};

export const ManyOptions: Story = {
  args: {
    options: [
      ...sampleOptions,
      ...colorOptions,
      { label: "Dodatkowa opcja 1", value: "extra1" },
      { label: "Dodatkowa opcja 2", value: "extra2" },
      { label: "Dodatkowa opcja 3", value: "extra3" },
      { label: "Dodatkowa opcja 4", value: "extra4" },
      { label: "Dodatkowa opcja 5", value: "extra5" },
    ],
    label: "Wiele opcji",
    placeholder: "Scrollowalna lista...",
    maxHeight: 250,
  },
};
