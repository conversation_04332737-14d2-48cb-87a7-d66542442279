import {
  ErrorDeleteTraining,
  ErrorGetTraining,
  ErrorPostTraining,
  ErrorPutTraining,
} from "@/errors";
import axiosInstance from "@/middleware/axiosInstance";
import {
  DeleteTrainingResponse,
  NewTrainingRequestPayload,
  NewTrainingResponse,
} from "@/types/training";

export const getAllTrainings = async (): Promise<NewTrainingResponse[]> => {
  try {
    const response = await axiosInstance.get(`/training`);
    return response.data;
  } catch (error) {
    throw new ErrorGetTraining(error);
  }
};

export const postNewTraining = async (
  payload: NewTrainingRequestPayload
): Promise<NewTrainingResponse> => {
  try {
    const response = await axiosInstance.post("/training", payload);
    return response.data;
  } catch (error) {
    throw new ErrorPostTraining(error);
  }
};

export const putNewTraining = async (
  payload: NewTrainingRequestPayload,
  id: string
): Promise<NewTrainingResponse> => {
  try {
    const response = await axiosInstance.put(`/training/${id}`, payload);
    return response.data;
  } catch (error) {
    throw new ErrorPutTraining(error);
  }
};

export const deleteTraining = async (
  id: string
): Promise<DeleteTrainingResponse> => {
  try {
    const response = await axiosInstance.delete(`/training/${id}`);
    return response.data;
  } catch (error) {
    throw new ErrorDeleteTraining(error);
  }
};
