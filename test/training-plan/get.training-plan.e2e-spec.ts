import 'reflect-metadata';
import { INestApplication } from '@nestjs/common';
import request from 'supertest';

import { loginAndAssertUser, registerAndAssertUser } from '../__helpers/auth';
import { buildTestApp } from '../__setup/app.factory';
import { generateTestUser } from '../__helpers/user';

import { ConsentDefinitionEntity } from '@app/consents/entities/consent-definition.entity';
import { createAndAssertConsent, generateGDPRConsentPayload } from '../__helpers/consents';
import { createAndAssertTrainingPlan, deleteAndAssertTrainingPlan } from '../__helpers/training-plan';
import { CreateTrainingPlanRequestDto } from '@app/training-plan/dto/create-training-plan.dto';
import { randomUUID } from 'crypto';

describe('/GET /v1/training-plan', () => {
  let app: INestApplication;
  let accessToken: string;
  let consent: ConsentDefinitionEntity;

  beforeAll(async () => {
    app = await buildTestApp();

    const consentPayload = generateGDPRConsentPayload();
    consent = await createAndAssertConsent(app, consentPayload);

    const testUser = generateTestUser([consent.id]);
    await registerAndAssertUser(app, testUser);
    accessToken = (await loginAndAssertUser(app, testUser)).accessToken;
  });

  afterAll(async () => {
    await app.close();
  });

  it('should list training plans by current user', async () => {
    const payload: CreateTrainingPlanRequestDto = {
      name: 'Test Exercise E2E',
      description: 'This is a test exercise',
      trainings: [],
    };

    const trainingsPlanCount = 5;

    for (let i = 0; i < trainingsPlanCount; i++) {
      await createAndAssertTrainingPlan(app, payload, accessToken);
    }

    const response = await request(app.getHttpServer())
      .get('/v1/training-plan')
      .set('Authorization', `Bearer ${accessToken}`);

    expect(response.status).toBe(200);
    expect(response.body.length).toBe(trainingsPlanCount);
    expect(response.body[0].id).toBeDefined();
    expect(response.body[0].name).toBe(payload.name);
    expect(response.body[0].description).toBe(payload.description);
    expect(response.body[0].trainings).toEqual(payload.trainings);
    expect(response.body[0].status).toBe('active');
  });

  it('should filter trainings by status DELETED', async () => {
    const payload: CreateTrainingPlanRequestDto = {
      name: 'Test Exercise E2E',
      description: 'This is a test exercise',
      trainings: [],
    };

    const trainingsPlanCount = 5;

    for (let i = 0; i < trainingsPlanCount; i++) {
      await createAndAssertTrainingPlan(app, payload, accessToken);
    }

    const deletedTrainingPlan = await createAndAssertTrainingPlan(app, payload, accessToken);
    await deleteAndAssertTrainingPlan(app, deletedTrainingPlan.id, accessToken);

    const response = await request(app.getHttpServer())
      .get(`/v1/training-plan?status=deleted`)
      .set('Authorization', `Bearer ${accessToken}`);

    expect(response.status).toBe(200);
    expect(response.body.length).toBe(1);
    expect(response.body[0].id).toBeDefined();
    expect(response.body[0].name).toBe(payload.name);
    expect(response.body[0].description).toBe(payload.description);
    expect(response.body[0].status).toBe('deleted');
  });

  it('should return 401 if user is not authenticated', async () => {
    const payload: CreateTrainingPlanRequestDto = {
      name: 'Test Exercise E2E',
      description: 'This is a test exercise',
      trainings: [],
    };

    await createAndAssertTrainingPlan(app, payload, accessToken);

    const response = await request(app.getHttpServer()).get('/v1/training-plan');

    expect(response.status).toBe(401);
    expect(response.body.message).toBe('Unauthorized');
  });
});

describe('/GET /v1/training-plan/:id', () => {
  let app: INestApplication;
  let accessToken: string;
  let consent: ConsentDefinitionEntity;

  beforeAll(async () => {
    app = await buildTestApp();

    const consentPayload = generateGDPRConsentPayload();
    consent = await createAndAssertConsent(app, consentPayload);

    const testUser = generateTestUser([consent.id]);
    await registerAndAssertUser(app, testUser);
    accessToken = (await loginAndAssertUser(app, testUser)).accessToken;
  });

  afterAll(async () => {
    await app.close();
  });

  it('should return a single training', async () => {
    const payload: CreateTrainingPlanRequestDto = {
      name: 'Test Exercise E2E',
      description: 'This is a test exercise',
      trainings: [],
    };

    const trainingPlan = await createAndAssertTrainingPlan(app, payload, accessToken);

    const response = await request(app.getHttpServer())
      .get(`/v1/training-plan/${trainingPlan.id}`)
      .set('Authorization', `Bearer ${accessToken}`);

    expect(response.status).toBe(200);
    expect(response.body.id).toBe(trainingPlan.id);
    expect(response.body.name).toBe(payload.name);
    expect(response.body.description).toBe(payload.description);
    expect(response.body.status).toBe('active');
  });

  it('should return 403 if accessing training of another user', async () => {
    const payload: CreateTrainingPlanRequestDto = {
      name: 'Test Exercise E2E',
      description: 'This is a test exercise',
      trainings: [],
    };

    const anotherUser = generateTestUser([consent.id]);

    await registerAndAssertUser(app, anotherUser);
    const anotherUserAccessToken = (await loginAndAssertUser(app, anotherUser)).accessToken;

    const trainingPlan = await createAndAssertTrainingPlan(app, payload, accessToken);

    const response = await request(app.getHttpServer())
      .get(`/v1/training-plan/${trainingPlan.id}`)
      .set('Authorization', `Bearer ${anotherUserAccessToken}`);

    expect(response.status).toBe(403);
    expect(response.body.message).toBe('You do not have permission to access this training plan');
    expect(response.body.error).toBe('Forbidden');
    expect(response.body.statusCode).toBe(403);
  });

  it('should return 404 if training plan does not exist', async () => {
    const id = randomUUID();

    const response = await request(app.getHttpServer())
      .get(`/v1/training-plan/${id}`)
      .set('Authorization', `Bearer ${accessToken}`);

    expect(response.status).toBe(404);
    expect(response.body.message).toBe('Training plan with ID ' + id + ' not found');
    expect(response.body.error).toBe('Not Found');
    expect(response.body.statusCode).toBe(404);
  });
});
