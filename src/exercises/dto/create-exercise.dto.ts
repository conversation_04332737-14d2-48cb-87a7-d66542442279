import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsEnum, IsUrl, IsNotEmpty } from 'class-validator';
import { DifficultyLevel } from '../enums/difficulty-level.enum';
import { ExerciseStatus } from '../enums/exercise-status.enum';
import { UserEntity } from '@app/users/entities/user.entity';

export class CreateExerciseRequestDto {
  @ApiProperty({
    description: 'Exercise name',
    example: '10 pompek',
  })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Exercise description',
    example: 'Zrób 10 pompek nie badź ...',
  })
  @IsNotEmpty()
  @IsString()
  description: string;

  @ApiProperty({
    description: 'Exercise tags (separated by |)',
    example: 'biceps|triceps|klatka-piersiowa|nogi',
    required: false,
  })
  @IsOptional()
  @IsString()
  tags?: string;

  @ApiProperty({
    description: 'Exercise difficulty level',
    enum: DifficultyLevel,
    enumName: 'DifficultyLevel',
    example: DifficultyLevel.MEDIUM,
    required: false,
  })
  @IsOptional()
  @IsEnum(DifficultyLevel)
  difficultyLevel?: DifficultyLevel;

  @ApiProperty({
    description: 'Exercise video URL',
    example: 'https://s3.amazon.com/video.mp4',
    required: false,
  })
  @IsOptional()
  @IsUrl()
  videoUrl?: string;

  @ApiProperty({
    description: 'Exercise image URL',
    example: 'https://image.com/image.jpg',
    required: false,
  })
  @IsOptional()
  @IsUrl()
  imageUrl?: string;

  @ApiProperty({
    description: 'Instructions for performing the exercise',
    example: '1. Stand straight with feet shoulder-width apart. 2. Bend your knees...',
    required: true,
  })
  @IsOptional()
  @IsString()
  instructions: string;
}

export class CreateExerciseResponseDto {
  @ApiProperty({
    description: 'Unique exercise identifier',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Exercise name',
    example: '10 pompek',
  })
  name: string;

  @ApiProperty({
    description: 'Exercise description',
    example: 'Zrób 10 pompek nie badź ...',
  })
  description: string;

  @ApiProperty({
    description: 'Exercise tags',
    example: 'biceps|triceps|klatka-piersiowa|nogi',
  })
  tags: string;

  @ApiProperty({
    description: 'Exercise difficulty level',
    enum: DifficultyLevel,
    example: DifficultyLevel.MEDIUM,
  })
  difficultyLevel: DifficultyLevel;

  @ApiProperty({
    description: 'Exercise video URL',
    example: 'https://s3.amazon.com/video.mp4',
    required: false,
  })
  videoUrl: string;

  @ApiProperty({
    description: 'Exercise image URL',
    example: 'https://image.com/image.jpg',
    required: false,
  })
  imageUrl: string;

  @ApiProperty({
    description: 'Exercise status',
    enum: ExerciseStatus,
    example: ExerciseStatus.PENDING,
  })
  status: ExerciseStatus;

  @ApiProperty({
    description: 'Status message (e.g. rejection reason)',
    example: 'Treść ćwiczenia zawiera nieodpowiednie słownictwo',
    required: false,
  })
  statusMessage: string;

  @ApiProperty({
    description: 'Exercise creation timestamp',
    example: '2023-01-01T12:00:00Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Exercise last update timestamp',
    example: '2023-01-01T12:00:00Z',
  })
  updatedAt: Date;

  @ApiProperty({
    description: 'Creator of the exercise',
    type: () => UserEntity,
  })
  creator: UserEntity;

  @ApiProperty({
    description: 'Creator of the exercise',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  creatorId: string;

  @ApiProperty({
    description: 'Instructions for performing the exercise',
    example: '1. Stand straight with feet shoulder-width apart. 2. Bend your knees...',
    required: true,
  })
  instructions: string;
}
