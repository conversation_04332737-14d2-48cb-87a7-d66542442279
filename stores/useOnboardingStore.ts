// stores/useOnboardingStore.ts
import { create } from "zustand";
import { devtools, persist, createJSONStorage } from "zustand/middleware";
import AsyncStorage from "@react-native-async-storage/async-storage";

interface State {
  onboardedUsers: string[];
  hasUserCompletedOnboarding: (userId: string) => boolean;
  setOnboardingComplete: (userId: string) => void;
  resetOnboarding: () => void;
}

export const useOnboardingStore = create<State>()(
  devtools(
    persist(
      (set, get) => ({
        onboardedUsers: [],
        hasUserCompletedOnboarding: (userId: string) => {
          return get().onboardedUsers.includes(userId);
        },
        setOnboardingComplete: (userId: string) => {
          const users = get().onboardedUsers;
          if (!users.includes(userId)) {
            set({ onboardedUsers: [...users, userId] });
          }
        },
        resetOnboarding: () => {
          set({ onboardedUsers: [] });
        },
      }),
      {
        name: "OnboardingStore",
        storage: createJSONStorage(() => AsyncStorage),
      }
    )
  )
);
