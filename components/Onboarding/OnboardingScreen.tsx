import { View, StyleSheet } from "react-native";
import { theme } from "@vs/kit-ui-expo";
import { DotsIndicator } from "./DotsIndicator";
import { ButtonSection } from "./ButtonSection";
import { ContentSection } from "./ContentSection";
import Swiper from "react-native-swiper";
import React from "react";

interface ItemProps {
  title: string;
  description: string;
  image: any;
  imageSize?: { width: number; height: number };
}

interface Props {
  items: ItemProps[];
  onComplete: () => void;
  onSkip: () => void;
}

export const OnboardingScreen = ({ items, onComplete, onSkip }: Props) => {
  const [currentIndex, setCurrentIndex] = React.useState(0);
  const swiperRef = React.createRef<Swiper>();

  const handleIndexChanged = (index: number) => {
    setCurrentIndex(index);
  };

  const handleNext = () => {
    if (currentIndex < items.length - 1) {
      swiperRef.current?.scrollBy(1);
    } else {
      onSkip();
      onComplete();
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.swiperWrapper}>
        <Swiper
          ref={swiperRef}
          loop={false}
          showsPagination={false}
          onIndexChanged={handleIndexChanged}
          horizontal={true}
          bounces={true}
          removeClippedSubviews={false}
          scrollEnabled={true}
          loadMinimal={true}
        >
          {items.map((item, index) => (
            <View style={styles.slide} key={index}>
              <ContentSection
                title={item.title}
                description={item.description}
                image={item.image}
                imageSize={item.imageSize}
                screenNumber={index + 1}
              />
            </View>
          ))}
        </Swiper>
      </View>

      <DotsIndicator
        currentStep={currentIndex}
        totalSteps={items.length}
        onDotPress={(index) => {
          swiperRef.current?.scrollTo(index);
        }}
      />

      <ButtonSection
        isLast={currentIndex === items.length - 1}
        onNext={handleNext}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.grey[900],
    width: "100%",
  },
  swiperWrapper: {
    flex: 1,
    width: "100%",
    justifyContent: "center",
    alignItems: "center",
    overflow: "hidden",
  },
  slide: {
    flex: 1,
    width: "100%",
    justifyContent: "center",
    alignItems: "center",
  },
});
