import React from "react";
import { View } from "react-native";
import type { Meta, StoryObj } from "@storybook/react";
import { BorderBoxWithGradient, ThemeProvider } from "@vs/kit-ui-expo";

const meta = {
  title: "Components/Containers/BorderBoxWithGradient",
  component: BorderBoxWithGradient,
  argTypes: {},
  args: {
    children: "Container Tab",
  },
  decorators: [
    Story => (
      <ThemeProvider>
        <View style={{ padding: 16 }}>
          <Story />
        </View>
      </ThemeProvider>
    ),
  ],
} satisfies Meta<typeof BorderBoxWithGradient>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {};

export const WithoutGradient: Story = {
  args: {
    useGradient: false,
  },
};

export const WithDefaultGradient: Story = {
  args: {
    useGradient: true,
  },
};

export const WithGradientAndCustomColors: Story = {
  args: {
    useGradient: true,
    gradientColors: ["red", "green", "blue"],
  },
};

export const WithGradientAndCustomStartAndEnd: Story = {
  args: {
    useGradient: true,
    gradientColors: ["red", "green", "blue"],
    gradientStart: { x: 1, y: 0 },
    gradientEnd: { x: 1, y: 1 },
  },
};

export const WithCustomStyleProps: Story = {
  args: {
    style: { backgroundColor: "#ff5722", borderWidth: 1, borderRadius: 25, padding: 16, borderColor: "orange" },
  },
};

export const MultipleContainers: Story = {
  render: () => (
    <View>
      <BorderBoxWithGradient style={{ marginBottom: 10 }}>First Tab</BorderBoxWithGradient>
      <BorderBoxWithGradient style={{ marginBottom: 10 }}>Second Tab</BorderBoxWithGradient>
      <BorderBoxWithGradient style={{ marginBottom: 10 }}>Third Tab</BorderBoxWithGradient>
    </View>
  ),
};
