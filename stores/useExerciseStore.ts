import { NewExerciseResponse } from "@/types/exercise";
import { create } from "zustand";
import { devtools } from "zustand/middleware";

interface State {
  exercises: NewExerciseResponse[];
  setExercises: (exercises: NewExerciseResponse[]) => void;
  getExerciseById: (id: string) => NewExerciseResponse | undefined;
  getExerciseListByIdArray: (ids: string[]) => NewExerciseResponse[];
}

export const useExerciseStore = create<State>()(
  devtools(
    (set, get) => ({
      exercises: [],

      setExercises: async (exercises) => {
        try {
          set({ exercises });
        } catch (error) {
          throw error;
        }
      },
      getExerciseById: (id) => {
        return get().exercises.find((exercise) => exercise.id === id);
      },
      getExerciseListByIdArray: (ids) => {
        return get().exercises.filter((exercise) => ids.includes(exercise.id));
      },
    }),
    { name: "ExerciseStore" }
  )
);
