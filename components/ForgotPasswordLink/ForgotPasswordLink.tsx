import { Typography, useTheme } from "@vs/kit-ui-expo";
import { TouchableOpacity, View } from "react-native";
import { StyleSheet } from "react-native";
import { useTranslation } from "react-i18next";

export const ForgotPasswordLink = () => {
  const { theme } = useTheme();
  const { t } = useTranslation();

  return (
    <View style={styles.forgotPasswordContainer}>
      <Typography variant="BodyM" color={theme.colors.grey[50]}>
        {t("forgot_password_link_title")}
      </Typography>
      <TouchableOpacity>
        <Typography
          variant="BodyM"
          color={theme.colors.grey[50]}
          style={styles.resetLink}
        >
          {t("forgot_password_link_callback")}
        </Typography>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  forgotPasswordContainer: {
    alignItems: "center",
    justifyContent: "center",
    flexDirection: "row",
    gap: 4,
  },
  resetLink: {
    textDecorationLine: "underline",
  },
});
