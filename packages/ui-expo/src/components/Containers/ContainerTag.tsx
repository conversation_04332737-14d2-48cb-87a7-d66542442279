import React from "react";
import { View, Text, StyleSheet, StyleProp, ViewStyle, TextStyle } from "react-native";
import { useTheme } from "../../theme";

interface Props {
  children?: React.ReactNode;
  backgroundColor?: string;
  textColor?: string;
  style?: StyleProp<ViewStyle>;
  textStyle?: StyleProp<TextStyle>;
}

export const ContainerTag: React.FC<Props> = ({ children, backgroundColor, textColor, style, textStyle }) => {
  const { theme } = useTheme();
  const { colors, borderRadius, typography, spacing } = theme;

  const styles = StyleSheet.create({
    container: {
      padding: spacing[2],
      alignItems: "center",
      justifyContent: "center",
    },
    text: {
      ...(typography.BodyXS as TextStyle),
    },
  });

  return (
    <View
      style={[
        styles.container,
        {
          backgroundColor: backgroundColor || colors.accent[300],
          borderBottomLeftRadius: borderRadius.lg,
          borderBottomRightRadius: borderRadius.lg,
        },
        style,
      ]}
    >
      {typeof children === "string" ? (
        <Text
          style={[
            styles.text,
            {
              color: textColor || colors.grey[600],
            },
            textStyle,
          ]}
        >
          {children}
        </Text>
      ) : (
        children
      )}
    </View>
  );
};
