import 'reflect-metadata';
import { INestApplication } from '@nestjs/common';
import request from 'supertest';

import { loginAndAssertUser, registerAndAssertUser } from '../__helpers/auth';
import { buildTestApp } from '../__setup/app.factory';
import { generateTestUser } from '../__helpers/user';
import { CreateTrainingRequestDto } from '@app/training/dto/create-training.dto';
import { createAndAssertTraining } from '../__helpers/training';
import { ConsentDefinitionEntity } from '@app/consents/entities/consent-definition.entity';
import { createAndAssertConsent, generateGDPRConsentPayload } from '../__helpers/consents';

describe('/DELETE /v1/training/:id', () => {
  let app: INestApplication;
  let accessToken: string;
  let consent: ConsentDefinitionEntity;

  beforeAll(async () => {
    app = await buildTestApp();

    const consentPayload = generateGDPRConsentPayload();
    consent = await createAndAssertConsent(app, consentPayload);

    const testUser = generateTestUser([consent.id]);
    await registerAndAssertUser(app, testUser);
    accessToken = (await loginAndAssertUser(app, testUser)).accessToken;
  });

  afterAll(async () => {
    await app.close();
  });

  it('should soft delete a training', async () => {
    const payload: CreateTrainingRequestDto = {
      name: 'Test training name',
      description: 'Test training description',
      exercises: [],
      tags: '',
    };

    const training = await createAndAssertTraining(app, payload, accessToken);

    const response = await request(app.getHttpServer())
      .delete(`/v1/training/${training.id}`)
      .set('Authorization', `Bearer ${accessToken}`);

    expect(response.status).toBe(200);
    expect(response.body.id).toBe(training.id);
    expect(response.body.status).toBe('deleted');
  });

  it('should return 401 if user is not authenticated', async () => {
    const payload: CreateTrainingRequestDto = {
      name: 'Test training name',
      description: 'Test training description',
      exercises: [],
      tags: '',
    };

    const training = await createAndAssertTraining(app, payload, accessToken);

    const response = await request(app.getHttpServer())
      .delete(`/v1/training/${training.id}`)
      .set('Authorization', `Bearer ${accessToken}`);

    expect(response.status).toBe(200);
    expect(response.body.id).toBe(training.id);
    expect(response.body.status).toBe('deleted');
  });
});
