import React, { useState } from "react";
import { View } from "react-native";
import type { Meta, StoryObj } from "@storybook/react";
import { InputPin, ThemeProvider } from "@vs/kit-ui-expo";

// Wrapper component to handle state for the InputPin
const InputPinWrapper = (props: React.ComponentProps<typeof InputPin>) => {
  const [value, setValue] = useState(props.value || "");
  return <InputPin {...props} value={value} onChange={setValue} />;
};

// Define a dummy onChange function for the stories
const dummyOnChange = () => {};

const meta = {
  title: "Components/Inputs/InputPin",
  component: InputPinWrapper,
  argTypes: {
    onChange: { action: "pin changed" },
    length: {
      control: { type: "number", min: 3, max: 8 },
    },
    disabled: {
      control: "boolean",
    },
    label: {
      control: "text",
    },
    error: {
      control: "text",
    },
  },
  args: {
    length: 4,
    disabled: false,
    value: "",
    onChange: dummyOnChange,
  },
  decorators: [
    Story => (
      <ThemeProvider>
        <View style={{ padding: 16, width: 600, alignItems: "center", justifyContent: "center" }}>
          <Story />
        </View>
      </ThemeProvider>
    ),
  ],
} satisfies Meta<typeof InputPinWrapper>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {};

export const WithLabel: Story = {
  args: {
    label: "Wprowadź PIN",
  },
};

export const WithError: Story = {
  args: {
    label: "Wprowadź PIN",
    error: "Nieprawidłowy PIN",
  },
};

export const CustomLength: Story = {
  args: {
    length: 6,
    label: "6-cyfrowy PIN",
  },
};

export const WithValue: Story = {
  args: {
    value: "1234",
    label: "PIN z wartością",
  },
};

export const Disabled: Story = {
  args: {
    disabled: true,
    value: "1234",
    label: "Zablokowany PIN",
  },
};

export const CustomStyle: Story = {
  args: {
    inputStyle: {
      backgroundColor: "#2a2a2a",
      borderColor: "#ff5722",
      borderWidth: 2,
      color: "#ffffff",
      width: 60,
      height: 60,
      borderRadius: 30,
    },
    labelStyle: { color: "#ff5722", fontWeight: "bold" },
    label: "Niestandardowy styl PIN",
  },
};
