import { ErrorBase } from "./ErrorBase";
import { ErrorTypes } from "./types";

export class ErrorGetExercise extends ErrorBase {
  constructor(error?: unknown) {
    super(ErrorTypes.GET_EXERCISE, error);
  }
}

export class <PERSON>rrorPostExercise extends ErrorBase {
  constructor(error?: unknown) {
    super(ErrorTypes.POST_EXERCISE, error);
  }
}

export class ErrorPutExercise extends E<PERSON>rBase {
  constructor(error?: unknown) {
    super(ErrorTypes.PUT_EXERCISE, error);
  }
}

export class ErrorDeleteExercise extends ErrorBase {
  constructor(error?: unknown) {
    super(ErrorTypes.DELETE_EXERCISE, error);
  }
}
