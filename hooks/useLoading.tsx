import { useState } from "react";

interface Props {
  init?: boolean;
}

export const useLoading = ({ init = true }: Props | undefined = {}) => {
  const [loading, setLoading] = useState(init);

  const startLoading = () => setLoading(true);
  const stopLoading = () => setLoading(false);

  const withLoading = async <T,>(
    operation: () => Promise<T>,
    extraOnFinally?: () => void
  ): Promise<T | undefined> => {
    startLoading();
    try {
      return await operation();
    } finally {
      stopLoading();
      if (extraOnFinally) {
        extraOnFinally();
      }
    }
  };

  return {
    loading,
    withLoading,
    startLoading,
    stopLoading,
  };
};
