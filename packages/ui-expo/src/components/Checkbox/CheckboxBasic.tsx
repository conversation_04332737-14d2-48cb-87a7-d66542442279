import React from "react";
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import { useTheme } from "../../theme";

interface Props {
  label?: string;
  checked?: boolean;
  disabled?: boolean;
  size?: "small" | "medium" | "large";
  onPress?: () => void;
  onLabelPress?: () => void;
  variant?: "outline" | "filled";
}

export const CheckboxBasic = ({
  label,
  checked = false,
  onPress,
  onLabelPress,
  disabled = false,
  size = "medium",
  variant = "outline",
}: Props) => {
  const { theme } = useTheme();
  const { colors } = theme;

  const activeColor = colors.accent[300];
  const disabledColor = colors.grey[300]; // Darker grey for disabled state

  const getSizes = () => {
    switch (size) {
      case "small":
        return {
          outer: 20,
          inner: checked ? 8 : 0,
          border: 2,
        };
      case "large":
        return {
          outer: 36,
          inner: checked ? 20 : 0,
          border: 2,
        };
      default: // medium
        return {
          outer: 28,
          inner: checked ? 16 : 0,
          border: 2,
        };
    }
  };

  const { outer: outerSize, inner: innerSize, border: borderWidth } = getSizes();
  const borderRadius = 4;

  const styles = StyleSheet.create({
    container: {
      flexDirection: "row",
      alignItems: "center",
    },
    radioContainer: {
      width: outerSize,
      height: outerSize,
      marginRight: 8,
      borderRadius: borderRadius,
      borderWidth: borderWidth,
      borderColor: disabled ? disabledColor : activeColor,
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: "transparent",
    },
    filledRadioContainer: {
      width: outerSize,
      height: outerSize,
      marginRight: 8,
      borderRadius: borderRadius,
      borderWidth: borderWidth,
      borderColor: disabled ? disabledColor : activeColor,
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: variant === "filled" && checked ? (disabled ? disabledColor : activeColor) : "transparent",
    },
    innerDot: {
      width: innerSize,
      height: innerSize,
      borderRadius: borderRadius / 2,
      backgroundColor: disabled ? disabledColor : activeColor,
    },
    filledInnerDot: {
      width: innerSize,
      height: innerSize,
      borderRadius: borderRadius / 2,
      backgroundColor: "white",
    },
    label: {
      color: disabled ? disabledColor : colors.grey[50],
      flexShrink: 1,
      maxWidth: "100%",
    },
  });

  return (
    <View style={styles.container}>
      <TouchableOpacity
        onPress={onPress}
        disabled={disabled}
        activeOpacity={0.8}
        accessibilityRole="radio"
        accessibilityState={{ checked, disabled }}
        accessibilityLabel={label}
      >
        {variant === "outline" ? (
          <View style={styles.radioContainer}>{checked && <View style={styles.innerDot} />}</View>
        ) : (
          <View style={styles.filledRadioContainer}>{checked && <View style={styles.filledInnerDot} />}</View>
        )}
      </TouchableOpacity>

      {label ? (
        <>
          {onLabelPress ? (
            <TouchableOpacity
              onPress={onLabelPress}
              activeOpacity={0.8}
              accessibilityRole="button"
              accessibilityLabel={label}
            >
              <Text style={styles.label}>{label}</Text>
            </TouchableOpacity>
          ) : (
            <Text style={styles.label}>{label}</Text>
          )}
        </>
      ) : null}
    </View>
  );
};
