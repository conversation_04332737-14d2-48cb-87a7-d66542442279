import { Test, TestingModule } from '@nestjs/testing';
import { TrainingPlanController } from './training-plan.controller';
import { TrainingPlanService } from '../services/training-plan.service';
import { CreateTrainingPlanRequestDto } from '../dto/create-training-plan.dto';
import { UpdateTrainingPlanRequestDto } from '../dto/update-training-plan.dto';
import { TrainingPlanStatus } from '../enums/training-plan-status.enum';
import { UserEntity } from '@app/users/entities/user.entity';
import { TrainingPlanEntity } from '../entities/training-plan.entity';
import { TrainingEntity } from '@app/training/entities/training.entity';
import { EUserRole } from '@app/constants/models/user-role.enum';
import { TrainingStatus } from '@app/training/enums/training-status.enum';
import { BadRequestException, NotFoundException } from '@nestjs/common';
import { AssignTrainingPlanToGroupDto } from '../dto/assign-training-plan-to-group.dto';
import { TrainingPlanGroupResponseDto } from '../dto/training-plan-group-response.dto';

describe('TrainingPlanController', () => {
  let controller: TrainingPlanController;
  let service: TrainingPlanService;

  const mockUser: UserEntity = {
    id: 'user-id',
    email: '<EMAIL>',
    name: 'Test User',
    password: 'hashedPassword',
    consents: ['RODO'] as any,
    roles: [EUserRole.TRAINER],
    createdAt: new Date(),
    refreshTokens: [],
    hashPassword: jest.fn(),
  };

  const mockTraining: TrainingEntity = {
    id: 'training_id_1',
    name: 'Trening na biceps',
    description: 'Trening opis super ciało w 2godziny',
    tags: 'na_cale_cialo|i_na_nogi',
    status: TrainingStatus.ACTIVE,
    creator: mockUser,
    creatorId: 'user-id',
    exercises: [],
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockTrainingPlan: TrainingPlanEntity = {
    id: 'training_plan_id',
    name: 'Plan na tydzień',
    description: 'Super plan treningowy',
    trainings: [mockTraining],
    status: TrainingPlanStatus.ACTIVE,
    creator: mockUser,
    creatorId: 'user-id',
    createdAt: new Date(),
    updatedAt: new Date(),
    trainingPlanGroups: [],
  };

  const mockTrainingPlanGroupResponse: TrainingPlanGroupResponseDto = {
    id: 'training_plan_group_id',
    trainingPlanId: 'training_plan_id',
    groupId: 'group_id',
    startDate: '2025-05-06',
    createdAt: new Date(),
  };

  const mockTrainingPlanService = {
    createTrainingPlan: jest.fn(),
    findAll: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
    assignToGroup: jest.fn(),
    unassignFromGroup: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [TrainingPlanController],
      providers: [
        {
          provide: TrainingPlanService,
          useValue: mockTrainingPlanService,
        },
      ],
    }).compile();

    controller = module.get<TrainingPlanController>(TrainingPlanController);
    service = module.get<TrainingPlanService>(TrainingPlanService);

    // Reset all mocks before each test
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create', () => {
    it('should create a training plan', async () => {
      const createTrainingPlanDto: CreateTrainingPlanRequestDto = {
        name: 'Plan na tydzień',
        description: 'Super plan treningowy',
        trainings: ['training_id_1', 'training_id_2'],
      };

      mockTrainingPlanService.createTrainingPlan.mockResolvedValue(mockTrainingPlan);

      const result = await controller.create(createTrainingPlanDto, mockUser);

      expect(service.createTrainingPlan).toHaveBeenCalledWith(createTrainingPlanDto, mockUser);
      expect(result).toEqual(mockTrainingPlan);
    });
  });

  describe('findAll', () => {
    it('should return all training plans', async () => {
      mockTrainingPlanService.findAll.mockResolvedValue([mockTrainingPlan]);

      const result = await controller.findAll(TrainingPlanStatus.ACTIVE, mockUser);

      expect(service.findAll).toHaveBeenCalledWith(mockUser, TrainingPlanStatus.ACTIVE);
      expect(result).toEqual([mockTrainingPlan]);
    });

    it('should return all training plans without status filter', async () => {
      mockTrainingPlanService.findAll.mockResolvedValue([mockTrainingPlan]);

      const result = await controller.findAll(undefined, mockUser);

      expect(service.findAll).toHaveBeenCalledWith(mockUser, undefined);
      expect(result).toEqual([mockTrainingPlan]);
    });
  });

  describe('findOne', () => {
    it('should return a training plan by id', async () => {
      mockTrainingPlanService.findOne.mockResolvedValue(mockTrainingPlan);

      const result = await controller.findOne('training_plan_id', mockUser);

      expect(service.findOne).toHaveBeenCalledWith('training_plan_id', mockUser);
      expect(result).toEqual(mockTrainingPlan);
    });
  });

  describe('update', () => {
    it('should update a training plan', async () => {
      const updateTrainingPlanDto: UpdateTrainingPlanRequestDto = {
        name: 'Updated Training Plan Name',
      };

      const updatedResponse = {
        ...mockTrainingPlan,
        name: 'Updated Training Plan Name',
      };

      mockTrainingPlanService.update.mockResolvedValue(updatedResponse);

      const result = await controller.update('training_plan_id', updateTrainingPlanDto, mockUser);

      expect(service.update).toHaveBeenCalledWith('training_plan_id', updateTrainingPlanDto, mockUser);
      expect(result.name).toBe('Updated Training Plan Name');
    });

    it('should update a training plan with multiple fields', async () => {
      const updateTrainingPlanDto: UpdateTrainingPlanRequestDto = {
        name: 'Updated Training Plan Name',
        description: 'Updated description',
        trainings: ['training_id_1', 'training_id_3'],
      };

      const updatedResponse = {
        ...mockTrainingPlan,
        name: 'Updated Training Plan Name',
        description: 'Updated description',
        trainings: ['training_id_1', 'training_id_3'],
      };

      mockTrainingPlanService.update.mockResolvedValue(updatedResponse);

      const result = await controller.update('training_plan_id', updateTrainingPlanDto, mockUser);

      expect(service.update).toHaveBeenCalledWith('training_plan_id', updateTrainingPlanDto, mockUser);
      expect(result).toEqual(updatedResponse);
    });
  });

  describe('remove', () => {
    it('should remove a training plan', async () => {
      mockTrainingPlanService.remove.mockResolvedValue({ status: TrainingPlanStatus.DELETED });

      const result = await controller.remove('training_plan_id', mockUser);

      expect(service.remove).toHaveBeenCalledWith('training_plan_id', mockUser);
      expect(result).toEqual({ status: TrainingPlanStatus.DELETED });
    });
  });

  describe('assignToGroup', () => {
    it('should assign a training plan to a group', async () => {
      const assignDto: AssignTrainingPlanToGroupDto = {
        startDate: '2025-05-06',
      };

      mockTrainingPlanService.assignToGroup.mockResolvedValue(mockTrainingPlanGroupResponse);

      // Create a mock request object with user property
      const mockReq = { user: mockUser };

      const result = await controller.assignToGroup('training_plan_id', 'group_id', assignDto, mockReq);

      expect(service.assignToGroup).toHaveBeenCalledWith('training_plan_id', 'group_id', assignDto, mockUser);
      expect(result).toEqual(mockTrainingPlanGroupResponse);
    });

    it('should handle invalid date format in assignToGroup', async () => {
      const assignDto: AssignTrainingPlanToGroupDto = {
        startDate: '2025-05-06',
      };

      mockTrainingPlanService.assignToGroup.mockRejectedValue(new Error('Invalid date format'));

      // Create a mock request object with user property
      const mockReq = { user: mockUser };

      await expect(controller.assignToGroup('training_plan_id', 'group_id', assignDto, mockReq)).rejects.toThrow();

      expect(service.assignToGroup).toHaveBeenCalledWith('training_plan_id', 'group_id', assignDto, mockUser);
    });

    it('should handle BadRequestException from service', async () => {
      const assignDto: AssignTrainingPlanToGroupDto = {
        startDate: '2025-05-06',
      };

      mockTrainingPlanService.assignToGroup.mockRejectedValue(new BadRequestException('Invalid data'));

      // Create a mock request object with user property
      const mockReq = { user: mockUser };

      await expect(controller.assignToGroup('training_plan_id', 'group_id', assignDto, mockReq)).rejects.toThrow(
        BadRequestException,
      );

      expect(service.assignToGroup).toHaveBeenCalledWith('training_plan_id', 'group_id', assignDto, mockUser);
    });
  });

  describe('unassignFromGroup', () => {
    it('should unassign a training plan from a group', async () => {
      mockTrainingPlanService.unassignFromGroup.mockResolvedValue(undefined);

      // Create a mock request object with user property
      const mockReq = { user: mockUser };

      await controller.unassignFromGroup('training_plan_id', 'group_id', mockReq);

      expect(service.unassignFromGroup).toHaveBeenCalledWith('training_plan_id', 'group_id', mockUser);
    });

    it('should handle errors when unassigning a training plan from a group', async () => {
      mockTrainingPlanService.unassignFromGroup.mockRejectedValue(new Error('Group not found'));

      // Create a mock request object with user property
      const mockReq = { user: mockUser };

      await expect(controller.unassignFromGroup('training_plan_id', 'group_id', mockReq)).rejects.toThrow();

      expect(service.unassignFromGroup).toHaveBeenCalledWith('training_plan_id', 'group_id', mockUser);
    });

    it('should handle NotFoundException when unassigning a training plan from a group', async () => {
      mockTrainingPlanService.unassignFromGroup.mockRejectedValue(new NotFoundException('Group not found'));

      // Create a mock request object with user property
      const mockReq = { user: mockUser };

      await expect(controller.unassignFromGroup('training_plan_id', 'group_id', mockReq)).rejects.toThrow(
        NotFoundException,
      );

      expect(service.unassignFromGroup).toHaveBeenCalledWith('training_plan_id', 'group_id', mockUser);
    });
  });
});
