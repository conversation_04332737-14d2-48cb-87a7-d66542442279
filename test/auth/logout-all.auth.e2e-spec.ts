import { ConsentDefinitionEntity } from '@app/consents/entities/consent-definition.entity';
import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { loginAndAssertUser, registerAndAssertUser } from '../__helpers/auth';
import { createAndAssertConsent, generateGDPRConsentPayload } from '../__helpers/consents';
import { generateTestUser } from '../__helpers/user';
import { buildTestApp } from '../__setup/app.factory';

describe('Auth/Logout All (e2e)', () => {
  let app: INestApplication;
  let consent: ConsentDefinitionEntity;

  beforeAll(async () => {
    app = await buildTestApp();

    const consentPayload = generateGDPRConsentPayload();
    consent = await createAndAssertConsent(app, consentPayload);
  });

  afterAll(async () => {
    await app.close();
  });

  describe('/POST /v1/user/logout-all', () => {
    it('should remove all refresh tokens for the user', async () => {
      const user = generateTestUser([consent.id]);
      await registerAndAssertUser(app, user);
      const session1 = await loginAndAssertUser(app, user);
      await loginAndAssertUser(app, user);

      const res = await request(app.getHttpServer())
        .post('/v1/user/logout-all')
        .set('Authorization', `Bearer ${session1.accessToken}`)
        .send();

      expect(res.status).toBe(200);
      expect(res.body).toEqual({ message: 'Logged out from 2 session(s).' });

      const refreshRes = await request(app.getHttpServer())
        .post('/v1/user/refresh')
        .set('Authorization', `Bearer ${session1.refreshToken}`)
        .send();

      expect(refreshRes.status).toBe(401);
    });

    it('should return 401 if access token is missing', async () => {
      const res = await request(app.getHttpServer()).post('/v1/user/logout-all').send();

      expect(res.status).toBe(401);
    });

    it('should return 401 if access token is invalid', async () => {
      const res = await request(app.getHttpServer())
        .post('/v1/user/logout-all')
        .set('Authorization', 'Bearer invalid.token')
        .send();

      expect(res.status).toBe(401);
    });
  });
});
