import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, ManyToOne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CreateDateColumn } from 'typeorm';
import { GroupEntity } from './group.entity';
import { UserEntity } from '@app/users/entities/user.entity';

@Entity({ name: 'group_users' })
export class GroupUserEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => GroupEntity, (group) => group.groupUsers, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'group_id' })
  group: GroupEntity;

  @ManyToOne(() => UserEntity, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: UserEntity;

  @CreateDateColumn()
  createdAt: Date;
}
