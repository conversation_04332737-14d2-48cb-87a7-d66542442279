import { useState, useEffect } from "react";
import { useTrainingStore } from "@/stores/useTrainingStore";

interface UseTrainingFormStateProps {
  id: string | string[] | undefined;
  reset: (values: any) => void;
}

export const useTrainingFormState = ({
  id,
  reset,
}: UseTrainingFormStateProps) => {
  const [isNewInstance, setIsNewInstance] = useState(true);
  const [isAddExercise, setIsAddExercise] = useState(false);
  const { getTrainingById } = useTrainingStore();

  useEffect(() => {
    if (id && typeof id === "string") {
      const training = getTrainingById(id);
      if (training) {
        setIsNewInstance(false);
        reset({
          name: training.name || "",
          description: training.description || "",
          tags: training.tags || "",
          trainingExercises: training.exercises?.map((item) => item.id) || [],
        });
      } else {
        setIsNewInstance(true);
      }
    }
  }, [id, getTrainingById, reset]);

  return {
    isNewInstance,
    isAddExercise,
    setIsAddExercise,
  };
};
