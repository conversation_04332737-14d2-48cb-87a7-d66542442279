import React from "react";
import { View, Text, StyleSheet } from "react-native";
import { ButtonBasic } from "@vs/kit-ui-expo";

interface Props {
  onPress: () => void;
}

export const Permission = ({ onPress }: Props) => {
  return (
    <View style={styles.container}>
      <Text style={styles.text}>Potrzebne są uprawnienia do kamery</Text>
      <ButtonBasic
        onPress={onPress}
        style={{ marginTop: 16, backgroundColor: "#3b82f6" }}
      >
        Nadaj uprawnienia
      </ButtonBasic>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 16,
    backgroundColor: "#111827",
  },
  text: {
    fontSize: 18,
    color: "#f9fafb",
    textAlign: "center",
    marginBottom: 16,
  },
});
