import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsArray, MinLength } from 'class-validator';
import { TrainingPlanStatus } from '../enums/training-plan-status.enum';
import { Exclude } from 'class-transformer';
import { UserEntity } from '@app/users/entities/user.entity';
import { TrainingEntity } from '@app/training/entities/training.entity';
import { TrainingPlanGroupEntity } from '../entities/training-plan-group.entity';

export class CreateTrainingPlanRequestDto {
  @ApiProperty({
    description: 'Training plan name',
    example: 'Plan na tydzień',
  })
  @MinLength(3)
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Training plan description',
    example: 'Super plan treningowy',
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(3)
  description: string;

  @ApiProperty({
    description: 'Training IDs to include in the training plan',
    example: ['training_id_1', 'training_id_2', 'training_id_3'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  trainings: string[];
}

export class CreateTrainingPlanResponseDto {
  @ApiProperty({
    description: 'Unique training plan identifier',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Training plan name',
    example: 'Plan na tydzień',
  })
  name: string;

  @ApiProperty({
    description: 'Training plan description',
    example: 'Super plan treningowy',
  })
  description: string;

  @ApiProperty({
    description: 'Training plan status',
    enum: TrainingPlanStatus,
    example: TrainingPlanStatus.ACTIVE,
  })
  status: TrainingPlanStatus;

  @ApiProperty({
    description: 'Training plan creation timestamp',
    example: '2023-01-01T12:00:00Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Training plan last update timestamp',
    example: '2023-01-01T12:00:00Z',
  })
  updatedAt: Date;

  @ApiProperty({
    description: 'Creator of the training plan',
    type: () => UserEntity,
  })
  @Exclude()
  creator: UserEntity;

  creatorId: string;

  @ApiProperty({
    description: 'Trainings included in the training plan',
    type: () => [TrainingEntity],
  })
  trainings: TrainingEntity[];

  @ApiProperty({
    description: 'Groups assigned to this training plan',
    type: () => [TrainingPlanGroupEntity],
  })
  trainingPlanGroups: TrainingPlanGroupEntity[];
}
