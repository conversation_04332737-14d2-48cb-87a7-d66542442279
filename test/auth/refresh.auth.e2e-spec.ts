import { ConsentDefinitionEntity } from '@app/consents/entities/consent-definition.entity';
import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { registerAndAssertUser, loginAndAssertUser } from '../__helpers/auth';
import { createAndAssertConsent, generateGDPRConsentPayload } from '../__helpers/consents';
import { generateTestUser } from '../__helpers/user';
import { buildTestApp } from '../__setup/app.factory';

describe('/POST /v1/user/refresh', () => {
  let app: INestApplication;
  let consent: ConsentDefinitionEntity;

  beforeAll(async () => {
    app = await buildTestApp();

    const consentPayload = generateGDPRConsentPayload();
    consent = await createAndAssertConsent(app, consentPayload);
  });

  afterAll(async () => {
    await app.close();
  });
  it('should return new access and refresh token for valid refresh token', async () => {
    const user = generateTestUser([consent.id]);
    await registerAndAssertUser(app, user);
    const session = await loginAndAssertUser(app, user);

    const response = await request(app.getHttpServer())
      .post('/v1/user/refresh')
      .set('Authorization', `Bearer ${session.refreshToken}`)
      .send();

    // TODO: I think we should return only accessToken and refreshToken
    expect(response.status).toBe(200);
    expect(response.body).toEqual({
      accessToken: expect.any(String),
      refreshToken: expect.any(String),
      expiresIn: {
        accessToken: expect.any(String),
        refreshToken: expect.any(String),
      },
      user: {
        id: expect.any(String),
        email: expect.any(String),
        roles: expect.any(Array),
        name: expect.any(String),
      },
    });
  });

  it('should return 401 if refresh token is invalid', async () => {
    const response = await request(app.getHttpServer())
      .post('/v1/user/refresh')
      .set('Authorization', 'Bearer invalid.token.value')
      .send();

    expect(response.status).toBe(401);
    expect(response.body.message).toBe('Unauthorized');
  });

  it('should return 401 if refresh token is missing', async () => {
    const response = await request(app.getHttpServer()).post('/v1/user/refresh').send();

    expect(response.status).toBe(401);
    expect(response.body.message).toBe('Unauthorized');
  });
});

describe('Expired refresh token', () => {
  it('should return 401 if refresh token has expired', async () => {
    process.env.JWT_REFRESH_TOKEN_EXPIRATION_S = '1';

    const appWithShortExpiry = await buildTestApp();

    const consentPayload = generateGDPRConsentPayload();
    const consent = await createAndAssertConsent(appWithShortExpiry, consentPayload);

    const user = generateTestUser([consent.id]);
    await registerAndAssertUser(appWithShortExpiry, user);
    const session = await loginAndAssertUser(appWithShortExpiry, user);

    await new Promise((res) => setTimeout(res, 1500));

    const response = await request(appWithShortExpiry.getHttpServer())
      .post('/v1/user/refresh')
      .set('Authorization', `Bearer ${session.refreshToken}`)
      .send();

    //TODO: return more detailed error message like "Refresh token has expired"
    expect(response.status).toBe(401);
    expect(response.body.message).toBe('Unauthorized');

    await appWithShortExpiry.close();
  });
});
