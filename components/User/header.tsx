import React from "react";
import { View, StyleSheet } from "react-native";
import { theme, Typography } from "@vs/kit-ui-expo";
import { USER_MOCK } from "@/mocks/user";
import { useUserStore } from "@/stores/useUserStore";

export default function UserHeader() {
  const { user } = useUserStore();

  const renderHeader = () => (
    <View style={styles.header}>
      <Typography
        variant="Heading3"
        color={theme.colors.grey[50]}
        style={{ marginTop: theme.spacing[2], marginRight: theme.spacing[2] }}
      >
        <PERSON><PERSON><PERSON><PERSON>, {user?.name || "Użytkowniku"}
      </Typography>
      {/* <FireIcon fill="#f79009" width={24} height={24} style={styles.icon} /> */}
    </View>
  );

  const renderSubheader = () => (
    <View style={styles.subHeader}>
      <Typography
        variant="BodyL"
        color={theme.colors.grey[50]}
        style={{ marginTop: theme.spacing[2] }}
      >
        To Twój {USER_MOCK.daysCounter} d<PERSON><PERSON> z VitaminStation!
      </Typography>
    </View>
  );

  if (!user) return null;
  return (
    <View style={styles.container}>
      {renderHeader()}
      {renderSubheader()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    paddingTop: 0,
    marginTop: 0,
  },
  image: {
    width: 74,
    height: 48,
    resizeMode: "contain",
  },
  header: {
    marginBottom: 4,
    alignItems: "center",
    justifyContent: "center",
    flexDirection: "row",
  },
  icon: {
    alignSelf: "center",
    justifyContent: "center",
  },
  subHeader: {
    marginBottom: 40,
  },
});
