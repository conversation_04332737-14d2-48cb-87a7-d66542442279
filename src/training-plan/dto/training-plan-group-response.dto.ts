import { ApiProperty } from '@nestjs/swagger';

export class TrainingPlanGroupResponseDto {
  @ApiProperty({
    description: 'Unique training plan group relationship identifier',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Training plan ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  trainingPlanId: string;

  @ApiProperty({
    description: 'Group ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  groupId: string;

  @ApiProperty({
    description: 'Start date of the training plan for the group',
    example: '2025-05-06',
  })
  startDate: string;

  @ApiProperty({
    description: 'Relationship creation timestamp',
    example: '2023-01-01T12:00:00Z',
  })
  createdAt: Date;
}
