import { Entity, PrimaryGeneratedColumn, Column, OneToMany, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { GroupUserEntity } from './group-user.entity';
import { TrainingPlanGroupEntity } from '@app/training-plan/entities/training-plan-group.entity';
import { BaseEntity } from 'typeorm';

@Entity({ name: 'groups' })
export class GroupEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column({ nullable: true })
  description?: string;

  @Column({ nullable: true })
  tags?: string;

  @Column()
  invitationUrl: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column()
  ownerId: string;

  @OneToMany(() => GroupUserEntity, (groupUser) => groupUser.group, { cascade: true })
  groupUsers: GroupUserEntity[];

  @OneToMany(() => TrainingPlanGroupEntity, (trainingPlanGroup) => trainingPlanGroup.group)
  trainingPlanGroups: TrainingPlanGroupEntity[];
}
