import { PostgreSqlContainer } from '@testcontainers/postgresql';

export const startDb = async () => {
  const container = await new PostgreSqlContainer('postgres:15')
    .withDatabase('testdb')
    .withUsername('test')
    .withPassword('test')
    .start();

  process.env.DB_HOST = container.getHost();
  process.env.DB_PORT = String(container.getPort());
  process.env.DB_USERNAME = container.getUsername();
  process.env.DB_PASSWORD = container.getPassword();
  process.env.DB_DATABASE = container.getDatabase();

  globalThis.__PG__ = container;
};

export const stopDb = async () => {
  await globalThis.__PG__?.stop();
};

export const dbConfig = () =>
  ({
    host: process.env.DB_HOST ?? 'localhost',
    port: Number(process.env.DB_PORT ?? '5432'),
    username: process.env.DB_USERNAME ?? 'test',
    password: process.env.DB_PASSWORD ?? 'test',
    database: process.env.DB_DATABASE ?? 'testdb',
    type: 'postgres',
  }) as const;
