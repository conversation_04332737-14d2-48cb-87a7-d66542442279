import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { UserEntity } from '../../users/entities/user.entity';
import { EUserRole } from '@app/constants/models/user-role.enum';

@Injectable()
export class TrainerGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const user = request.user as UserEntity;

    if (!user) {
      throw new ForbiddenException('User not found in request');
    }

    if (!user.roles.includes(EUserRole.TRAINER)) {
      throw new ForbiddenException('Only trainers can access this resource');
    }

    return true;
  }
}
