import { ConsentDefinitionEntity } from '@app/consents/entities/consent-definition.entity';
import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { loginAndAssertUser, registerAndAssertUser } from '../__helpers/auth';
import { createAndAssertConsent, generateGDPRConsentPayload } from '../__helpers/consents';
import { generateTestUser } from '../__helpers/user';
import { buildTestApp } from '../__setup/app.factory';

describe('Auth/Logout (e2e)', () => {
  let app: INestApplication;
  let consent: ConsentDefinitionEntity;

  beforeAll(async () => {
    app = await buildTestApp();

    const consentPayload = generateGDPRConsentPayload();
    consent = await createAndAssertConsent(app, consentPayload);
  });

  afterAll(async () => {
    await app.close();
  });

  describe('/POST /v1/user/logout', () => {
    it('should remove refresh token and return 200', async () => {
      const user = generateTestUser([consent.id]);
      await registerAndAssertUser(app, user);
      const session = await loginAndAssertUser(app, user);

      const res = await request(app.getHttpServer())
        .post('/v1/user/logout')
        .set('Authorization', `Bearer ${session.refreshToken}`)
        .send();

      expect(res.status).toBe(200);
      expect(res.body).toEqual({ message: 'Successfully logged out.' });

      const refreshAttempt = await request(app.getHttpServer())
        .post('/v1/user/refresh')
        .set('Authorization', `Bearer ${session.refreshToken}`)
        .send();

      expect(refreshAttempt.status).toBe(401);
    });

    it('should return 401 if refresh token is missing', async () => {
      const res = await request(app.getHttpServer()).post('/v1/user/logout').send();

      expect(res.status).toBe(401);
    });

    it('should return 401 if refresh token is invalid', async () => {
      const res = await request(app.getHttpServer())
        .post('/v1/user/logout')
        .set('Authorization', 'Bearer invalid.token.here')
        .send();

      expect(res.status).toBe(401);
    });
  });
});
