import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { CreateTrainingPlanRequestDto } from '@app/training-plan/dto/create-training-plan.dto';
import { TrainingPlanEntity } from '@app/training-plan/entities/training-plan.entity';

export const createAndAssertTrainingPlan = async (
  app: INestApplication,
  payload: CreateTrainingPlanRequestDto,
  accessToken: string,
): Promise<TrainingPlanEntity> => {
  const response = await request(app.getHttpServer())
    .post('/v1/training-plan')
    .set('Authorization', `Bearer ${accessToken}`)
    .send(payload);

  expect(response.status).toBe(201);
  expect(response.body.name).toBe(payload.name);
  expect(response.body.description).toBe(payload.description);
  expect(response.body.trainings).toMatchObject(expect.any(Array));
  expect(response.body.status).toBe('active');
  expect(response.body.createdAt).toBeDefined();
  expect(response.body.updatedAt).toBeDefined();
  expect(response.body.creatorId).toBeDefined();

  return response.body as TrainingPlanEntity;
};

export const deleteAndAssertTrainingPlan = async (app: INestApplication, id: string, accessToken: string) => {
  const response = await request(app.getHttpServer())
    .delete(`/v1/training-plan/${id}`)
    .set('Authorization', `Bearer ${accessToken}`);

  expect(response.status).toBe(200);
  expect(response.body.id).toBe(id);
  expect(response.body.status).toBe('deleted');
};
