import React, { useState } from "react";
import { View } from "react-native";
import type { Meta, StoryObj } from "@storybook/react";
import { InputTextArea, ThemeProvider, Sizes } from "@vs/kit-ui-expo";

// Wrapper component to handle state for the InputTextArea
const InputTextAreaWrapper = (props: React.ComponentProps<typeof InputTextArea>) => {
  const [value, setValue] = useState(props.value || "");
  return <InputTextArea {...props} value={value} onChangeText={setValue} />;
};

// Define a dummy onChangeText function for the stories
const dummyOnChangeText = () => {};

const meta = {
  title: "Components/Inputs/InputTextArea",
  component: InputTextAreaWrapper,
  argTypes: {
    onChangeText: { action: "text changed" },
    size: {
      control: "select",
      options: [Sizes.Small, Sizes.Medium, Sizes.Large],
    },
    disabled: {
      control: "boolean",
    },
    label: {
      control: "text",
    },
    placeholder: {
      control: "text",
    },
    numberOfLines: {
      control: "number",
    },
    maxLength: {
      control: "number",
    },
  },
  args: {
    placeholder: "Wprowadź swój tekst...",
    size: Sizes.Medium,
    disabled: false,
    value: "",
    numberOfLines: 4,
    onChangeText: dummyOnChangeText,
  },
  decorators: [
    Story => (
      <ThemeProvider>
        <View style={{ padding: 16, width: "100%" }}>
          <Story />
        </View>
      </ThemeProvider>
    ),
  ],
} satisfies Meta<typeof InputTextAreaWrapper>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {};

export const WithLabel: Story = {
  args: {
    label: "Opis",
  },
};

export const Small: Story = {
  args: {
    size: Sizes.Small,
    label: "Small TextArea",
    numberOfLines: 3,
  },
};

export const Medium: Story = {
  args: {
    size: Sizes.Medium,
    label: "Medium TextArea",
    numberOfLines: 4,
  },
};

export const Large: Story = {
  args: {
    size: Sizes.Large,
    label: "Large TextArea",
    numberOfLines: 5,
  },
};

export const Disabled: Story = {
  args: {
    disabled: true,
    value: "To jest wyłączona textarea z przykładowym tekstem, który nie może być edytowany.",
    label: "Disabled TextArea",
    numberOfLines: 3,
  },
};

export const WithError: Story = {
  args: {
    error: "Tekst jest za krótki",
    label: "TextArea z błędem",
    value: "Krótki tekst",
  },
};

export const WithMaxLength: Story = {
  args: {
    maxLength: 100,
    label: "Ograniczona długość (100 znaków)",
    placeholder: "Możesz wpisać maksymalnie 100 znaków...",
    numberOfLines: 4,
  },
};

export const TallTextArea: Story = {
  args: {
    numberOfLines: 8,
    label: "Wysoka TextArea",
    placeholder: "To jest bardzo wysoka textarea z 8 liniami...",
  },
};

export const ShortTextArea: Story = {
  args: {
    numberOfLines: 2,
    label: "Niska TextArea",
    placeholder: "Krótka textarea z 2 liniami...",
  },
};

export const PrefilledContent: Story = {
  args: {
    value:
      "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.\n\nUt enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.",
    label: "Wypełniona TextArea",
    numberOfLines: 6,
  },
};

export const CustomStyle: Story = {
  args: {
    inputStyle: {
      backgroundColor: "#2a2a2a",
      borderColor: "#ff5722",
      borderWidth: 2,
      borderRadius: 12,
    },
    labelStyle: { color: "#ff5722", fontWeight: "bold" },
    label: "Custom Styled TextArea",
    numberOfLines: 4,
  },
};

export const CommentBox: Story = {
  args: {
    label: "Komentarz",
    placeholder: "Podziel się swoimi przemyśleniami...",
    numberOfLines: 5,
    maxLength: 500,
  },
};

export const FeedbackForm: Story = {
  args: {
    label: "Opinia o produkcie",
    placeholder: "Opisz swoje doświadczenie z naszym produktem. Twoja opinia pomoże nam się rozwijać...",
    numberOfLines: 6,
    maxLength: 1000,
  },
};
