import React from "react";
import { View } from "react-native";
import type { Meta, StoryObj } from "@storybook/react";
import { ButtonBorder, ThemeProvider, Sizes } from "@vs/kit-ui-expo";

const meta = {
  title: "Components/Buttons/ButtonBorder",
  component: ButtonBorder,
  argTypes: {
    onPress: { action: "pressed the button" },
    size: {
      control: "select",
      options: [Sizes.Small, Sizes.Medium, Sizes.Large],
    },
    isLoading: {
      control: "boolean",
    },
    disabled: {
      control: "boolean",
    },
  },
  args: {
    children: "Basic Button",
    size: Sizes.Medium,
    isLoading: false,
    disabled: false,
  },
  decorators: [
    Story => (
      <ThemeProvider>
        <View style={{ padding: 16, alignItems: "flex-start" }}>
          <Story />
        </View>
      </ThemeProvider>
    ),
  ],
} satisfies Meta<typeof ButtonBorder>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {};

export const Small: Story = {
  args: {
    size: Sizes.Small,
  },
};

export const Medium: Story = {
  args: {
    size: Sizes.Medium,
  },
};

export const Large: Story = {
  args: {
    size: Sizes.Large,
  },
};

export const Loading: Story = {
  args: {
    isLoading: true,
  },
};

export const Disabled: Story = {
  args: {
    disabled: true,
  },
};

export const CustomStyle: Story = {
  args: {
    style: { backgroundColor: "#ff5722", borderRadius: 25 },
    textStyle: { color: "white", fontWeight: "bold" },
  },
};
