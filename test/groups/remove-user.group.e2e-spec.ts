import 'reflect-metadata';
import { INestApplication } from '@nestjs/common';
import request from 'supertest';

import { loginAndAssertUser, registerAndAssertUser } from '../__helpers/auth';
import { buildTestApp } from '../__setup/app.factory';
import { generateTestUser } from '../__helpers/user';
import { ConsentDefinitionEntity } from '@app/consents/entities/consent-definition.entity';
import { createAndAssertConsent, generateGDPRConsentPayload } from '../__helpers/consents';
import { CreateGroupRequestDto } from '@app/groups/dto/create-group.dto';
import { addAndAssertUserToGroup, createAndAssertGroup, getAndAssertGroupById } from '../__helpers/group';
import { randomUUID } from 'crypto';

describe('/DELETE /v1/group/:groupId/user/:userId', () => {
  let app: INestApplication;
  let accessToken: string;
  let consent: ConsentDefinitionEntity;

  beforeAll(async () => {
    app = await buildTestApp();

    const consentPayload = generateGDPRConsentPayload();
    consent = await createAndAssertConsent(app, consentPayload);

    const testUser = generateTestUser([consent.id]);
    await registerAndAssertUser(app, testUser);
    accessToken = (await loginAndAssertUser(app, testUser)).accessToken;
  });

  afterAll(async () => {
    await app.close();
  });

  it('should remove user from group by owner', async () => {
    const payload: CreateGroupRequestDto = {
      name: 'Test Exercise E2E',
      description: 'This is a test exercise',
      tags: 'e2e|test',
    };

    const group = await createAndAssertGroup(app, payload, accessToken);

    const groupMember = generateTestUser([consent.id]);
    const groupMemberUser = await registerAndAssertUser(app, groupMember);

    await addAndAssertUserToGroup(app, group.id, groupMemberUser.id, accessToken);

    const response = await request(app.getHttpServer())
      .delete(`/v1/group/${group.id}/user/${groupMemberUser.id}`)
      .set('Authorization', `Bearer ${accessToken}`);

    expect(response.status).toBe(200);
    expect(response.body.message).toBe('User successfully removed from group');

    //=========================================================
    //          Verify if user is added to group
    //=========================================================
    const groupAfterAddingUser = await getAndAssertGroupById(app, group.id, accessToken);

    expect(groupAfterAddingUser.groupUsers.length).toBe(1); // Only Owner should be left
    expect(groupAfterAddingUser.groupUsers[0].user.id).toBe(group.ownerId);
  });

  it('should remove user from group by user himself', async () => {
    const payload: CreateGroupRequestDto = {
      name: 'Test Exercise E2E',
      description: 'This is a test exercise',
      tags: 'e2e|test',
    };

    const group = await createAndAssertGroup(app, payload, accessToken);

    const groupMember = generateTestUser([consent.id]);
    const groupMemberUser = await registerAndAssertUser(app, groupMember);
    const groupMemberAccessToken = (await loginAndAssertUser(app, groupMember)).accessToken;

    await addAndAssertUserToGroup(app, group.id, groupMemberUser.id, accessToken);

    const response = await request(app.getHttpServer())
      .delete(`/v1/group/${group.id}/user/${groupMemberUser.id}`)
      .set('Authorization', `Bearer ${groupMemberAccessToken}`);

    expect(response.status).toBe(200);
    expect(response.body.message).toBe('User successfully removed from group');

    //=========================================================
    //          Verify if user is added to group
    //=========================================================
    const groupAfterAddingUser = await getAndAssertGroupById(app, group.id, accessToken);

    expect(groupAfterAddingUser.groupUsers.length).toBe(1); // Only Owner should be left
    expect(groupAfterAddingUser.groupUsers[0].user.id).toBe(group.ownerId);
  });

  it('should return 401 when user is not authenticated', async () => {
    const payload: CreateGroupRequestDto = {
      name: 'Test Exercise E2E',
      description: 'This is a test exercise',
      tags: 'e2e|test',
    };

    const group = await createAndAssertGroup(app, payload, accessToken);

    const groupMember = generateTestUser([consent.id]);
    const groupMemberUser = await registerAndAssertUser(app, groupMember);

    const response = await request(app.getHttpServer()).delete(`/v1/group/${group.id}/user/${groupMemberUser.id}`);

    expect(response.status).toBe(401);
    expect(response.body.message).toBe('Unauthorized');
  });

  it('should return 404 if member does not exist in group', async () => {
    const payload: CreateGroupRequestDto = {
      name: 'Test Exercise E2E',
      description: 'This is a test exercise',
      tags: 'e2e|test',
    };

    const group = await createAndAssertGroup(app, payload, accessToken);

    const groupMember = generateTestUser([consent.id]);
    const groupMemberUser = await registerAndAssertUser(app, groupMember);
    await addAndAssertUserToGroup(app, group.id, groupMemberUser.id, accessToken);

    await request(app.getHttpServer())
      .delete(`/v1/group/${group.id}/user/${groupMemberUser.id}`)
      .set('Authorization', `Bearer ${accessToken}`);

    const response = await request(app.getHttpServer())
      .delete(`/v1/group/${group.id}/user/${groupMemberUser.id}`)
      .set('Authorization', `Bearer ${accessToken}`);

    expect(response.status).toBe(404);
    expect(response.body.message).toBe(`User with id ${groupMemberUser.id} is not a member of this group`);
  });

  it('should return 404 if group not found', async () => {
    const id = randomUUID();

    const groupMember = generateTestUser([consent.id]);
    const groupMemberUser = await registerAndAssertUser(app, groupMember);

    const response = await request(app.getHttpServer())
      .delete(`/v1/group/${id}/user/${groupMemberUser.id}`)
      .set('Authorization', `Bearer ${accessToken}`);

    expect(response.status).toBe(404);
    expect(response.body.message).toBe(`Group with id ${id} not found`);
  });
});
