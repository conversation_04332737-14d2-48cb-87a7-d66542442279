import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsUUID, ArrayNotEmpty } from 'class-validator';

export class AcceptConsentDto {
  @ApiProperty({
    description: 'ID of the user accepting the consents',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  user: string;

  @ApiProperty({
    description: 'List of consent IDs accepted by the user',
    example: ['223e4567-e89b-12d3-a456-************', '323e4567-e89b-12d3-a456-************'],
  })
  @IsArray()
  @ArrayNotEmpty()
  @IsUUID('all', { each: true })
  consentIds: string[];
}
