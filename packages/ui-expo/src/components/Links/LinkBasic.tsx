import React, { forwardRef } from "react";
import {
  Text,
  StyleSheet,
  StyleProp,
  ViewStyle,
  TextStyle,
  View,
} from "react-native";
import { LoaderSpinnerSmall } from "../Loaders";
import { Sizes, buttonSizeStyles } from "../../utils/sizes";
import { useTheme } from "../../theme";
import { Link } from "expo-router";

interface Props {
  children?: React.ReactNode;
  size?: Sizes;
  isLoading?: boolean;
  href: string;
  disabled?: boolean;
  style?: StyleProp<ViewStyle>;
  textStyle?: StyleProp<TextStyle>;
}

export const LinkBasic = forwardRef<View, Props>((props, ref) => {
  const {
    children,
    isLoading,
    size = Sizes.Medium,
    href,
    disabled,
    style,
    textStyle,
  } = props;

  const { theme } = useTheme();
  const sizeStyle = buttonSizeStyles[size];

  return (
    <View ref={ref} style={{ width: '100%' }}>
      <Link
        href={href}
        style={[
          styles.button,
          sizeStyle,
          {
            backgroundColor: disabled
              ? theme.colors.grey[400]
              : theme.colors.accent[300],
            borderRadius: theme.borderRadius.lg,
          },
          style,
        ]}
        disabled={disabled || isLoading}
      >
        {isLoading ? (
          <LoaderSpinnerSmall />
        ) : (
          <Text
            style={[
              styles.text,
              {
                color: disabled ? theme.colors.grey[500] : theme.colors.grey[900],
              },
              textStyle,
            ]}
          >
            {children || "Click"}
          </Text>
        )}
      </Link>
    </View>
  );
});

const styles = StyleSheet.create({
  button: {
    paddingHorizontal: 24,
    alignItems: "center",
    justifyContent: "center",
  },
  text: {
    fontSize: 16,
    textAlign: "center",
    width: "100%",
  },
});
