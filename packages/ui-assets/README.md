# React SVG icons library boilerplate

The boilerplate to create a standalone React icons library from your SVG files.

## Resource

[dev.to](https://dev.to/quanpham/create-your-own-react-icons-library-and-publish-to-npm-automatically-4i11)

## Installation

```bash
# Using npm
npm install @empe/front-kit-assets

# Using yarn
yarn add @empe/front-kit-assets
```

## Usage

### For Web (React, Next.js, Expo Web)

```jsx
import { SomeIcon } from '@empe/front-kit-assets';

function App() {
  return (
    <div>
      {/* You can use color, fill, stroke and other SVG properties */}
      <SomeIcon color="#FF0000" width={24} height={24} />
      <SomeIcon fill="#00FF00" stroke="#0000FF" width={32} height={32} />
    </div>
  );
}
```

### For React Native (iOS, Android)

For React Native, you need to install the `react-native-svg` package:

```bash
# Using npm
npm install react-native-svg

# Using yarn
yarn add react-native-svg
```

Then you can use the icons in your React Native components:

```jsx
import { SomeIcon } from '@empe/front-kit-assets';

function App() {
  return (
    <View>
      {/* You can use color, fill, stroke and other SVG properties */}
      <SomeIcon color="#FF0000" width={24} height={24} />
      <SomeIcon fill="#00FF00" stroke="#0000FF" width={32} height={32} />
    </View>
  );
}
```

The library will automatically use the correct implementation based on your platform.

## Supported Properties

All icons support standard SVG properties, including:

- `width` and `height`: Control the size of the icon
- `color`: Set the primary color (usually affects fill or stroke depending on the icon)
- `fill`: Explicitly set the fill color
- `stroke`: Set the stroke color
- `strokeWidth`: Control the thickness of strokes

## Adding New Icons

1. Place your SVG files in the `raw` directory
2. Run the build command:

```bash
yarn build
```

This will optimize the SVGs and generate the React and React Native components.

## License

MIT
