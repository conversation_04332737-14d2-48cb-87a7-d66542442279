import 'reflect-metadata';
import { INestApplication } from '@nestjs/common';
import request from 'supertest';

import { loginAndAssertUser, registerAndAssertUser } from '../__helpers/auth';
import { buildTestApp } from '../__setup/app.factory';
import { generateTestUser } from '../__helpers/user';
import { CreateTrainingRequestDto } from '@app/training/dto/create-training.dto';
import { createAndAssertTraining, deleteAndAssertTraining } from '../__helpers/training';
import { ConsentDefinitionEntity } from '@app/consents/entities/consent-definition.entity';
import { createAndAssertConsent, generateGDPRConsentPayload } from '../__helpers/consents';

describe('/GET /v1/training', () => {
  let app: INestApplication;
  let accessToken: string;
  let consent: ConsentDefinitionEntity;

  beforeAll(async () => {
    app = await buildTestApp();

    const consentPayload = generateGDPRConsentPayload();
    consent = await createAndAssertConsent(app, consentPayload);

    const testUser = generateTestUser([consent.id]);
    await registerAndAssertUser(app, testUser);
    accessToken = (await loginAndAssertUser(app, testUser)).accessToken;
  });

  afterAll(async () => {
    await app.close();
  });

  it('should list trainings by current user', async () => {
    const payload: CreateTrainingRequestDto = {
      name: 'Test training name',
      description: 'Test training description',
      exercises: [],
      tags: '',
    };

    const trainingsCount = 5;

    for (let i = 0; i < trainingsCount; i++) {
      await createAndAssertTraining(app, payload, accessToken);
    }

    const response = await request(app.getHttpServer())
      .get('/v1/training')
      .set('Authorization', `Bearer ${accessToken}`);

    expect(response.status).toBe(200);
    expect(response.body.length).toBe(trainingsCount);
    expect(response.body[0].id).toBeDefined();
    expect(response.body[0].name).toBe(payload.name);
    expect(response.body[0].description).toBe(payload.description);
    expect(response.body[0].tags).toBe(payload.tags);
    expect(response.body[0].status).toBe('active');
  });

  it('should filter trainings by status DELETED', async () => {
    const payload: CreateTrainingRequestDto = {
      name: 'Test training name',
      description: 'Test training description',
      exercises: [],
      tags: '',
    };

    const trainingsCount = 5;

    for (let i = 0; i < trainingsCount; i++) {
      await createAndAssertTraining(app, payload, accessToken);
    }

    const deletedTraining = await createAndAssertTraining(app, payload, accessToken);
    await deleteAndAssertTraining(app, deletedTraining.id, accessToken);

    const response = await request(app.getHttpServer())
      .get(`/v1/training?status=deleted`)
      .set('Authorization', `Bearer ${accessToken}`);

    expect(response.status).toBe(200);
    expect(response.body.length).toBe(1);
    expect(response.body[0].id).toBeDefined();
    expect(response.body[0].name).toBe(payload.name);
    expect(response.body[0].description).toBe(payload.description);
    expect(response.body[0].tags).toBe(payload.tags);
    expect(response.body[0].status).toBe('deleted');
  });

  it('should return 401 if user is not authenticated', async () => {
    const payload: CreateTrainingRequestDto = {
      name: 'Test training name',
      description: 'Test training description',
      exercises: [],
      tags: '',
    };

    await createAndAssertTraining(app, payload, accessToken);

    const response = await request(app.getHttpServer()).get('/v1/training');

    expect(response.status).toBe(401);
    expect(response.body.message).toBe('Unauthorized');
  });
});

describe('/GET /v1/training/:id', () => {
  let app: INestApplication;
  let accessToken: string;
  let consent: ConsentDefinitionEntity;

  beforeAll(async () => {
    app = await buildTestApp();

    const consentPayload = generateGDPRConsentPayload();
    consent = await createAndAssertConsent(app, consentPayload);

    const testUser = generateTestUser([consent.id]);
    await registerAndAssertUser(app, testUser);
    accessToken = (await loginAndAssertUser(app, testUser)).accessToken;
  });

  afterAll(async () => {
    await app.close();
  });

  it('should return a single training', async () => {
    const payload: CreateTrainingRequestDto = {
      name: 'Test training name',
      description: 'Test training description',
      exercises: [],
      tags: '',
    };

    const training = await createAndAssertTraining(app, payload, accessToken);

    const response = await request(app.getHttpServer())
      .get(`/v1/training/${training.id}`)
      .set('Authorization', `Bearer ${accessToken}`);

    expect(response.status).toBe(200);
    expect(response.body.id).toBe(training.id);
    expect(response.body.name).toBe(payload.name);
    expect(response.body.description).toBe(payload.description);
    expect(response.body.tags).toBe(payload.tags);
    expect(response.body.status).toBe('active');
  });

  it('should return 403 if accessing training of another user', async () => {
    const payload: CreateTrainingRequestDto = {
      name: 'Test training name',
      description: 'Test training description',
      exercises: [],
      tags: '',
    };

    const anotherUser = generateTestUser([consent.id]);

    await registerAndAssertUser(app, anotherUser);
    const anotherUserAccessToken = (await loginAndAssertUser(app, anotherUser)).accessToken;

    const training = await createAndAssertTraining(app, payload, accessToken);

    const response = await request(app.getHttpServer())
      .get(`/v1/training/${training.id}`)
      .set('Authorization', `Bearer ${anotherUserAccessToken}`);

    expect(response.status).toBe(403);
    expect(response.body.message).toBe('You do not have permission to access this training');
    expect(response.body.error).toBe('Forbidden');
    expect(response.body.statusCode).toBe(403);
  });
});
