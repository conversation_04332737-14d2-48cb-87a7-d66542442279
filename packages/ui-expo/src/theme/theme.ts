export const colors = {
  grey: {
    50: "#eeeeee",
    100: "#cacaca",
    200: "#b0b0b0",
    300: "#8c8c8c",
    400: "#767676",
    500: "#545454",
    600: "#3c3c3c",
    700: "#1f1f1f",
    800: "#181818",
    900: "#121212",
  },
  accent: {
    50: "#f3f9ec",
    100: "#edf6e3",
    200: "#daedc5",
    300: "#87c543",
    400: "#7ab13c",
    500: "#6c9e36",
    600: "#659432",
    700: "#517628",
    800: "#3d591e",
    900: "#2f4517",
  },
  alert: {
    50: "#f8e9e8",
    100: "#e8bbb7",
    200: "#dd9a95",
    300: "#cd6c64",
    400: "#c34f46",
    500: "#b42318",
    600: "#a42016",
    700: "#801911",
    800: "#63130d",
    900: "#4c0f0a",
  },
  warning: {
    50: "#fef4e6",
    100: "#fdddb3",
    200: "#fbcc8e",
    300: "#fab55a",
    400: "#f9a63a",
    500: "#f79009",
    600: "#e18308",
    700: "#af6606",
    800: "#884f05",
    900: "#683c04",
  },
  success: {
    50: "#e6f6ec",
    100: "#b0e2c3",
    200: "#8ad5a7",
    300: "#54c17e",
    400: "#33b565",
    500: "#00a33f",
    600: "#009439",
    700: "#00742d",
    800: "#005a23",
    900: "#00441a",
  },
  transparent: "transparent",
};

export const spacing = {
  1: 4,
  2: 8,
  3: 12,
  4: 16,
  5: 20,
  6: 24,
  8: 32,
  10: 40,
  12: 48,
  16: 64,
  20: 80,
  24: 96,
};

export const borderRadius = {
  none: 0,
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  "2xl": 24,
  full: 9999,
};

export const typography = {
  Display1: {
    fontSize: 64,
    lineHeight: 82,
    fontWeight: "600",
  },
  Heading1: {
    fontSize: 48,
    lineHeight: 62,
    fontWeight: "600",
  },
  Heading2: {
    fontSize: 40,
    lineHeight: 48,
    fontWeight: "600",
  },
  Heading3: {
    fontSize: 32,
    lineHeight: 38,
    fontWeight: "600",
  },
  Heading4: {
    fontSize: 24,
    lineHeight: 31,
    fontWeight: "600",
  },
  Heading5: {
    fontSize: 20,
    lineHeight: 26,
    fontWeight: "600",
  },
  BodyL: {
    fontSize: 18,
    lineHeight: 22,
    fontWeight: "300",
  },
  BodyM: {
    fontSize: 16,
    lineHeight: 20,
    fontWeight: "300",
  },
  BodyS: {
    fontSize: 14,
    lineHeight: 18,
    fontWeight: "300",
  },
  BodyXS: {
    fontSize: 12,
    lineHeight: 16,
    fontWeight: "300",
  },
  SubtitleL: {
    fontSize: 18,
    lineHeight: 24,
    fontWeight: "500",
  },
  SubtitleM: {
    fontSize: 16,
    lineHeight: 20,
    fontWeight: "500",
  },
  SubtitleS: {
    fontSize: 14,
    lineHeight: 18,
    fontWeight: "500",
  },
  SubtitleXS: {
    fontSize: 12,
    lineHeight: 16,
    fontWeight: "500",
  },
  Link: {
    fontSize: 14,
    lineHeight: 16,
    fontWeight: "300",
  },
};

export const fontFamily = {
  outfit: "Outfit",
  sans: "Outfit",
};

export const fontWeight = {
  light: "300",
  normal: "400",
  medium: "500",
  semibold: "600",
  bold: "700",
};

export const lineHeight = {
  none: 1,
  tight: 1.25,
  snug: 1.375,
  normal: 1.5,
  relaxed: 1.625,
  loose: 2,
};

export const theme = {
  colors,
  spacing,
  borderRadius,
  typography,
  fontFamily,
  fontWeight,
  lineHeight,
};

export type Theme = typeof theme;
