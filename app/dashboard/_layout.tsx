import { Tabs } from "expo-router";
import React from "react";
import {
  DietitianIcon,
  HomeAccentIcon,
  SettingsIcon,
  TrainerIcon,
  UserIcon,
} from "@/components/NavbarConditionalIcons";
import { useNavbarScreenOptions } from "@/hooks/useNavbarScreenOptions";

export enum UserPathEnum {
  HOME = "index",
  DIET = "diet",
  ACTIVITY = "activity",
  SETTINGS = "settings",
  PROFILE = "profile",
}

function RoleBasedMenu() {
  const { screenOptions } = useNavbarScreenOptions();

  const tabsConfig = [
    { name: UserPathEnum.ACTIVITY, icon: TrainerIcon },
    { name: UserPathEnum.DIET, icon: DietitianIcon },
    { name: UserPathEnum.HOME, title: "Home", icon: HomeAccentIcon },
    { name: UserPathEnum.SETTINGS, icon: SettingsIcon },
    { name: UserPathEnum.PROFILE, title: "User", icon: UserIcon },
  ];

  return (
    <Tabs initialRouteName="index" screenOptions={screenOptions}>
      {tabsConfig.map((tab) => (
        <Tabs.Screen
          key={tab.name}
          name={tab.name}
          options={{
            title: tab.title,
            tabBarIcon: tab.icon,
          }}
        />
      ))}
    </Tabs>
  );
}

export default function UserLayout() {
  return <RoleBasedMenu />;
}
