import React, { ReactNode } from "react";
import { Text, StyleSheet, View } from "react-native";

interface Props {
  children: ReactNode;
  code: string | null;
}

export const Code = ({ children, code }: Props) => {
  return (
    <View style={styles.view}>
      <Text style={styles.code}>
        {code ?? "Zeskanuj kod QR aby dołączyć do grupy"}
      </Text>
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  view: {
    padding: 16,
    gap: 16,
    alignItems: "center",
  },
  code: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#f9fafb",
    textAlign: "center",
  },
});
