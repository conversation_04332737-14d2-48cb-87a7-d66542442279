export enum PathEnum {
  // Auth routes (group routes)
  LOGIN = "/login",
  REGISTER = "/register",
  CONSENTS_CONTENT = "/consentConent", // Dynamic route: /consentConent/[id]
  ONBOARDING = "/onboarding",
  QR_SCANNER = "/qr-scanner",
  EMAIL_OTC = "/email-otc",

  // Main routes
  HOME = "/",

  // Dashboard routes
  DASHBOARD = "/dashboard",
  SETTINGS = "/dashboard/settings",
  PROFILE = "/dashboard/profile",

  // Training routes
  ACTIVITY = "/dashboard/activity",
  EXERCISE = "/dashboard/activity/exercise",
  EXERCISE_NEW = "/dashboard/activity/exercise/[id]",
  TRAINING = "/dashboard/activity/training",
  TRAINING_NEW = "/dashboard/activity/training/[id]",
  TRAINING_PLAN = "/dashboard/activity/training-plan",
  TRAINING_PLAN_NEW = "/dashboard/activity/training-plan/[id]",
  // Note: matches file name spelling

  // Diet routes
  DIET = "/dashboard/diet",
}
