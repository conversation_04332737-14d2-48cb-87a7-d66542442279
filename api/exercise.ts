import {
  <PERSON><PERSON>r<PERSON>ele<PERSON>Exerc<PERSON>,
  <PERSON><PERSON>r<PERSON>et<PERSON><PERSON><PERSON><PERSON>,
  <PERSON>rror<PERSON>ost<PERSON>xerc<PERSON>,
  <PERSON>rrorPutExercise,
} from "@/errors";
import axiosInstance from "@/middleware/axiosInstance";
import {
  DeleteExerciseResponse,
  NewExerciseRequestPayload,
  NewExerciseResponse,
} from "@/types/exercise";

export const getAllExercises = async (): Promise<NewExerciseResponse[]> => {
  try {
    const response = await axiosInstance.get(`/exercise`);
    return response.data;
  } catch (error) {
    throw new ErrorGetExercise(error);
  }
};

export const postNewExercise = async (
  payload: NewExerciseRequestPayload
): Promise<NewExerciseResponse> => {
  try {
    const response = await axiosInstance.post("/exercise", payload);
    return response.data;
  } catch (error) {
    throw new ErrorPostExercise(error);
  }
};

export const putNewExercise = async (
  payload: NewExerciseRequestPayload,
  id: string
): Promise<NewExerciseResponse> => {
  try {
    const response = await axiosInstance.put(`/exercise/${id}`, payload);
    return response.data;
  } catch (error) {
    throw new ErrorPutExercise(error);
  }
};

export const deleteExercise = async (
  id: string
): Promise<DeleteExerciseResponse> => {
  try {
    const response = await axiosInstance.delete(`/exercise/${id}`);
    return response.data;
  } catch (error) {
    throw new ErrorDeleteExercise(error);
  }
};
