import React, { useRef, useState } from "react";
import {
  View,
  TextInput,
  StyleSheet,
  StyleProp,
  ViewStyle,
  TextStyle,
  Text,
  NativeSyntheticEvent,
  TextInputKeyPressEventData,
} from "react-native";
import { useTheme } from "../../theme";

interface InputPinProps {
  value: string;
  onChange: (value: string) => void;
  length?: number;
  label?: string;
  disabled?: boolean;
  containerStyle?: StyleProp<ViewStyle>;
  inputStyle?: StyleProp<TextStyle>;
  labelStyle?: StyleProp<TextStyle>;
  error?: string;
}

export const InputPin = ({
  value = "",
  onChange,
  length = 4,
  label,
  disabled = false,
  containerStyle,
  inputStyle,
  labelStyle,
  error,
}: InputPinProps) => {
  const { theme } = useTheme();
  const inputRefs = useRef<Array<TextInput | null>>([]);
  const [isFocused, setIsFocused] = useState<number[]>([]);

  // Initialize the array of refs based on the length
  if (inputRefs.current.length !== length) {
    inputRefs.current = Array(length).fill(null);
  }

  const handleChangeText = (text: string, index: number) => {
    // Handle pasted text (if multiple characters are detected)
    if (text.length > 1) {
      // Filter out non-numeric characters
      const numericText = text.replace(/[^0-9]/g, "");

      // Only use up to the required length
      const usableText = numericText.substring(0, length - index);

      if (usableText.length > 0) {
        // Create a new value with the pasted text
        const newValue = value.split("");
        for (let i = 0; i < usableText.length && index + i < length; i++) {
          newValue[index + i] = usableText[i];
        }

        onChange(newValue.join(""));

        // Focus the next input after the pasted text
        const nextIndex = Math.min(index + usableText.length, length - 1);
        if (nextIndex < length) {
          inputRefs.current[nextIndex]?.focus();
        }
        return;
      }
    }

    // Handle single character input
    // Only allow a single digit
    const singleChar = text.charAt(text.length - 1);

    // Only allow numbers
    if (text && !/^[0-9]$/.test(singleChar)) {
      return;
    }

    // Update the value
    const newValue = value.split("");
    newValue[index] = singleChar;
    onChange(newValue.join(""));

    // Move to next input if there's a value
    if (singleChar && index < length - 1) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handleKeyPress = (e: NativeSyntheticEvent<TextInputKeyPressEventData>, index: number) => {
    // If backspace is pressed and the input is empty, move to the previous input
    if (e.nativeEvent.key === "Backspace" && !value[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handleFocus = (index: number) => {
    setIsFocused(prev => [...prev, index]);
  };

  const handleBlur = (index: number) => {
    setIsFocused(prev => prev.filter(i => i !== index));
  };

  return (
    <View style={[styles.container, containerStyle]}>
      {label && (
        <Text
          style={[
            styles.label,
            {
              color: theme.colors.grey[50],
            },
            labelStyle,
          ]}
        >
          {label}
        </Text>
      )}
      <View style={styles.inputContainer}>
        {Array(length)
          .fill(0)
          .map((_, index) => (
            <TextInput
              key={index}
              ref={ref => (inputRefs.current[index] = ref)}
              value={value[index] || ""}
              onChangeText={text => handleChangeText(text, index)}
              onKeyPress={e => handleKeyPress(e, index)}
              onFocus={() => handleFocus(index)}
              onBlur={() => handleBlur(index)}
              style={[
                styles.input,
                {
                  backgroundColor: theme.colors.grey[900],
                  color: theme.colors.grey[50],
                  borderRadius: theme.borderRadius.lg,
                  borderColor: isFocused.includes(index) ? theme.colors.accent[400] : theme.colors.grey[500],
                },
                disabled && { opacity: 0.7 },
                inputStyle,
              ]}
              keyboardType="numeric"
              maxLength={10} // Allow pasting multiple digits
              editable={!disabled}
              selectTextOnFocus
            />
          ))}
      </View>
      {error && (
        <Text
          style={[
            styles.error,
            {
              color: theme.colors.alert[400],
            },
          ]}
        >
          {error}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: "100%",
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
    fontWeight: "500",
  },
  inputContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
  },
  input: {
    width: 70,
    height: 70,
    fontSize: 24,
    borderWidth: 1,
    textAlign: "center",
    fontWeight: "500",
  },
  error: {
    fontSize: 14,
    marginTop: 8,
  },
});
