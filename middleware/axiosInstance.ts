import { buildApiAdress } from "@/utils/buildApiAdress";
import { useAuthStore } from "@/stores/useAuthStore";
import axios from "axios";

console.log("API:", buildApiAdress());

// Ś<PERSON>ż<PERSON>, które nie wymagają bearer token
const excludePaths = [
  "/auth/login",
  "/auth/register",
  "/auth/refresh",
  "/auth/forgot-password",
  "/auth/reset-password",
  "/auth/verify-email",
  "/public",
  "/health",
];
export const axiosInstance = axios.create({
  baseURL: buildApiAdress(),
  timeout: 10000,
  headers: {
    "Content-Type": "application/json",
  },
});

// Funkcja pomocnicza do sprawdzenia czy ścieżka jest wykluczona
const shouldExcludeAuth = (url: string): boolean => {
  return excludePaths.some((path) => url.includes(path));
};

axiosInstance.interceptors.request.use(function (config) {
  // Pobieramy aktualny stan z zustand store
  const { credentials, isAuthenticated } = useAuthStore.getState();

  // Logujemy podstawowe informacje o request
  console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`);

  // Sprawdzamy czy ścieżka jest wykluczona z autoryzacji
  const requestUrl = config.url || "";
  const isExcluded = shouldExcludeAuth(requestUrl);

  if (isExcluded) {
    console.log(`🔓 Skipping auth for excluded path: ${requestUrl}`);
  } else if (isAuthenticated && credentials?.accessToken) {
    // Dodajemy bearer token do nagłówka
    config.headers = config.headers || {};
    config.headers.Authorization = `Bearer ${credentials.accessToken}`;
    console.log(`🔐 Adding bearer token for: ${requestUrl}`);
  } else {
    console.log(`⚠️  No auth token available for: ${requestUrl}`);
  }

  return config;
});

axiosInstance.interceptors.response.use(
  function (response) {
    // Logujemy udane odpowiedzi
    console.log(
      `✅ API Response: ${
        response.status
      } ${response.config.method?.toUpperCase()} ${response.config.url}`
    );
    return response;
  },
  function (error) {
    // Logujemy błędy
    const status = error.response?.status;
    const url = error.config?.url;
    const method = error.config?.method?.toUpperCase();

    console.log(`❌ API Error: ${status} ${method} ${url}`);

    // Jeśli token wygasł (401), możemy wyczyścić credentials
    if (status === 401) {
      const { clearCredentials } = useAuthStore.getState();
      console.log("🔄 Token expired, clearing credentials");
      clearCredentials();
    }

    return Promise.reject(error);
  }
);

export default axiosInstance;
