import { defineConfig } from "tsup";

export default defineConfig(options => ({
  entryPoints: ["src/**/*.tsx", "src/**/*.ts"],
  format: ["cjs", "esm"],
  dts: true,
  external: ["react"],
  ...options,
}));

// import { defineConfig } from "tsup";

// export default defineConfig({
//   entryPoints: ["src/**/*.tsx", "src/**/*.ts", "src/**/*.svg"],
//   format: ["cjs", "esm"],
//   dts: true,
//   external: ["react"],
// });
