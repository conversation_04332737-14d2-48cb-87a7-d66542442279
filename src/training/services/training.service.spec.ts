import { Test, TestingModule } from '@nestjs/testing';
import { TrainingService } from './training.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { TrainingEntity } from '../entities/training.entity';
import { ExerciseEntity } from '../../exercises/entities/exercise.entity';
import { CreateTrainingRequestDto } from '../dto/create-training.dto';
import { UserEntity } from '../../users/entities/user.entity';
import { TrainingStatus } from '../enums/training-status.enum';
import { BadRequestException, ForbiddenException, NotFoundException } from '@nestjs/common';
import { UpdateTrainingRequestDto } from '../dto/update-training.dto';

describe('TrainingService', () => {
  let service: TrainingService;

  const mockUser = {
    id: 'user-id',
    email: '<EMAIL>',
    name: 'Test User',
    password: 'hashedPassword',
    consents: ['RODO'],
    roles: ['trainer'],
    createdAt: new Date(),
  } as unknown as UserEntity;

  const mockExercises = [
    { id: 'exercise_id_1', name: 'Exercise 1', creatorId: 'user-id' },
    { id: 'exercise_id_2', name: 'Exercise 2', creatorId: 'user-id' },
  ] as ExerciseEntity[];

  const mockTraining = {
    id: 'training_id',
    name: 'Trening na biceps',
    description: 'Trening opis super ciało w 2godziny',
    exercises: mockExercises,
    status: TrainingStatus.ACTIVE,
    creatorId: 'user-id',
    creator: mockUser,
    createdAt: new Date(),
    updatedAt: new Date(),
  } as TrainingEntity;

  // Create a mock query builder that we can reference directly in tests
  const mockQueryBuilder = {
    leftJoinAndSelect: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    getMany: jest.fn().mockResolvedValue([]),
  };

  const mockTrainingRepository = {
    create: jest.fn(),
    save: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    createQueryBuilder: jest.fn(() => mockQueryBuilder),
  };

  const mockExerciseRepository = {
    find: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TrainingService,
        {
          provide: getRepositoryToken(TrainingEntity),
          useValue: mockTrainingRepository,
        },
        {
          provide: getRepositoryToken(ExerciseEntity),
          useValue: mockExerciseRepository,
        },
      ],
    }).compile();

    service = module.get<TrainingService>(TrainingService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createTraining', () => {
    it('should create a training successfully', async () => {
      const createTrainingDto: CreateTrainingRequestDto = {
        name: 'Trening na biceps',
        description: 'Trening opis super ciało w 2godziny',
        exercises: ['exercise_id_1', 'exercise_id_2'],
      };

      mockExerciseRepository.find.mockResolvedValue(mockExercises);
      mockTrainingRepository.create.mockReturnValue(mockTraining);
      mockTrainingRepository.save.mockResolvedValue(mockTraining);

      const result = await service.createTraining(createTrainingDto, mockUser);

      expect(mockExerciseRepository.find).toHaveBeenCalledWith({
        where: {
          id: expect.any(Object),
          creatorId: mockUser.id,
        },
      });
      expect(mockTrainingRepository.create).toHaveBeenCalled();
      expect(mockTrainingRepository.save).toHaveBeenCalled();
      expect(result).toEqual(mockTraining);
    });

    it('should throw BadRequestException if exercises do not exist or do not belong to user', async () => {
      const createTrainingDto: CreateTrainingRequestDto = {
        name: 'Trening na biceps',
        description: 'Trening opis super ciało w 2godziny',
        exercises: ['exercise_id_1', 'exercise_id_2'],
      };

      // Return only one exercise to simulate missing exercise
      mockExerciseRepository.find.mockResolvedValue([mockExercises[0]]);

      await expect(service.createTraining(createTrainingDto, mockUser)).rejects.toThrow(BadRequestException);
    });
  });

  describe('findAll', () => {
    beforeEach(() => {
      // Reset mock calls before each test
      mockQueryBuilder.where.mockClear();
      mockQueryBuilder.andWhere.mockClear();
      mockQueryBuilder.getMany.mockClear();
    });

    it('should return all trainings for a user', async () => {
      mockQueryBuilder.getMany.mockResolvedValue([mockTraining]);

      const result = await service.findAll(mockUser);

      expect(mockQueryBuilder.where).toHaveBeenCalledWith('training.creatorId = :creatorId', {
        creatorId: mockUser.id,
      });
      expect(result).toEqual([mockTraining]);
    });

    it('should filter trainings by status', async () => {
      mockQueryBuilder.getMany.mockResolvedValue([mockTraining]);

      const result = await service.findAll(mockUser, TrainingStatus.ACTIVE);

      expect(mockQueryBuilder.where).toHaveBeenCalledWith('training.creatorId = :creatorId', {
        creatorId: mockUser.id,
      });
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('training.status = :status', {
        status: TrainingStatus.ACTIVE,
      });
      expect(result).toEqual([mockTraining]);
    });
  });

  describe('findOne', () => {
    it('should return a training by id', async () => {
      mockTrainingRepository.findOne.mockResolvedValue(mockTraining);

      const result = await service.findOne('training_id', mockUser);

      expect(mockTrainingRepository.findOne).toHaveBeenCalledWith({
        where: { id: 'training_id' },
        relations: ['exercises'],
      });
      expect(result).toEqual(mockTraining);
    });

    it('should throw NotFoundException if training not found', async () => {
      mockTrainingRepository.findOne.mockResolvedValue(null);

      await expect(service.findOne('non-existent-id', mockUser)).rejects.toThrow(NotFoundException);
    });

    it('should throw ForbiddenException if user does not own the training', async () => {
      const otherUserTraining = { ...mockTraining, creatorId: 'other-user-id' };
      mockTrainingRepository.findOne.mockResolvedValue(otherUserTraining);

      await expect(service.findOne('training_id', mockUser)).rejects.toThrow(ForbiddenException);
    });
  });

  describe('update', () => {
    beforeEach(() => {
      // Reset mock calls before each test
      mockTrainingRepository.findOne.mockClear();
      mockTrainingRepository.save.mockClear();
    });

    it('should update a training successfully', async () => {
      const updateTrainingDto: UpdateTrainingRequestDto = {
        name: 'Updated Training Name',
      };

      mockTrainingRepository.findOne
        .mockResolvedValueOnce(mockTraining) // For permission check
        .mockResolvedValueOnce(mockTraining); // For actual update

      const updatedTraining = {
        ...mockTraining,
        name: 'Updated Training Name',
      };
      mockTrainingRepository.save.mockResolvedValue(updatedTraining);

      const result = await service.update('training_id', updateTrainingDto, mockUser);

      // Verify findOne was called exactly twice
      expect(mockTrainingRepository.findOne).toHaveBeenCalledTimes(2);
      expect(mockTrainingRepository.save).toHaveBeenCalled();
      expect(result.name).toBe('Updated Training Name');
    });
  });

  describe('remove', () => {
    it('should soft delete a training', async () => {
      mockTrainingRepository.findOne.mockResolvedValue(mockTraining);

      const result = await service.remove('training_id', mockUser);

      expect(mockTrainingRepository.update).toHaveBeenCalledWith('training_id', { status: TrainingStatus.DELETED });
      expect(result).toEqual({ status: TrainingStatus.DELETED });
    });
  });
});
