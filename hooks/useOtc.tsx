import React, { useState } from "react";
import { ToastSuccess, useToast } from "@vs/kit-ui-expo";
import { useTranslation } from "react-i18next";
import { useErrorHandling } from "@/hooks/useErrorHandling";
import { useRouter } from "expo-router";

export const useOtc = () => {
  const { t } = useTranslation();
  const router = useRouter();
  const { withErrorHandling } = useErrorHandling();
  const { showToast } = useToast();
  const [value, setValue] = useState("");

  const handleOtcVerifire = () =>
    withErrorHandling(async () => {
      //TODO: Implement OTC verification process
      showToast(<ToastSuccess title={t("email_otc_modal_successs_message")} />);
      //TODO: Add auto login
      router.replace("/login");
    });

  return {
    value,
    setValue,
    handleOtcVerifire,
  };
};
