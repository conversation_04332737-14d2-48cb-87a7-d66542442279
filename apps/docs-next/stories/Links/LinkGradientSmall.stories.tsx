import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { LinkGradientSmall } from "@vs/kit-ui-next";

const meta: Meta<typeof LinkGradientSmall> = {
  title: "Components/Links/LinkGradientSmall",
  component: LinkGradientSmall,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof LinkGradientSmall>;

export const Default: Story = {
  args: {
    href: "#",
    children: "Click me",
  },
};

export const WithCustomText: Story = {
  args: {
    href: "#",
    children: "Custom Link Text",
  },
};

export const AsExternalLink: Story = {
  args: {
    href: "https://example.com",
    target: "_blank",
    rel: "noopener noreferrer",
    children: "External Link",
  },
};

export const Disabled: Story = {
  args: {
    href: "#",
    children: "Disabled Link",
    onClick: e => e.preventDefault(),
    className:
      "item-gradient disabled:opacity-50 disabled:cursor-not-allowed w-full text-center whitespace-nowrap text-white px-8 rounded-xl disabled",
  },
};
